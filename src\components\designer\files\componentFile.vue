<template>
	<div class="content file-box">componentFile</div>
</template>

<script lang="ts" setup>
	import {reactive,ref,onMounted} from 'vue'
	import { useFileBase } from '../../../common/fileBasic';
	const fileBase = useFileBase()
	const props = defineProps({
		fileItem:{
			type:Object,
			default:{}
		}
	})
	
	onMounted(()=>{
	     props.fileItem.func.saveData = saveData
	})
	
	function saveData(success){
		console.log("保存了ComponentFile上面的数据")
		if(success) success()
	}
</script>

<style lang="scss" scoped>
	.content{
		border:1px solid blue;
	}
</style>