<template>
	<div class="fw-box">
	    <div class="title-box flex_box">
			<div class="title-able">Project--区域</div>
			<div class="close-able" @click.stop="onCloseBlock()">
				<el-icon> <Close /> </el-icon>
			</div>
		</div>
		<el-tabs v-model="currentName" class="demo-tabs" @tab-click="handleClick" stretch>
			<el-tab-pane label="项目" name="first" >
				<ProjectTree></ProjectTree>
			</el-tab-pane>
			<el-tab-pane label="控件" name="second">
				<div class="theme-box">
					<div class="theme-fg" :style="{backgroundColor: ProjectRef.BasicData.theme.currentTheme.color}"></div>
					<div class="theme-title">{{ProjectRef.BasicData.theme.currentTheme.title}}</div>
					<div class="theme-edit" @click.stop="onOpenThemeMgr()">
					<el-tooltip class="box-item" effect="light" content="编辑主题" placement="top-start" :show-after="500">
						<el-icon :size="18" ><EditPen /></el-icon>
					</el-tooltip>
					</div>
				</div>
				<div class="component-box" :style="{height:'calc(100vh - 245px - '+layoutParam.bottomTabHeight+'px)'}">
				   <ComponentBox style="margin-top:20px;width:calc(100% - 10px);"></ComponentBox>
				</div>
			</el-tab-pane>
			<el-tab-pane label="全局" name="third">
				<GlobalShare :varData= "ProjectRef.BasicData.globalData" title="项目全局共享变量"  
				    @refreshData="onRefreshGlobalData"  @saveData="onSaveGlobalData"></GlobalShare>
			</el-tab-pane>
		</el-tabs>
	</div>
	
</template>

<script lang="ts" setup>
	import { reactive,ref,inject,onMounted} from 'vue';
	import {apiLoadFileJson,apiUpdateFile} from '../../apis/designer.js'
    import { ElMessage } from 'element-plus';
	
	const ProjectRef = inject('ProjectRef')
	const layoutParam = inject("layoutParam")
	
	const currentName = ref('first')
	
	function handleClick(){
		
	}
	function onCloseBlock(){
		ProjectRef.funcObj.setBlockVisible('left')
	}
	function onOpenThemeMgr(){
		ProjectRef.funcObj.openFile("theme.top.current")
	}
	
	onMounted(()=>{
		// onRefreshGlobalData()
	})
	
	function onRefreshGlobalData(){
		loadGlobalJson({
			path:'global.top.data'
		})
	}
	
	function onSaveGlobalData(value:string){
		// console.log("onSaveGlobalData",value)
		updateGlobalData(value)
	}
	
	const varVersion = ref(0)
	
	async function loadGlobalJson(data){
		data.projectId = ProjectRef.BasicData.ProjectId
		let res = await apiLoadFileJson(data)
		if(res.code==200){
			ProjectRef.BasicData.globalData.varText = res.data.content
			ProjectRef.BasicData.globalData.version = res.data.version
		}
	}
	
	async function updateGlobalData(jsonStr:string){
		let res = await apiUpdateFile({
			projectId:ProjectRef.BasicData.ProjectId,
			version:ProjectRef.BasicData.globalData.version,
			path:"global.top.data",
			_json:jsonStr
		})
		if(res.code==200){
			ElMessage({
				message:"保存成功",
				type:"success"
			})
		}
	}
</script>

<style lang="scss" scoped>
	.fw-box{
		background-color: #F5F5F5;
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: stretch;
		user-select: none; /* 标准语法 */
		overflow: hidden;
		.title-box{
			background-color: white;
			padding:8px 16px;
			white-space: nowrap;
			margin-bottom: 1px;
			margin-top:1px;
			.title-able{
				flex: 1;
				font-size: 13px;
				text-align: left;
			}
			.close-able{
				cursor: pointer;
			}
		}
		
		.demo-tabs > .el-tabs__content {
		  padding: 32px;
		  color: #6b778c;
		  font-size: 32px;
		  font-weight: 600;
		  background-color: white;
		}
	}
	.theme-box {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding:4px 10px 8px 10px;
		.theme-fg{
			width:20px;
			height:20px;
			border-radius: 5px;
		}
		.theme-title{
			flex:1;
			font-size: 15px;
			color:#545454;
			margin-left:15px;
			font-weight: 600;
		}
		.theme-edit {
			cursor:pointer;
		}
		.theme-edit:hover {
			transform: scale(1.2);
		}
		.theme-edit:active {
			color:red;
		}
	}
	.component-box{
		// border:1px solid red;
		overflow: auto;
	}
	:deep(.el-tabs){
		flex:1;
		background-color: white;
		padding:0px 5px;
		overflow: hidden;
		// border:1px solid red;
	}
	:deep(.el-tab-pane){
		// border:1px solid red;
		height: calc(100% - 6px);
		overflow: hidden;
	}
</style>