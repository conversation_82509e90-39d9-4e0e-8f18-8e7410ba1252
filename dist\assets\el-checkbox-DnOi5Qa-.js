import{af as J,O as W,aA as w,Y as ee,L as F,M as f,aX as P,au as ae,ag as le,a2 as ne,e as T,ad as I,bv as A,P as ge,b9 as X,_ as R,K as E,T as te,N as K,g as oe,o as x,f as ue,a as Y,c as S,D as O,n as C,v as n,w as N,a8 as $,b6 as z,bQ as D,Q as j,F as Ce,k as se,t as ie,R as re,j as xe,H as Ve,J as ye,Z as Se,$ as Le,S as Be,U as ce}from"./index-Dgb0NZ1J.js";import{u as de,U as G,C as M,d as be,p as Ee}from"./index-CINbulG0.js";import{g as _e,e as H,u as Z,b as ve,c as U}from"./el-button-B8TCQS4u.js";import{i as Ie}from"./isEqual-CwZW0B1R.js";const me={modelValue:{type:[Number,String,Boolean],default:void 0},label:{type:[String,Boolean,Number,Object],default:void 0},value:{type:[String,Boolean,Number,Object],default:void 0},indeterminate:Boolean,disabled:Boolean,checked:Boolean,name:{type:String,default:void 0},trueValue:{type:[String,Number],default:void 0},falseValue:{type:[String,Number],default:void 0},trueLabel:{type:[String,Number],default:void 0},falseLabel:{type:[String,Number],default:void 0},id:{type:String,default:void 0},border:Boolean,size:ee,tabindex:[String,Number],validateEvent:{type:Boolean,default:!0},...de(["ariaControls"])},fe={[G]:e=>J(e)||W(e)||w(e),change:e=>J(e)||W(e)||w(e)},_=Symbol("checkboxGroupContextKey"),Fe=({model:e,isChecked:c})=>{const i=F(_,void 0),l=f(()=>{var r,d;const t=(r=i==null?void 0:i.max)==null?void 0:r.value,v=(d=i==null?void 0:i.min)==null?void 0:d.value;return!P(t)&&e.value.length>=t&&!c.value||!P(v)&&e.value.length<=v&&c.value});return{isDisabled:_e(f(()=>(i==null?void 0:i.disabled.value)||l.value)),isLimitDisabled:l}},Ne=(e,{model:c,isLimitExceeded:i,hasOwnLabel:l,isDisabled:b,isLabeledByFormItem:r})=>{const d=F(_,void 0),{formItem:t}=H(),{emit:v}=ae();function s(a){var h,k,u,p;return[!0,e.trueValue,e.trueLabel].includes(a)?(k=(h=e.trueValue)!=null?h:e.trueLabel)!=null?k:!0:(p=(u=e.falseValue)!=null?u:e.falseLabel)!=null?p:!1}function o(a,h){v(M,s(a),h)}function m(a){if(i.value)return;const h=a.target;v(M,s(h.checked),a)}async function V(a){i.value||!l.value&&!b.value&&r.value&&(a.composedPath().some(u=>u.tagName==="LABEL")||(c.value=s([!1,e.falseValue,e.falseLabel].includes(c.value)),await ne(),o(c.value,a)))}const L=f(()=>(d==null?void 0:d.validateEvent)||e.validateEvent);return le(()=>e.modelValue,()=>{L.value&&(t==null||t.validate("change").catch(a=>be()))}),{handleChange:m,onClickRoot:V}},$e=e=>{const c=T(!1),{emit:i}=ae(),l=F(_,void 0),b=f(()=>P(l)===!1),r=T(!1),d=f({get(){var t,v;return b.value?(t=l==null?void 0:l.modelValue)==null?void 0:t.value:(v=e.modelValue)!=null?v:c.value},set(t){var v,s;b.value&&I(t)?(r.value=((v=l==null?void 0:l.max)==null?void 0:v.value)!==void 0&&t.length>(l==null?void 0:l.max.value)&&t.length>d.value.length,r.value===!1&&((s=l==null?void 0:l.changeEvent)==null||s.call(l,t))):(i(G,t),c.value=t)}});return{model:d,isGroup:b,isLimitExceeded:r}},ze=(e,c,{model:i})=>{const l=F(_,void 0),b=T(!1),r=f(()=>A(e.value)?e.label:e.value),d=f(()=>{const o=i.value;return w(o)?o:I(o)?ge(r.value)?o.map(X).some(m=>Ie(m,r.value)):o.map(X).includes(r.value):o!=null?o===e.trueValue||o===e.trueLabel:!!o}),t=Z(f(()=>{var o;return(o=l==null?void 0:l.size)==null?void 0:o.value}),{prop:!0}),v=Z(f(()=>{var o;return(o=l==null?void 0:l.size)==null?void 0:o.value})),s=f(()=>!!c.default||!A(r.value));return{checkboxButtonSize:t,isChecked:d,isFocused:b,checkboxSize:v,hasOwnLabel:s,actualValue:r}},he=(e,c)=>{const{formItem:i}=H(),{model:l,isGroup:b,isLimitExceeded:r}=$e(e),{isFocused:d,isChecked:t,checkboxButtonSize:v,checkboxSize:s,hasOwnLabel:o,actualValue:m}=ze(e,c,{model:l}),{isDisabled:V}=Fe({model:l,isChecked:t}),{inputId:L,isLabeledByFormItem:a}=ve(e,{formItemContext:i,disableIdGeneration:o,disableIdManagement:b}),{handleChange:h,onClickRoot:k}=Ne(e,{model:l,isLimitExceeded:r,hasOwnLabel:o,isDisabled:V,isLabeledByFormItem:a});return(()=>{function p(){var y,g;I(l.value)&&!l.value.includes(m.value)?l.value.push(m.value):l.value=(g=(y=e.trueValue)!=null?y:e.trueLabel)!=null?g:!0}e.checked&&p()})(),U({from:"label act as value",replacement:"value",version:"3.0.0",scope:"el-checkbox",ref:"https://element-plus.org/en-US/component/checkbox.html"},f(()=>b.value&&A(e.value))),U({from:"true-label",replacement:"true-value",version:"3.0.0",scope:"el-checkbox",ref:"https://element-plus.org/en-US/component/checkbox.html"},f(()=>!!e.trueLabel)),U({from:"false-label",replacement:"false-value",version:"3.0.0",scope:"el-checkbox",ref:"https://element-plus.org/en-US/component/checkbox.html"},f(()=>!!e.falseLabel)),{inputId:L,isLabeledByFormItem:a,isChecked:t,isDisabled:V,isFocused:d,checkboxButtonSize:v,checkboxSize:s,hasOwnLabel:o,model:l,actualValue:m,handleChange:h,onClickRoot:k}},De=E({name:"ElCheckbox"}),Ge=E({...De,props:me,emits:fe,setup(e){const c=e,i=te(),{inputId:l,isLabeledByFormItem:b,isChecked:r,isDisabled:d,isFocused:t,checkboxSize:v,hasOwnLabel:s,model:o,actualValue:m,handleChange:V,onClickRoot:L}=he(c,i),a=K("checkbox"),h=f(()=>[a.b(),a.m(v.value),a.is("disabled",d.value),a.is("bordered",c.border),a.is("checked",r.value)]),k=f(()=>[a.e("input"),a.is("disabled",d.value),a.is("checked",r.value),a.is("indeterminate",c.indeterminate),a.is("focus",t.value)]);return(u,p)=>(x(),oe(re(!n(s)&&n(b)?"span":"label"),{class:C(n(h)),"aria-controls":u.indeterminate?u.ariaControls:null,onClick:n(L)},{default:ue(()=>{var y,g,Q,q;return[Y("span",{class:C(n(k))},[u.trueValue||u.falseValue||u.trueLabel||u.falseLabel?N((x(),S("input",{key:0,id:n(l),"onUpdate:modelValue":B=>z(o)?o.value=B:null,class:C(n(a).e("original")),type:"checkbox",indeterminate:u.indeterminate,name:u.name,tabindex:u.tabindex,disabled:n(d),"true-value":(g=(y=u.trueValue)!=null?y:u.trueLabel)!=null?g:!0,"false-value":(q=(Q=u.falseValue)!=null?Q:u.falseLabel)!=null?q:!1,onChange:n(V),onFocus:B=>t.value=!0,onBlur:B=>t.value=!1,onClick:$(()=>{},["stop"])},null,42,["id","onUpdate:modelValue","indeterminate","name","tabindex","disabled","true-value","false-value","onChange","onFocus","onBlur","onClick"])),[[D,n(o)]]):N((x(),S("input",{key:1,id:n(l),"onUpdate:modelValue":B=>z(o)?o.value=B:null,class:C(n(a).e("original")),type:"checkbox",indeterminate:u.indeterminate,disabled:n(d),value:n(m),name:u.name,tabindex:u.tabindex,onChange:n(V),onFocus:B=>t.value=!0,onBlur:B=>t.value=!1,onClick:$(()=>{},["stop"])},null,42,["id","onUpdate:modelValue","indeterminate","disabled","value","name","tabindex","onChange","onFocus","onBlur","onClick"])),[[D,n(o)]]),Y("span",{class:C(n(a).e("inner"))},null,2)],2),n(s)?(x(),S("span",{key:0,class:C(n(a).e("label"))},[j(u.$slots,"default"),u.$slots.default?O("v-if",!0):(x(),S(Ce,{key:0},[se(ie(u.label),1)],64))],2)):O("v-if",!0)]}),_:3},8,["class","aria-controls","onClick"]))}});var Ue=R(Ge,[["__file","checkbox.vue"]]);const we=E({name:"ElCheckboxButton"}),Pe=E({...we,props:me,emits:fe,setup(e){const c=e,i=te(),{isFocused:l,isChecked:b,isDisabled:r,checkboxButtonSize:d,model:t,actualValue:v,handleChange:s}=he(c,i),o=F(_,void 0),m=K("checkbox"),V=f(()=>{var a,h,k,u;const p=(h=(a=o==null?void 0:o.fill)==null?void 0:a.value)!=null?h:"";return{backgroundColor:p,borderColor:p,color:(u=(k=o==null?void 0:o.textColor)==null?void 0:k.value)!=null?u:"",boxShadow:p?`-1px 0 0 0 ${p}`:void 0}}),L=f(()=>[m.b("button"),m.bm("button",d.value),m.is("disabled",r.value),m.is("checked",b.value),m.is("focus",l.value)]);return(a,h)=>{var k,u,p,y;return x(),S("label",{class:C(n(L))},[a.trueValue||a.falseValue||a.trueLabel||a.falseLabel?N((x(),S("input",{key:0,"onUpdate:modelValue":g=>z(t)?t.value=g:null,class:C(n(m).be("button","original")),type:"checkbox",name:a.name,tabindex:a.tabindex,disabled:n(r),"true-value":(u=(k=a.trueValue)!=null?k:a.trueLabel)!=null?u:!0,"false-value":(y=(p=a.falseValue)!=null?p:a.falseLabel)!=null?y:!1,onChange:n(s),onFocus:g=>l.value=!0,onBlur:g=>l.value=!1,onClick:$(()=>{},["stop"])},null,42,["onUpdate:modelValue","name","tabindex","disabled","true-value","false-value","onChange","onFocus","onBlur","onClick"])),[[D,n(t)]]):N((x(),S("input",{key:1,"onUpdate:modelValue":g=>z(t)?t.value=g:null,class:C(n(m).be("button","original")),type:"checkbox",name:a.name,tabindex:a.tabindex,disabled:n(r),value:n(v),onChange:n(s),onFocus:g=>l.value=!0,onBlur:g=>l.value=!1,onClick:$(()=>{},["stop"])},null,42,["onUpdate:modelValue","name","tabindex","disabled","value","onChange","onFocus","onBlur","onClick"])),[[D,n(t)]]),a.$slots.default||a.label?(x(),S("span",{key:2,class:C(n(m).be("button","inner")),style:xe(n(b)?n(V):void 0)},[j(a.$slots,"default",{},()=>[se(ie(a.label),1)])],6)):O("v-if",!0)],2)}}});var ke=R(Pe,[["__file","checkbox-button.vue"]]);const Te=Ve({modelValue:{type:ye(Array),default:()=>[]},disabled:Boolean,min:Number,max:Number,size:ee,fill:String,textColor:String,tag:{type:String,default:"div"},validateEvent:{type:Boolean,default:!0},...de(["ariaLabel"])}),Ae={[G]:e=>I(e),change:e=>I(e)},Oe=E({name:"ElCheckboxGroup"}),Me=E({...Oe,props:Te,emits:Ae,setup(e,{emit:c}){const i=e,l=K("checkbox"),{formItem:b}=H(),{inputId:r,isLabeledByFormItem:d}=ve(i,{formItemContext:b}),t=async s=>{c(G,s),await ne(),c(M,s)},v=f({get(){return i.modelValue},set(s){t(s)}});return Se(_,{...Ee(Le(i),["size","min","max","disabled","validateEvent","fill","textColor"]),modelValue:v,changeEvent:t}),le(()=>i.modelValue,()=>{i.validateEvent&&(b==null||b.validate("change").catch(s=>be()))}),(s,o)=>{var m;return x(),oe(re(s.tag),{id:n(r),class:C(n(l).b("group")),role:"group","aria-label":n(d)?void 0:s.ariaLabel||"checkbox-group","aria-labelledby":n(d)?(m=n(b))==null?void 0:m.labelId:void 0},{default:ue(()=>[j(s.$slots,"default")]),_:3},8,["id","class","aria-label","aria-labelledby"])}}});var pe=R(Me,[["__file","checkbox-group.vue"]]);const Qe=Be(Ue,{CheckboxButton:ke,CheckboxGroup:pe});ce(ke);ce(pe);export{Qe as E};
