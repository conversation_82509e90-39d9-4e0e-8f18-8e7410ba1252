<template>
	<div class="content">
		<el-tree
		    ref="cmpTreeRef"
		    class="filter-tree"
		    :data="comTrees"
			default-expand-all
			empty-text="没有查到数据"
			:style="{height:'calc(100vh - 200px - '+layoutParam.bottomTabHeight+'px)',overflow:'auto'}"
		  >
		    <template #default="scope">
				<div :style="{backgroundColor: checkChooseItem(scope.data.id) ? '#f5f4ff' : 'white' }" class="item-plan">
					<div style="flex:1;text-align: left;" @click.stop="onClickItem(scope.data)" @contextmenu.prevent.stop="handleContextMenu(scope.data,$event)">
						{{scope.data.name}}
					</div>
					<div style="width:130px;text-align: right;">{{scope.data.key}}</div>
					<div style="width:130px;text-align: right;">{{scope.data.model}}</div>
				</div>
			</template>
		  </el-tree>
	</div>
</template>

<script lang="ts" setup>
	import {reactive,ref,onMounted,inject } from 'vue'
	const FileScript = inject("FileScript")
	const layoutParam = inject("layoutParam")
	const ChooseComponentList = inject("ChooseComponentList")
	const rightMenu = inject("rightMenu")
	
	const comTrees = reactive([
		{
			id:'-100',
			name:"组件对象",
			key:"组件类型",
			model:"呈现方式",
			children:FileScript.componentList
		}
	])
	const cmpTreeRef = ref(null)
	onMounted(()=>{
		
	})
	
	function checkChooseItem(id){
		if(ChooseComponentList && ChooseComponentList.chooseMap.has(id)){
			return true
		}
		return false
	}
	
	FileScript.otherFunc.refreshTree = ()=>{
		comTrees[0].children = FileScript.componentList
	}
	
	function onClickItem(data){
		if(data.id != '-100'){
			rightMenu.show=false
			if(checkIsComptChild(data)) return
			ChooseComponentList.setKey(data)
		}else{
			ChooseComponentList.clear();
		}
	}
	
	function checkIsComptChild(data){
		if(data.hasOwnProperty("parent") && data.parent && data.parent.hasOwnProperty("model") && data.parent.model && data.parent.model == 'TCustomCmpt'){
			return true
		}
		if(data.hasOwnProperty("parent") && data.parent && data.parent.id != '10001'){
			return checkIsComptChild(data.parent)
		}
		return false
	}
	
	const handleContextMenu = (item,e) => {
	  e.preventDefault()
	  
	  onClickItem(item)
	  
	  rightMenu.x = e.clientX 
	  rightMenu.y = e.clientY
	  rightMenu.offsetX = e.offsetX 
	  rightMenu.offsetY = e.offsetY
	  rightMenu.first = true
	  rightMenu.third = true
	  if(item.hasOwnProperty("key") && item.key == 'TBox'){
		  rightMenu.second = true
	  }else{
		  rightMenu.second = false
	  }
	  if(item.hasOwnProperty("key") && item.key == 'TCustomCmpt'){
	  		  rightMenu.fourth = true
	  }else{
	  		  rightMenu.fourth = false
	  }
	  rightMenu.parent = item
	  rightMenu.show=true
	}
	
</script>

<style lang="scss" scoped>
	.content {
		
	}
	.item-plan {
		display: flex;
		justify-content: space-between;
		align-items: center;
		width:100%;
		min-width: 200px;
	}
</style>