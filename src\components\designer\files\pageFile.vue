<template>
	<div class="content file-box" @mousemove="onMouseMove" @mouseup="onMouseUp">
		<div class="content-box" :style="{height: 'calc(100vh - 96px - ' + layoutParam.bottomTabHeight+'px)'}">
			<div class="app-box" :style="{width: 'calc(100% - 6px - '+ attriWidth + 'px)'}">
				<div class="app-bar">
					<div class="app-tools" :style="{width:'256px',textAlign:'left',paddingLeft:'20px'}">
					   <el-tooltip content="回退" placement="top" effect="light" style="font-size:22px;">
							<el-icon size="22" style="cursor:pointer;"> <Back /></el-icon>
					   </el-tooltip>
					   <el-tooltip content="参数列表" placement="top" effect="light" >
							<el-icon size="22" style="cursor:pointer;margin-left:20px;" :color="paramAttri.showPanel ? '#00aaff' : '#b8b8b8'"
							    @click.stop="onShowParam()">
								    <Operation />
							</el-icon>
					   </el-tooltip>
					</div>
					<div class="app-screen-value">
						<div>当前屏幕 {{' w:'+currentWidth +' x h:' + appScreen.realHeight.toFixed(0)}}</div>
					</div>
					<div :style="{width:'256px'}">
						<div class="app-tools" >
							<span v-if="ChooseComponentList.chooseMap.size == 1" >
							<el-tooltip content="置顶层" placement="top" effect="light" :show-after="500" disabled>
							<img src="@/assets/images/nav_icon_tot.png" class="tool-btn" @click.stop="setZindex('toTop')">
							</el-tooltip>
							</span>
							
							<span v-if="ChooseComponentList.chooseMap.size == 1" >
							<el-tooltip content="置底层" placement="top" effect="light" :show-after="500" >
							<img src="@/assets/images/nav_icon_tob.png" class="tool-btn" @click.stop="setZindex('toBottom')">
							</el-tooltip>
							</span>
							
						    <span v-if="ChooseComponentList.chooseMap.size > 0" >
							<el-tooltip content="左右居中对齐" placement="top" effect="light" >
							<img src="@/assets/images/nav_icon_lpr.png" class="tool-btn" @click.stop="toAlignDo('lpr')"  >
							</el-tooltip>
							</span>
							
							<span v-if="ChooseComponentList.chooseMap.size > 0" >
							<el-tooltip content="上下居中对齐" placement="top" effect="light" :show-after="500">
							<img src="@/assets/images/nav_icon_tpb.png" class="tool-btn" @click.stop="toAlignDo('tpb')">
							</el-tooltip>
							</span>
							
							<span v-if="ChooseComponentList.chooseMap.size > 1" >
							<el-tooltip content="左对齐" placement="top" effect="light" :show-after="500">
							<img src="@/assets/images/nav_icon_tol.png" class="tool-btn" @click.stop="toAlignDo('tol')">
							</el-tooltip>
							</span>
							
							<span v-if="ChooseComponentList.chooseMap.size > 1" >
							<el-tooltip content="上对齐" placement="top" effect="light" :show-after="500">
							<img src="@/assets/images/nav_icon_tot.png" class="tool-btn" @click.stop="toAlignDo('tot')">
							</el-tooltip>
							</span>
							
							<span v-if="ChooseComponentList.chooseMap.size > 1" >
							<el-tooltip content="右对齐" placement="top" effect="light" :show-after="500">
							<img src="@/assets/images/nav_icon_tor.png" class="tool-btn" @click.stop="toAlignDo('tor')">
							</el-tooltip>
							</span>
							
							<span v-if="ChooseComponentList.chooseMap.size > 1" >
							<el-tooltip content="下对齐" placement="top" effect="light" :show-after="500">
							<img src="@/assets/images/nav_icon_tob.png" class="tool-btn" @click.stop="toAlignDo('tob')">
							</el-tooltip>
							</span>
							
							<span v-if="ChooseComponentList.chooseMap.size > 1" >
							<el-tooltip content="置左右等间" placement="top" effect="light" :show-after="500">
							<img src="@/assets/images/nav_icon_ltor.png" class="tool-btn" @click.stop="toAlignDo('ltor')">
							</el-tooltip>
							</span>
							
							<span v-if="ChooseComponentList.chooseMap.size > 1" >
							<el-tooltip content="置上下等间" placement="top" effect="light" :show-after="500">
							<img src="@/assets/images/nav_icon_ttob.png" class="tool-btn" @click.stop="toAlignDo('ttob')">
							</el-tooltip>
							</span>
						</div>
					</div>
				</div>
				
				<div class="app-body" @keyup.stop="onDivKeyUp" @keydown.stop="onDivKeyDown" tabindex="0"  @click.stop="testMovePos()">
					<div class="app-param" :style="{height:'calc('+ attriHeight + 'px)'}" v-if="paramAttri.showPanel" >
						<div class="param-header">
							<div class="header-name">参数名</div>
							<div class="header-type">参数类型</div>
							<div class="header-value">默认值</div>
							<div class="oper-btn" @click.stop="FileScript.addParam()">
								<el-icon style="cursor:pointer;"> <Plus /></el-icon>
								<div>新增</div>
							</div>
						</div>
						<div class="param-row" v-for="(rowItem,index) in FileScript.params" :key="index">
							<!-- <div class="row-name">fdfdsffd</div> -->
							<el-input v-model="rowItem.name" class="row-name" placeholder="请输入参数名" />
							<!-- <div class="row-type">{{rowItem.type}}</div> -->
							<el-select v-model="rowItem.type" placeholder="Select" class="row-type">
							    <el-option  key="string" label="字符型(string)" value="string" />
								<el-option  key="number" label="数字型(number)" value="number" />
								<el-option  key="boolean" label="布尔型(boolean)" value="boolean" />
								<el-option  key="array" label="数组型(array)" value="array" />
								<el-option  key="object" label="对象型(object)" value="object" />
								<el-option  key="date" label="时间型(date)" value="date" />
							</el-select>
							<el-input v-model="rowItem.value" class="row-value" placeholder="可输入默认值..." />
							<!-- <div class="row-value">{{rowItem.value}}</div> -->
							<div class="oper-btn" @click.stop="FileScript.removeParam(index)">
								<el-icon style="cursor:pointer;" > <Delete /></el-icon>
								<div>删除</div>
							</div>
						</div>
					</div>
					<div class="drag-h-line" :style="{top: 'calc('+ attriHeight + 'px)'}" @mousedown="onMouseDownForMoveParam" v-if="paramAttri.showPanel"></div>
					<div  class="app-content" :style="{width: 'calc('+(appScreen.scaleValue >= 100 ? appScreen.scaleValue : 100)+'% - 10px)', 
					      height: (appScreen.realHeight + 1500)  + 'px'}" @mouseup="FileScript.onMouseUp">
						  
						<div class="app-shadow" v-show="props.fileItem.fileItem.type=='page'"
						     :style="{width: (currentWidth + 40) +'px',height: (currentHeight + 40) + 'px',
						     left: 'calc(50vw - '+ ((attriWidth + layoutParam.leftTabWidth + currentWidth + 40)/2)+'px)'}">
							
						</div> 
						<div class="app-board" ref="appBoardRef" @dragover.stop.prevent="onBoardDragOver" @drop.stop="onBoardDrop" @mousedown.stop="FileScript.onMouseDown(20,$event)"
						     @mousemove.prevent="FileScript.onMouseMove"  @mouseup="FileScript.onMouseUp"  @contextmenu.prevent.stop="handleContextMenu"
						     :style="{width: (currentWidth - paddingWidth('width')) +'px',height: (appScreen.realHeight - paddingWidth('height')) + 'px',
						     left: 'calc(50vw - '+ ((attriWidth + layoutParam.leftTabWidth + currentWidth + 40)/2)+'px + 20px)',
							 ...getAppBodyStyle()}">
							 <TComponents v-for="(cpt,index) in FileScript.componentList "  :key="index" :TItem="cpt" :TParent="{id:appBodyParentId}" :TIndex="index" />
							 <div class="choose-auto-box"  v-show="chooseBox.show"
							     :style="{top: (chooseBox.y <= chooseBox.y1 ? chooseBox.y : chooseBox.y1) + 'px',
								          left: (chooseBox.x <= chooseBox.x1 ? chooseBox.x :  chooseBox.x1) + 'px',
										  width: (chooseBox.x <= chooseBox.x1 ? (chooseBox.x1 - chooseBox.x) : (chooseBox.x - chooseBox.x1)) + 'px',
										  height: (chooseBox.y <= chooseBox.y1 ? (chooseBox.y1 - chooseBox.y) : (chooseBox.y - chooseBox.y1)) + 'px'}"
							      ></div>
						</div>
						<div class="app-down-line" @mousedown="onMouseDownForAppHeight"
						    :style="{top:'calc( 40px + '+appScreen.realHeight+'px)',
						    left: 'calc(50vw - '+ ((attriWidth + layoutParam.leftTabWidth + 50 + 40)/2)+'px + 20px)'}">
							
						</div>
					</div>
					
				</div>
			</div>
			<div class="drag-v-line" :style="{left: 'calc(100% - '+ attriWidth + 'px)'}" @mousedown="onMouseDownForMoveAttr"
				v-if="layoutParam.showRightPanel"></div>
			<RightBox class="attrib-box" :style="{width: attriWidth+'px'}" v-if="layoutParam.showRightPanel"></RightBox>
		</div>
		<div class="right-menu-box" v-show="rightMenu.show" :style="{left:rightMenu.x+'px',top:rightMenu.y+'px'}">
			<div class="menu-copy menu-item" @click.stop="handleAction('copy')" v-if="rightMenu.first">复制</div>
			<div class="menu-paste menu-item" @click.stop="handleAction('paste')" v-if="rightMenu.second">粘贴</div>
			<div class="menu-paste menu-item" @click.stop="handleAction('refresh')" v-if="rightMenu.fourth">刷新</div>
			<div class="menu-delete menu-item" @click.stop="handleAction('delete')" v-if="rightMenu.third">删除</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
	import { reactive, ref, onMounted,onUnmounted, inject, computed,watch,getCurrentInstance, provide, nextTick,toRaw } from 'vue'
	import { useFileBase } from '../../../common/fileBasic'
	const { emitter } = getCurrentInstance()!.appContext.config.globalProperties
	import {  ElMessage, ElMessageBox } from "element-plus";
	import {apiUpdateFile,apiLoadFileJson} from '../../../apis/designer.js'
	import { useExplainVar } from '../../../common/useExplainVar';
	
    interface IComponentType {
		id:string,
		key:string,
		group:string,
		name:string,
		icon:string,
		children?:[]
	}
	const appBodyParentId = '10001'
	
	const zindexLine = 400
	
	const {parse,releaseFile,packFile} = useExplainVar()
	
	const props = defineProps({
		fileItem: {
			type: Object,
			default: {}
		},
	})
	const rightMenu = reactive({
		show:false,
		x:0,
		y:0,
		offsetX:0,
		offsetY:0,
		first:false,
		second:false,
		third:false,
		fourth:false,
	})
	provide("rightMenu",rightMenu)
	
	const cmptNameMap = reactive({
		names:new Map<string,number>(),
		nameNum:new Map<string,number>()
	})
	
	cmptNameMap.getName =(key:string)=>{
		let maxNum:number = 1
		if(cmptNameMap.nameNum.has(key)){
			maxNum = cmptNameMap.nameNum.get(key)
		}
		for(let n=1;n<500;n++){
			let keyName:string = key + (maxNum + n)
			if(!cmptNameMap.names.has(keyName)){
				cmptNameMap.names.set(keyName,maxNum + n)
				cmptNameMap.nameNum.set(key,maxNum + n)
				return keyName
			}
		}
		return key+(new Date()).getTime()
	}
	
	cmptNameMap.setName = (name:string)=>{
		if(!cmptNameMap.names.has(name)){
		   	cmptNameMap.names.set(name,0)
			return true
		}
		return false
	}
		
	cmptNameMap.checkName = (name:string)=>{
		return cmptNameMap.names.has(name)
	}
	
	provide("cmptNameMap",cmptNameMap)
	
	const fileBase = useFileBase(props.fileItem)
	const ProjectRef = inject("ProjectRef")
	const layoutParam = inject("layoutParam")
	const sizeControl = inject("sizeControl")
	const pjResouceMap = inject("pjResouceMap")
	const appBoardRef = ref(null)

	const attrOrignWidth = ref(360)
	const attriWidth = computed(() => {
		return layoutParam.showRightPanel ? attrOrignWidth.value : 0
	})
	
	const attriHeight = computed(()=>{
		return paramAttri.showPanel ? paramAttri.orignHeight : 0
	})
	
	const paramAttri = reactive({
		showPanel:false,
		orignHeight:40,
	})

	function onCloseMenu(){
		rightMenu.show = false
	}
	
	const handleContextMenu = (e) => {
	  e.preventDefault()
	  readyCopy(e)
	  rightMenu.show=true
	}
	
	const chooseBox = reactive({
		show:false,
		x:0,
		y:0,
		x1:0,
		y1:0,
		offsetX:0,
		offsetY:0,
		parent:{id:appBodyParentId}
	})
	provide("chooseBox",chooseBox)
	
	const ChooseComponentList = reactive({
		chooseMap:new Map,
		parentId:appBodyParentId,
		doOtherFunc:{}
	})
	ChooseComponentList.setKey = (item)=>{
		if(!ChooseComponentList.chooseMap.has(item.id)){
			const xParentId = item.hasOwnProperty("parent") && item.parent ? item.parent.id : appBodyParentId
			if(pressControlKey.value != 1){
				ChooseComponentList.chooseMap.clear()
			}else{
				if(ChooseComponentList.chooseMap.size > 0 && ChooseComponentList.parentId != xParentId){
					ChooseComponentList.chooseMap.clear()
				}
			}
			ChooseComponentList.parentId = xParentId
			ChooseComponentList.chooseMap.set(item.id,item)
			ChooseComponentList.notice()
		}
	}
	
	ChooseComponentList.clear = ()=>{
		ChooseComponentList.chooseMap.clear()
		ChooseComponentList.notice()
	}
	
	ChooseComponentList.notice = ()=>{
		if(ChooseComponentList.doOtherFunc.noticeAttr){
			ChooseComponentList.doOtherFunc.noticeAttr()
		}
		if(ChooseComponentList.doOtherFunc.noticeEvent){
			ChooseComponentList.doOtherFunc.noticeEvent()
		}
	}

	provide("ChooseComponentList",ChooseComponentList)
	
	const FileScript = reactive({
		fileId:props.fileItem.fileItem.fileId,
		filePath:props.fileItem.fileItem.filePath,
		params:[],
		componentList:[],
		attribute:{},
		triggers:{},
		instance:{
			varText:""
		},
		otherFunc:{},
		isMouseDown:false,
		category:0,
		downX:0,
		downY:0,
		targetW:0,
		targetH:0,
		target:null,
		lastZindex:zindexLine,
		firstZindex:zindexLine,
	})
	
	function getAppBodyStyle(){
		let nStyle = {}
		if(FileScript.attribute && FileScript.attribute.background && FileScript.attribute.background.backgroundImageOpen==true
		    && FileScript.attribute.background.backgroundImageUrl && FileScript.attribute.background.backgroundImageUrl.length > 0){
			if(FileScript.attribute.background.backgroundImageUrl.split('.')[0] =='resource'){
				nStyle.backgroundImage = 'url('+pjResouceMap.getResourceUrl(FileScript.attribute.background.backgroundImageUrl)+')'
			}else{
				nStyle.backgroundImage = 'url('+FileScript.attribute.background.backgroundImageUrl+')'
			}
			nStyle.backgroundSize = 'cover'
			nStyle.backgroundPosition = 'center'
			nStyle.backgroundRepeat = 'no-repeat'
		}else{
			if(FileScript.hasOwnProperty("attribute") && FileScript.attribute.hasOwnProperty("background")
			  && FileScript.attribute.background.gradientOpen==true){
				nStyle.background='linear-gradient('+ (FileScript.attribute.background.gradientAngle ? FileScript.attribute.background.gradientAngle : 90) +'deg ,'+
				  FileScript.getBackgroundColor() + ','+ FileScript.attribute.background.gradientColor2+')'
			}else{
			     nStyle.backgroundColor = FileScript.getBackgroundColor()
			}
		}
		nStyle.padding = FileScript.getPadding()
		
		if(FileScript.hasOwnProperty("attribute") && FileScript.attribute.hasOwnProperty("layout")){
		    if(FileScript.attribute.layout.layoutDirection == 'column') {
				nStyle.display = 'flex'
				nStyle.flexDirection = 'column'
				if(FileScript.attribute.layout.vAlign=='align-top'){
					nStyle.justifyContent = 'flex-start'
				}else if(FileScript.attribute.layout.vAlign=='align-bottom'){
					nStyle.justifyContent = 'flex-end'
				}else if(FileScript.attribute.layout.vAlign=='align-center'){
					nStyle.justifyContent = 'center'
				}else if(FileScript.attribute.layout.vAlign=='align-stretch'){
					nStyle.justifyContent = 'space-between'
				}
				
				if(FileScript.attribute.layout.hAlign=='align-left'){
					nStyle.alignItems = 'flex-start'
				}else if(FileScript.attribute.layout.hAlign=='align-right'){
					nStyle.alignItems = 'flex-end'
				}else if(FileScript.attribute.layout.hAlign=='align-center'){
					nStyle.alignItems = 'center'
				}else if(FileScript.attribute.layout.hAlign=='align-stretch'){
					nStyle.alignItems = 'stretch'
				}
			}else if(FileScript.attribute.layout.layoutDirection == 'row'){
				nStyle.display = 'flex'
				nStyle.flexDirection = 'row'
				if(FileScript.attribute.layout.hAlign=='align-left'){
					nStyle.justifyContent = 'flex-start'
				}else if(FileScript.attribute.layout.hAlign=='align-right'){
					nStyle.justifyContent = 'flex-end'
				}else if(FileScript.attribute.layout.hAlign=='align-center'){
					nStyle.justifyContent = 'center'
				}else if(FileScript.attribute.layout.hAlign=='align-stretch'){
					nStyle.justifyContent = 'space-between'
				}
				
				if(FileScript.attribute.layout.vAlign=='align-top'){
					nStyle.alignItems = 'flex-start'
				}else if(FileScript.attribute.layout.vAlign=='align-bottom'){
					nStyle.alignItems = 'flex-end'
				}else if(FileScript.attribute.layout.vAlign=='align-center'){
					nStyle.alignItems = 'center'
				}else if(FileScript.attribute.layout.vAlign=='align-stretch'){
					nStyle.alignItems = 'stretch'
				}
			}
		}
		
		// nStyle.overflowY = 'hidden'
		// nStyle.overflowX = 'hidden'
		if(FileScript.attribute.hasOwnProperty("layout")){
			if(FileScript.attribute.layout.hasOwnProperty("scrollHOpen") 
				   && FileScript.attribute.layout.scrollHOpen && FileScript.attribute.layout.scrollHOpen==true){
				nStyle.overflowX = 'auto'
			}
			if(FileScript.attribute.layout.hasOwnProperty("scrollVOpen") && FileScript.attribute.layout.scrollVOpen
				  && FileScript.attribute.layout.scrollVOpen==true){
				nStyle.overflowY = 'auto'
			}
		}
		
		return nStyle 
	}
	
	FileScript.getBackgroundColor = ()=>{	
		if(FileScript.attribute && FileScript.attribute.background){
			if(FileScript.attribute.background.theme && FileScript.attribute.background.theme==true){
				let themeColor = ProjectRef.funcObj.getValueByThemeKey(FileScript.attribute.background.themeKey)
				if(themeColor) return themeColor
			}
			return FileScript.attribute.background.backgroundColor ? FileScript.attribute.background.backgroundColor : '#fff'
		}else{
			return '#fff'
		}
	}
	
	FileScript.getPadding = ()=>{
		if(FileScript.hasOwnProperty('attribute') && FileScript.attribute.hasOwnProperty('box')){
			return FileScript.attribute.box.paddingTop + 'px ' + FileScript.attribute.box.paddingRight+"px " +
			       FileScript.attribute.box.paddingBottom + 'px ' + FileScript.attribute.box.paddingLeft+"px "
		}else{
			return '0px'
		}
	}
	
	function paddingWidth(d){
		if(FileScript.hasOwnProperty('attribute') && FileScript.attribute.hasOwnProperty('box')){
			if(d=='width'){
				return FileScript.attribute.box.paddingLeft + FileScript.attribute.box.paddingRight
			}else if(d=='height'){
				return FileScript.attribute.box.paddingTop + FileScript.attribute.box.paddingBottom
			}
		}else{
			return 0
		}
	}
	
	// :style="{width: currentWidth +'px',height: appScreen.realHeight + 'px',
	
	FileScript.addParam = ()=>{
		FileScript.params.push({
			name:'',
			type:'string',
			value:''
		})
		props.fileItem.isModify = true
		let shouldHeight = 49*FileScript.params.length + 43
		if(paramAttri.orignHeight < shouldHeight){
			paramAttri.orignHeight = shouldHeight
		}
	}
	FileScript.refreshTree = ()=>{
		if(FileScript.otherFunc.refreshTree){
			FileScript.otherFunc.refreshTree()
		}
	}
	
	
	
	FileScript.removeParam = (index)=>{
		FileScript.params.splice(index,1)
		props.fileItem.isModify = true
		let shouldHeight = 49*FileScript.params.length + 43
		if(paramAttri.orignHeight >= shouldHeight){
			paramAttri.orignHeight = shouldHeight
		}
	}
	
	FileScript.createNewComponent = (xParent:IComponentType,xItem:IComponentType)=>{
		
		xItem.name = cmptNameMap.getName(xItem.key)
		if(xParent && xParent.id != appBodyParentId){
			if(!xParent.hasOwnProperty("lastZindex")) xParent.lastZindex = zindexLine
			if(!xParent.hasOwnProperty("firstZindex")) xParent.firstZindex = zindexLine
			xItem.zindex = xParent.lastZindex++
			
			xParent.children?.push(xItem)
			xItem.parent = xParent
			props.fileItem.isModify = true
			return xParent.children[xParent.children.length - 1]
		}else{
			xItem.parent = xParent ? xParent : {id:appBodyParentId}
			
			if(xItem.parent.id == appBodyParentId){
				xItem.zindex = FileScript.lastZindex++
			}else{
				if(!xItem.parent.hasOwnProperty("lastZindex")) xItem.parent.lastZindex = zindexLine
				if(!xItem.parent.hasOwnProperty("firstZindex")) xItem.parent.firstZindex = zindexLine
				xItem.zindex = xItem.parent.lastZindex++
			}
			
			FileScript.componentList.push(xItem)
			props.fileItem.isModify = true
			return FileScript.componentList[FileScript.componentList.length - 1]
		}
	}
	
	function readyCopy(e){
		rightMenu.x = e.clientX
		rightMenu.y = e.clientY
		rightMenu.offsetX = e.offsetX
		rightMenu.offsetY = e.offsetY
		rightMenu.first = false
		rightMenu.third = false
		rightMenu.fourth = false
		rightMenu.second = true
		rightMenu.parent = {id:appBodyParentId}
	}
	
	FileScript.onMouseDown = (category:number,e:MouseEvent)=>{
		onCloseMenu()
		FileScript.isMouseDown = true
		FileScript.category = category
		FileScript.downX = e.clientX
		FileScript.downY = e.clientY
		if(category==20){
			chooseBox.show = true
			chooseBox.x = e.offsetX + chooseBox.offsetX
			chooseBox.y = e.offsetY + chooseBox.offsetY
			chooseBox.x1 = e.offsetX + chooseBox.offsetX
			chooseBox.y1 = e.offsetY + chooseBox.offsetY
		}
		FileScript.targetH = 0
		FileScript.targetW = 0
		
		readyCopy(e)
	}
	FileScript.onMouseMove = (e:MouseEvent)=>{
		if(FileScript.isMouseDown){

			FileScript.targetW = e.clientX - FileScript.downX
			FileScript.downX = e.clientX
			FileScript.targetH = e.clientY - FileScript.downY
			FileScript.downY = e.clientY
			
			if(FileScript.category==1){
				chooseItemMove(0,FileScript.targetW,FileScript.targetH)
			}else if(FileScript.target && FileScript.category==11){ //left-top
				FileScript.target.x += FileScript.targetW
				FileScript.target.w -= FileScript.targetW
				FileScript.target.y += FileScript.targetH
				FileScript.target.h -= FileScript.targetH
			
			}else if(FileScript.target && FileScript.category==12){ //top
			    FileScript.target.y += FileScript.targetH
			    FileScript.target.h -= FileScript.targetH
				
			}else if(FileScript.target && FileScript.category==13){ //right-top
				FileScript.target.w += FileScript.targetW
				FileScript.target.y += FileScript.targetH
				FileScript.target.h -= FileScript.targetH
				
			}else if(FileScript.target && FileScript.category==14){ // right
				FileScript.target.w += FileScript.targetW
				
			}else if(FileScript.target && FileScript.category==15){ // right-bottom
				FileScript.target.w += FileScript.targetW
				FileScript.target.h += FileScript.targetH
				
			}else if(FileScript.target && FileScript.category==16){ // bottom
				FileScript.target.h += FileScript.targetH
				
			}else if(FileScript.target && FileScript.category==17){ // left-bottom
				FileScript.target.x += FileScript.targetW
				FileScript.target.w -= FileScript.targetW
				FileScript.target.h += FileScript.targetH
				
			}else if(FileScript.target && FileScript.category==18){ // left
				FileScript.target.x += FileScript.targetW
				FileScript.target.w -= FileScript.targetW
				
			}else if(FileScript.category==20){
				chooseBox.x1 += FileScript.targetW
				chooseBox.y1 += FileScript.targetH
				
			}
			FileScript.targetW = 0
			FileScript.targetH = 0
		}
	}
    FileScript.onMouseUp = (e:MouseEvent)=>{
		if(FileScript.isMouseDown){
			FileScript.isMouseDown = false
			if(FileScript.category==20 ){
				if(Math.abs(chooseBox.x - chooseBox.x1) > 2 || Math.abs(chooseBox.y - chooseBox.y1) > 2 ){
					selectMoreItemByBox()
				}else if(chooseBox.parent && chooseBox.parent.id != appBodyParentId){
					ChooseComponentList.setKey(chooseBox.parent)
				}else{
					ChooseComponentList.clear()
				}
			}
			if(FileScript.category==0){
				ChooseComponentList.clear()
			}
			FileScript.category = 0
			FileScript.target = null
			chooseBox.show = false
			chooseBox.x = 0
			chooseBox.y = 0
			chooseBox.x1 = 0
			chooseBox.y1 = 0
			chooseBox.offsetX = 0
			chooseBox.offsetY = 0
			chooseBox.parent = {id:appBodyParentId}
			
			window.dispatchEvent(new Event('click',{}));
		}
		onCloseMenu()
		
		
	}
	
	function testMovePos(){

	}
	
	provide("FileScript",FileScript)
	
	const appScreen = reactive({
		screenWidth:298,
		screenHeight:468,
		realHeight:468,
		scaleValue:100,
	})
	
	provide("appScreen",appScreen)
	
	const currentWidth = computed(()=>{
		return appScreen.screenWidth * (appScreen.scaleValue/100);
	})
	const currentHeight = computed(()=>{
		return appScreen.screenHeight * (appScreen.scaleValue/100);
	})

	function refreshSizeControl(pos){
		if(pos==1){
			appScreen.screenHeight = sizeControl.hg
			appScreen.screenWidth = sizeControl.wh
		}else if(pos == 2){
			appScreen.scaleValue = sizeControl.scale.value
		}else{
			appScreen.screenHeight = sizeControl.hg
			appScreen.screenWidth = sizeControl.wh
			appScreen.scaleValue = sizeControl.scale.value
		}
		if(appScreen.screenHeight * (appScreen.scaleValue/100) > appScreen.realHeight + 40 ){
			appScreen.realHeight = appScreen.screenHeight * (appScreen.scaleValue/100)
		}
	}

	const dragDown = reactive({
		isDown: false,
		category:0,
		xPos: 0,
		minValue:0
	}) 
	const onMouseDownForMoveAttr = (e) => {
		dragDown.isDown = true
		dragDown.category = 1
		dragDown.xPos = e.clientX
	}
	const onMouseDownForMoveParam = (e) => {
		dragDown.isDown = true
		dragDown.category = 3
		dragDown.xPos = e.clientY
	}
	const onMouseDownForAppHeight = (e)=>{
		dragDown.isDown = true
		dragDown.category = 2
		dragDown.xPos = e.clientY
		dragDown.minValue = appScreen.screenHeight * (appScreen.scaleValue/100)
	}
	const onMouseMove = (e) => {
		if (dragDown.isDown) {
			if(dragDown.category==1){
				attrOrignWidth.value += dragDown.xPos - e.clientX
				dragDown.xPos = e.clientX
			}else if(dragDown.category==2){
				let tmpH = appScreen.realHeight + e.clientY - dragDown.xPos
				if(props.fileItem.fileItem.type=='page'){
					if(tmpH > appScreen.realHeight || (tmpH < appScreen.realHeight && dragDown.minValue < tmpH)){
						appScreen.realHeight = tmpH
					}
				}else{
					appScreen.realHeight = tmpH
				}
				dragDown.xPos = e.clientY
			}else if(dragDown.category==3){
				paramAttri.orignHeight += e.clientY - dragDown.xPos
				dragDown.xPos = e.clientY
			}
		}
	}
	const onMouseUp = (e) => {
		dragDown.isDown = false
		dragDown.category==0
	}

	onMounted(() => {
		props.fileItem.func.saveData = saveData
		layoutParam.showRightPanel = true
		refreshSizeControl(0)
		emitter.on("refreshSizeControl",(v:number)=>{
			refreshSizeControl(v)
		})
		loadJsonByPath()
	})
	
	onUnmounted(()=>{
		emitter.off("refreshSizeControl")
		
	})
	
	fileBase.onFWActived(()=>{
		//页面得到当前为活动页面触发，即得到焦点页面
	})
	
	fileBase.onFWUnActived(()=>{
		//失去焦点页面
	})
	
	function onShowParam(){
		if(!paramAttri.showPanel){
			let shouldHeight = 49*FileScript.params.length + 43
			if(paramAttri.orignHeight < shouldHeight){
				paramAttri.orignHeight = shouldHeight
			}
		}
		paramAttri.showPanel = !paramAttri.showPanel
	}
	

	function saveData(success) {
		updateFile(()=>{
			if (success) success()
		})
		
	}
	
	function setZindex(pos){
		if(ChooseComponentList.chooseMap.size == 1){
			let xItem = ChooseComponentList.chooseMap.values().next().value
			if(pos=='toTop'){
				if(xItem.parent && xItem.parent.id == appBodyParentId){
					if(!FileScript.hasOwnProperty("lastZindex")){
						FileScript.lastZindex = FileScript.componentList.length + zindexLine + 1
					}
					xItem.zindex = FileScript.lastZindex++
				}else if(xItem.parent){
					if(!xItem.parent.hasOwnProperty("lastZindex")){
						xItem.parent.lastZindex = xItem.parent.children.length + zindexLine + 1
					}
					xItem.zindex = xItem.parent.lastZindex++
				}
			}else if(pos=='toBottom'){
				if(xItem.parent && xItem.parent.id == appBodyParentId){
					if(!FileScript.hasOwnProperty("firstZindex")){
						FileScript.firstZindex = 199
					}
					xItem.zindex = FileScript.firstZindex--
				}else  if(xItem.parent){
					if(!xItem.parent.hasOwnProperty("firstZindex")){
						xItem.parent.firstZindex = 199
					}
					xItem.zindex = xItem.parent.firstZindex--
				}
			}
		}
	}
	
	function getParentWH(xItem,type){
		if(xItem && xItem.id=='10001'){
			if(type=='width'){
				return appScreen.screenWidth * (appScreen.scaleValue/100) - paddingWidth('width')
			}else{
				return appScreen.realHeight  -  paddingWidth('height') - 20
			}
		}else if(xItem && xItem.id !='10001'){
			if(type=='width'){
				if(xItem.wUnit=='%'){
					let offset = xItem.whOffsetOpen ? xItem.offsetW : 0
					return (xItem.w/100.0)* getParentWH(xItem.parent,type) + offset
				}else{
					return xItem.w
				}
			}else{
				if(xItem.hUnit=='%'){
					let offset = xItem.whOffsetOpen ? xItem.offsetH : 0
					return (xItem.h/100.0)* getParentWH(xItem.parent,type) + offset
				}else{
					return xItem.h
				}
			}
		}
		return 0
	}
	
	function toAlignDo(tool:string){
		if(ChooseComponentList.chooseMap.size == 1){
			let xItem = ChooseComponentList.chooseMap.values().next().value
			if(xItem && xItem.parent){
				if(tool=='lpr'){
					let parentW = getParentWH(xItem.parent,"width")
					let selfW = getParentWH(xItem,'width')
					if(parentW > 0){
						xItem.x = (parentW - selfW)/2
					}
				}else if(tool == 'tpb'){
					let parentH = getParentWH(xItem.parent,"height")
					let selfH = getParentWH(xItem,"height")
					if(parentH > 0){
					   xItem.y = (parentH - selfH)/2
					}
				}
			}
		}else if(ChooseComponentList.chooseMap.size > 1){
			let targetItem = null
			if(tool=='ltor' || tool=='ttob'){
				targetItem = getFourDirectItems()
				if(tool=='ltor') targetItem.remain = targetItem.rightItem.x + targetItem.rightItem.w  -  targetItem.leftItem.x 
				if(tool=='ttob') targetItem.remain = targetItem.bottomItem.y + targetItem.bottomItem.h  -  targetItem.topItem.y
			}
			for(let key of ChooseComponentList.chooseMap.keys()){
				let xItem = ChooseComponentList.chooseMap.get(key)
				if(!targetItem) targetItem = xItem
				if(tool=='lpr'){
					if(targetItem.id != xItem.id){
						xItem.x = targetItem.x + targetItem.w/2 - xItem.w/2
					}
				}else if(tool=='tpb'){
					if(targetItem.id != xItem.id){
						xItem.y = targetItem.y + targetItem.h/2 - xItem.h/2
					}
				}else if(tool=='tol'){
					if(targetItem.id != xItem.id){
						xItem.x = targetItem.x
					}
				}else if(tool=='tot'){
					if(targetItem.id != xItem.id){
						xItem.y = targetItem.y
					}
				}else if(tool=='tor'){
					if(targetItem.id != xItem.id){
						xItem.x = targetItem.x + targetItem.w - xItem.w
					}
				}else if(tool=='tob'){
					if(targetItem.id != xItem.id){
						xItem.y = targetItem.y + targetItem.h - xItem.h
					}
				}else if(tool=='ltor'){
					targetItem.remain = targetItem.remain - xItem.w
					// console.log(" w - :"+xItem.w+",remain=" + targetItem.remain)
				}else if(tool=='ttob'){
					targetItem.remain = targetItem.remain - xItem.h
					// console.log(" h - :"+xItem.h+",remain=" + targetItem.remain)
				}
			}
			// console.log(targetItem.remain,targetItem)
			if(targetItem.remain > 0){
				let interval = targetItem.remain/(ChooseComponentList.chooseMap.size - 1)
				if(interval > 0){
					let tmpItem = tool=='ltor' ? targetItem.leftItem : targetItem.topItem
					for(let key1 of ChooseComponentList.chooseMap.keys()){
						let xItem = ChooseComponentList.chooseMap.get(key1)
						if(tool=='ltor'){
							if(targetItem.leftItem.id != xItem.id && targetItem.rightItem.id != xItem.id){
								xItem.x = tmpItem.x + tmpItem.w + interval 
								tmpItem = xItem
							}
						}else if(tool=='ttob'){
							if(targetItem.topItem.id != xItem.id && targetItem.bottomItem.id != xItem.id){
								xItem.y = tmpItem.y + tmpItem.h + interval
								tmpItem = xItem
							}
						}
					}
				}
			}
		}
	}
	
	ChooseComponentList.handleDrop = (parent,e)=>{
		e.preventDefault()
		let res = e.dataTransfer.getData("componentItem")
		let itemObj=null
		if(res){
			itemObj = JSON.parse(res)
		}else{
			res = e.dataTransfer.getData("treeItem")
			if(res.split(".")[0]=='component'){
				itemObj = {
				   id:'333',
				   name:"TCustomCmpt",
				   key:'TCustomCmpt',
				   group:'custom',
				   value:res,
				   icon:'local:custom',
				   model:'TCustomCmpt',
				   componentParams:{},
				   componentVars:{},
				   children:[]
				}
			}
		}
		if(itemObj){
			itemObj.id = (new Date()).getTime()+""
			if(!itemObj.hasOwnProperty('attribute')){
				itemObj.attribute = {}
			}
			if(itemObj.group =='layout'){
				itemObj.x = 0
				itemObj.w = appScreen.screenWidth
				itemObj.children = []
				itemObj.lastZindex = zindexLine
				itemObj.firstZindex = zindexLine
			}else if(itemObj.group == 'custom'){
				itemObj.x = 1
				itemObj.w = appScreen.screenWidth - 4
				itemObj.h = 140
			}else if(itemObj.group == 'list'){
				itemObj.x = 1
				itemObj.w = appScreen.screenWidth - 4
				itemObj.children = []
				itemObj.lastZindex = zindexLine
				itemObj.firstZindex = zindexLine
			}else{
				itemObj.x = e.offsetX
			}
			itemObj.y = e.offsetY
			itemObj.wUnit = 'px'
			itemObj.hUnit = 'px'
			itemObj.visible=true
			itemObj.enabled=true
			const xParentId = parent ? parent.Id : appBodyParentId
			if(ChooseComponentList.chooseMap.size > 0  && ChooseComponentList.parentId != xParentId){
				ChooseComponentList.chooseMap.clear()
			}
			ChooseComponentList.parentId = xParentId
			
			if(itemObj.model=='TCustomCmpt'){
				loadComponentByPath(itemObj.value,(data)=>{
					if(data && data.hasOwnProperty('instance') ){
						let varTexts= data.instance.varText
						let varObj = parse(varTexts,'component')
						if(varObj && varObj.length > 0){
							varObj.forEach(item=>{
								itemObj.componentParams[item.name] = {
									name:item.name,
									type:item.type,
									value:item.default,
									expression:''
								}
							})
							itemObj.componentVars = varObj
						}
						console.log("success varObj=",itemObj.componentParams)
					}
					ChooseComponentList.setKey(FileScript.createNewComponent(parent,itemObj))
				})
				return
			}else if(itemObj.model=='TTab'){
				itemObj.w = 100
				itemObj.wUnit = '%'
				itemObj.x = 0
				itemObj.y = 0
				itemObj.h = 100 
				itemObj.hUnit = '%'
				itemObj.zindex = 10
			}
			ChooseComponentList.setKey(FileScript.createNewComponent(parent,itemObj))
		}
	}
	
	function onBoardDragOver(e){
		e.preventDefault()
	}
	
	function onBoardDrop(e){
		ChooseComponentList.handleDrop({id:appBodyParentId},e)
	}
	
	const pressControlKey = ref(0)
	provide("pressControlKey",pressControlKey)
	
	function onDivKeyDown(e){
		if(pressControlKey.value == 0 && e.key == 'Control'){
			e.preventDefault()
			pressControlKey.value = 1
		}else if(e.key=='ArrowUp'){
			e.preventDefault()
			chooseItemMove(pressControlKey.value,0,-2)
		}else if(e.key=='ArrowDown'){
			e.preventDefault()
			chooseItemMove(pressControlKey.value,0,2)
		}else if(e.key=='ArrowLeft'){
			e.preventDefault()
			chooseItemMove(pressControlKey.value,-2,0)
		}else if(e.key=='ArrowRight'){
			e.preventDefault()
			chooseItemMove(pressControlKey.value,2,0)
		}else if(e.key=='Delete' || e.key == 'Backspace'){
			e.preventDefault()
			removeChooseItems()
		}else if(pressControlKey.value == 1 && e.key=='c'){
			handleAction('copy')
		}else if(pressControlKey.value == 1 && e.key=='v'){
			handleAction('paste')
		}
	}
	
	function onDivKeyUp(e){
		if(pressControlKey.value == 1 && e.key == 'Control'){
			e.preventDefault()
			pressControlKey.value = 0
		}
	}
	
	function chooseItemMove(ctrl,w,h){
		if(ChooseComponentList.chooseMap.size > 0 && (w || h != 0)){
			for (let key of ChooseComponentList.chooseMap.keys()){
				if(ctrl==1){
					if(w != 0) ChooseComponentList.chooseMap.get(key).w += w
					if(h != 0 )ChooseComponentList.chooseMap.get(key).h += h
				}else{
					if(w != 0) ChooseComponentList.chooseMap.get(key).x += w
					if(h != 0 )ChooseComponentList.chooseMap.get(key).y += h
				}
			}
			props.fileItem.isModify = true
		}
	}
	
	function removeChooseItems(){
		if(ChooseComponentList.chooseMap.size > 0 && FileScript.componentList.length > 0){
			for(let key of ChooseComponentList.chooseMap.keys()){
				const xItem = ChooseComponentList.chooseMap.get(key)
				if(xItem.hasOwnProperty("parent") && xItem.parent && xItem.parent.hasOwnProperty("children") && xItem.parent.children.length > 0){
					for(let r=xItem.parent.children.length - 1;r>=0;r--){
						if(xItem.parent.children[r].id == key){
							if(xItem.parent.key == 'TTab') break;
							xItem.parent.children.splice(r,1)
							props.fileItem.isModify = true
							break;
						}
					}
				}else{
					if(FileScript.componentList.length > 0){
						for(let r=FileScript.componentList.length - 1;r>=0;r--){
							if(FileScript.componentList[r].id == key){
								FileScript.componentList.splice(r,1)
								props.fileItem.isModify = true
								break;
							}
						}
					}
				}
			}
			ChooseComponentList.clear()
		}
	}
	
	function selectMoreItemByBox(){
		ChooseComponentList.chooseMap.clear()
		let leftLine = (chooseBox.x <= chooseBox.x1 ? chooseBox.x : chooseBox.x1) - chooseBox.offsetX
		let rightLine = (chooseBox.x <= chooseBox.x1 ? chooseBox.x1 : chooseBox.x) - chooseBox.offsetX
		let topLine = (chooseBox.y <= chooseBox.y1 ? chooseBox.y : chooseBox.y1) - chooseBox.offsetY
		let bottomLine = (chooseBox.y <= chooseBox.y1 ? chooseBox.y1 : chooseBox.y) - chooseBox.offsetY
		
		let childrenList = FileScript.componentList
		if(chooseBox.parent && chooseBox.parent.id != appBodyParentId){
			childrenList = chooseBox.parent.children
		}
		for(let r=0;r<childrenList.length;r++){
			let x = childrenList[r].x 
			let y = childrenList[r].y 
			let w = childrenList[r].w 
			let h = childrenList[r].h
			let needSetup = false
			if(x >= leftLine && x <= rightLine && y >= topLine && y <= bottomLine){
				needSetup = true
			}else if(x+w >= leftLine && x+w <= rightLine && y >= topLine && y <= bottomLine){
				needSetup = true
			}else if(x+w >= leftLine && x+w <= rightLine && y+h >= topLine && y+h <= bottomLine){
				needSetup = true
			}else if(x >= leftLine && x <= rightLine && y+h >= topLine && y+h <= bottomLine){
				needSetup = true
			}else if(x < leftLine && x + w > leftLine && ( (y > topLine && y < bottomLine) || (y+h > topLine && y+h < bottomLine))){
				needSetup = true
			}else if(x < rightLine && x + w > rightLine && ( (y > topLine && y < bottomLine) || (y+h > topLine && y+h < bottomLine))){
				needSetup = true
			}else if(y < topLine && y + h > topLine && ( (x > leftLine && x < rightLine) || (x+w > leftLine && x+w < rightLine))){
				needSetup = true
			}else if(y < bottomLine && y + h > bottomLine && ( (x > leftLine && x < rightLine) || (x+w > leftLine && x+w < rightLine))){
				needSetup = true
			}
			if(needSetup) ChooseComponentList.chooseMap.set(childrenList[r].id,childrenList[r])
			ChooseComponentList.notice()
		}
	}
	
	function getFourDirectItems(){
		let result = {
			leftItem:null,
			topItem:null,
			rightItem:null,
			bottomItem:null
		}
		for(let key of ChooseComponentList.chooseMap.keys()){
			const iItem = ChooseComponentList.chooseMap.get(key)
			// left
			if(!result.leftItem || result.leftItem.x > iItem.x){
				result.leftItem = iItem
			}
			//right
			if(!result.rightItem || result.rightItem.x + result.rightItem.w < iItem.x + iItem.w){
				result.rightItem = iItem
			}
			//top
			if(!result.topItem || result.topItem.y > iItem.y){
				result.topItem = iItem
			}
			//bottom
			if(!result.bottomItem || result.bottomItem.y + result.bottomItem.h < iItem.y + iItem.h){
				result.bottomItem = iItem
			}
		}
		return result
	}
	
	function handleAction(name){
		onCloseMenu()
		if(name=='copy'){
			rightMenu.copyResult = []
			if(ChooseComponentList.chooseMap.size > 0){
				rightMenu.minY = 10000
				rightMenu.minX = 10000
				rightMenu.maxY = 0
				rightMenu.totalH = 0
				for(let key of ChooseComponentList.chooseMap.keys()){
					let item = ChooseComponentList.chooseMap.get(key)
					if(rightMenu.minY > item.y){
						rightMenu.minY = item.y
					}
					if(rightMenu.maxY < item.y){
						rightMenu.maxY = item.y
					}
					if(rightMenu.minX > item.x){
						rightMenu.minX = item.x
					}
					rightMenu.totalH += item.h
					rightMenu.copyResult.push(item)
				}
				
				if(rightMenu.copyResult.length > 0){
					rightMenu.offsetX = rightMenu.minX
					rightMenu.offsetY = rightMenu.maxY + rightMenu.totalH + 10
					rightMenu.parent = rightMenu.copyResult[0].parent
				}
			}
		}else if(name=='paste'){
			if(rightMenu.parent  && rightMenu.copyResult.length > 0){
				for(let n=0;n<rightMenu.copyResult.length;n++){
					let newItem = replayParent(rightMenu.copyResult[n],rightMenu.parent,1,n)
					newItem.y = rightMenu.offsetY + (newItem.y - (rightMenu.minY==10000 || rightMenu.minY==0 ? 15 : rightMenu.minY))
					newItem.x = rightMenu.offsetX + (newItem.x - (rightMenu.minX==10000 || rightMenu.minX==0 ? 15 : rightMenu.minX))
					if(rightMenu.parent.id==appBodyParentId){
						FileScript.componentList.push(newItem)
						props.fileItem.isModify = true
					}else{
						if(rightMenu.parent.hasOwnProperty("children")){
						   rightMenu.parent.children.push(newItem)
						   props.fileItem.isModify = true
						}else{
							break;
						}
					}
				}
			}
			rightMenu.copyResult = []
		}else if(name=='delete'){
			removeChooseItems()
		}else if(name=='refresh'){
			
		}
	}
	
	function replayParent(target,parent,level,row){
		 let result = {}
         Object.keys(target).forEach(key => {
			if(key=='children'){
				result.children = []
				for(let c=0;c<target.children.length;c++){
					result.children.push(replayParent(target.children[c],result,level+1,c))
				}
			}else if(key=='parent'){
				result.parent = parent
			}else if(key=='id'){
				result.id = (new Date()).getTime() + '' + level +'' + row
			}else{
				if(typeof target[key] =='string' || typeof target[key] =='number' || typeof target[key]=='boolean' ){
					result[key] = target[key]
				}else if(typeof target[key] == 'object'){
					result[key] = JSON.parse(JSON.stringify(target[key]))
				}
			}
			
		 });
		 result.name = cmptNameMap.getName(result.key)
		 return result
	}
	
	const fileVersion = ref(0)
	
	async function updateFile(success){
		let json = releaseFile(FileScript)
		json['realHeight'] = appScreen.realHeight+'px'
		console.log("save json:",json)
		let res = await apiUpdateFile({
			projectId:ProjectRef.BasicData.ProjectId,
			version:fileVersion.value,
			path:props.fileItem.fileItem.filePath,
			_json:json
		})
		if(res.code==200){
			// console.log(res)
			if(res.data.hasOwnProperty("success") && res.data.success==false){
				ElMessage({
					message:res.message,
					type:"error"
				})
			}else{
				props.fileItem.isModify = false
				fileVersion.value++
				if(success) success()
				ElMessage({
					message:res.message,
					type:"success"
				})
			}
		}
	}
	
	function addParent(cptList,parent){
		if(cptList.length > 0){
			for(let n=0;n<cptList.length;n++){
				cptList[n].parent = parent
				cmptNameMap.setName(cptList[n].name)
				if(cptList.hasOwnProperty("children") && cptList.children.length > 0){
					addParent(cptList[n].children,cptList[n])
				}
			}
		}
	}
	
	async function loadComponentByPath(path,success) {
		let res = await apiLoadFileJson({
			projectId:ProjectRef.BasicData.ProjectId,
			path:path
		})
		if(res.code==200){
			if(res.data.content.trim() != ''){
				if(success) success(JSON.parse(res.data.content))
			}
		}
	}
	
	async function loadJsonByPath(){
		let res = await apiLoadFileJson({
			projectId:ProjectRef.BasicData.ProjectId,
			path:props.fileItem.fileItem.filePath
		})
		if(res.code==200){
			fileVersion.value = res.data.version
			if(res.data.content){
				let tmpJson = JSON.parse(res.data.content)
				if(tmpJson){
					if(tmpJson.hasOwnProperty("realHeight")){
						if(tmpJson.realHeight.slice(-2)=='px'){
							appScreen.realHeight = parseInt(tmpJson.realHeight.slice(0,-2))
						}
					}
					FileScript.fileId = tmpJson.fileId
					FileScript.filePath = tmpJson.filePath
					FileScript.params = tmpJson.params
					FileScript.instance.varText = tmpJson.instance.varText 
					FileScript.attribute = tmpJson.attribute
					FileScript.triggers = tmpJson.triggers
					FileScript.componentList = tmpJson.componentList
					cmptNameMap.names.clear()
					addParent(FileScript.componentList,{id:appBodyParentId})
					FileScript.refreshTree()
					ProjectRef.funcObj.sendLog("加载文件："+props.fileItem.fileItem.filePath)
					nextTick(()=>{
						props.fileItem.isModify = false
						ChooseComponentList.notice()
					})
				}
			}else{
				nextTick(()=>{
					props.fileItem.isModify = false
					ChooseComponentList.notice()
				})
			}
		}
	}

</script>

<style lang="scss" scoped>
	.content {
		.content-box {
			position: relative;
			overflow: hidden;

			.app-box {
				position: absolute;
				top: 0px;
				left: 0px;
				height: calc(100% - 3px);
				display: flex; 
				flex-direction: column;
				justify-content: space-between;
				align-items: stretch;
				overflow: hidden;
				background-color: #eaeaea;
			
				.app-bar{
					background-color: white;
					height:40px;
					border-bottom: 1px solid #d9d9d9;
					// position: relative;
					display: flex;
					justify-content: space-between;
					align-items: center;
					white-space: nowrap;
					
					.app-screen-value {
						flex:1;
					}
					.app-tools {
						width:256px;
						text-align: right;
						.tool-disabled {
							width: 21px;
							height: 21px;
							margin:2px 5px;
						}
						.tool-btn {
							width: 21px; 
							height: 21px;
							cursor:pointer;
							margin:2px 5px;
						}
						.tool-btn:hover{
							transform: scale(1.1);
						}
						.tool-btn:active{
							transform: scale(0.97);
						}
					}
				}
				.app-body {
					flex:1;
					position: relative;
					overflow: auto;
					
					.app-content{
						position: absolute;
						top:1px;
						left:1px;
						
						.app-shadow {
							background-color: #8c8c8c;
							position: absolute;
							top:20px;
							border-radius: 4px;
						}
						
						.app-board {
							background-color: white;
							top:40px;
							position: absolute;
							overflow: scroll;
							.choose-auto-box{
								position: absolute;
								border:1px dotted #00aaff;
								z-index:990;
							}
						}
						.app-down-line{
							position: absolute;
							width:50px;
							height:5px;
							background-color: #00aaff;
							border-radius: 4px;
							cursor:row-resize;
						}
					}
				}
				.app-body:focus{
					outline: none; 
					box-shadow: 0 0 0 2px white; 
				}
				.app-param {
					position: absolute;
					top:0px;
					left:0px;
					width:calc(100% - 3px);
					height:100px;
					border: 1px solid #dddddd;
					z-index: 998;
					background-color: #f5f5f5;
					overflow: auto;
					
					.oper-btn{
						display: flex;
						justify-content: center;
						align-items: center;
						width:80px;
						font-size: 14px;
						color: #00aaff;
						cursor: pointer;
					}
					.oper-btn:hover{
						font-size: 15px;
					}
					.oper-btn:active{
						font-size: 13px;
					}
					
					.param-header {
						display: flex;
						justify-content: space-between;
						align-items: center;
						background-color: white;
						padding:8px 1px;
						.header-name{
							flex:1;
							border-right:1px solid #ececec
						}
						.header-type{
							width:150px;
							border-right:1px solid #ececec
						}
						.header-value{
							flex:1;
							border-right:1px solid #ececec
						}
					}
					.param-row {
						display: flex;
						justify-content: space-between;
						align-items: center;
						background-color: white;
						border-top: 1px solid #e7e7e7;
						padding:8px 1px;
						overflow: auto;
						.row-name {
							flex:1;
							// border:1px solid red;
						}
						:deep(.el-input){
							// flex:1;
							width: calc(100% - 8px);
							margin:0px 3px;
						}
						.row-type {
							width:150px;
						}
						.row-value {
							flex:1;
						}
					}
				}
			}

			.drag-v-line {
				position: absolute;
				top: 0px;
				height: 100%;
				width: 2px;
				background-color: #f0f0f0;
				z-index: 998;
				cursor: col-resize;
			}
			
			.drag-h-line {
				position: absolute;
				left: 0px;
				width: 100%;
				height: 2px;
				background-color: transparent;
				z-index: 998;
				cursor: row-resize;
			}

			.attrib-box {
				position: absolute;
				top: 0px;
				right: 0px;
				width: 100px;
				height: 100%;
			}
		}
	}
	.right-menu-box {
		position: fixed;
		width:100px;
		background-color: white;
		border:1px solid #d1d1d1;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		align-items: stretch;
		z-index: 999;
		padding:8px 5px;
		border-radius:6px;
		.menu-item {
			
		}
		.menu-item:hover{
			color: #00aaff;
		}
		
		.menu-copy{
			border-bottom:1px dotted #e7e7e7;
			padding:4px 0px;
			cursor:pointer;
		}
		.menu-paste{
			
			padding:4px 0px;
			cursor:pointer;
		}
		.menu-delete {
			border-top:1px dotted #e7e7e7;
			padding:4px 0px;
			cursor:pointer;
		}
	}
</style>