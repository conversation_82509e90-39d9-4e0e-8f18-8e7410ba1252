import{E as Ae}from"./el-button-B8TCQS4u.js";import{E as ye}from"./el-overlay-CF-bF7w3.js";import{E as be,a as _e}from"./el-progress-BWXbzDlP.js";import{E as xe,a as Ce,b as Ue}from"./el-tab-pane-BjqSQjs9.js";import"./el-scrollbar-DLTsEwUl.js";import{E as we}from"./el-checkbox-DnOi5Qa-.js";/* empty css                 */import{E as Ve}from"./el-message-box-BNmbnUjS.js";import{E as Ee,a as he}from"./el-select-DNFSsBSe.js";import{E as Fe,a as ke}from"./el-table-column-S0LD8t0E.js";import{E as Be,a as De}from"./el-form-item-Beaw8WQV.js";import{_ as Te,a as Ne}from"./list_icon_del-CcyIrAYi.js";import{p as Ie,c as Oe}from"./list_icon_comput-BNFIRqSF.js";import{_ as Re}from"./form_icon_del-D3IU39US.js";import{e as r,d as k,B as Me,c as A,a as n,b as t,f as a,F as O,h as Le,i as Se,o as d,C as J,g as y,k as i,t as v,n as Ke,D as Z,v as Qe,G as Ge,w as R,E as b}from"./index-Dgb0NZ1J.js";import{p as Ye,c as Xe,d as Pe,a as je,b as ze,u as qe,e as Je,f as Ze,g as He,h as We}from"./homeApi-CwzLWTxy.js";import{_ as $e}from"./UploadImg-DiG_g-wU.js";/* empty css                   */import{_ as el}from"./plugin-vue_export-helper-DlAUqK2U.js";import{E as ll}from"./index-C-aKrRv3.js";import{E as tl}from"./index-DzgKufUD.js";import"./vnode-CV7pw3rS.js";import"./refs-CFD094o4.js";import"./index-CINbulG0.js";import"./isEqual-CwZW0B1R.js";import"./castArray-BpiBxx45.js";const al="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAA45JREFUWEftmEFIFGEUx/9vXEnUQwcPdkohQncM7BAUBQoe0kNQEBQo5ClyZyM7ZRBk1MEgyMjZPBRsUBR08BJkkFAk5CHIgzPrQWi7CXXYQy5LuPPy22aXcfxmZ3d2FvbQwF52vu/N7/u/733fe4/Q4A81OB8qAxzkiNqHITD6mdFSzaKIkDZ0el7NHOdYX8CoxkPEmAfhUNCPMGPMTNDLIPPLAh6Z4IE8YZGoOtUkIONBVfQE7BrnltZWpIjQZX8ww4QXsPCrSiU2nOqpcT4JxhQz+oiQ2bGfXFuDjo+0LbPrCajG+TQYi/akzXweJ9bnKV0l3K7hvTG+phBmJTYWt7ZwLp2knPudN6DGNwDM2BNmDZ2u1wmuaFYK6QkYjfFtIkyL2cyYNhN0JyjgHuUYy5aFCSWCUeFuh909kHUHlMExMGIm6LcAcwphg+6CrCugbM/tnKMzZoJuOr0hgzQMnBGBUzfAMgEh3TISyLihk14XQJlbQRAuHS4qJ9vXqsZ3Adyyx3w1dDoWOqDXnstmsd3WhoVykIcvc09zM1KFwATSpk7doQL6BYQ4/L0gozFuJ+AdCKeKwWLoNBIaoB9c0bUySAD3wBh0wCGvYHD9MX0KBbBSOB/IUmBbjMlUgh6JP2oGrBbOD9IJVzNgUDj7gHbvObjhagIs5InAh5JfGMvOG6LctSgJCClcTYCqxp8BO+LqBFcr4E8AHcJIPo/uSlKxapQreiBwkKgalwCZccBM0GZYbnXaCQ4Y4++ws20/BYMoF4aC3wD02/niUTNBqzIFK4GLxniUCBFZ3RJYwWiMvxDhuICyLJxIPaEVN2AlcKrGlwAk7YXuqf4CA6oavwFw3oZ6all4RoScZSGjKMj9aUJkXx6vnNeX7Jzzy9xrASytvJJSQAbnzqhlKVhgQNv4AhHO+gF6wdUdUHygL84XLAsXAXTaBf7+nWpQtEfEb4WBB6ZOS16LCOxi1Vl2EuaMObrqp1SQ96rGDwFM2nOnDJ3uV3QOugr3jAX0p3T6EQTCa06vxgcVQER/Z2EMYdiYo/cVAUJ0tNRC+l1sGomb4jUzMmFAkoIOMMYAiC0hCql0Noted3ehbPNIZCxgvA2heVR2TczIWU0YFhm0e6Bv+61nggcUQtLRRApDQKeNDQaueAWSL6CwVKgj2jEQpIHptRpxqIOwaqxhyauz9W9bNvjzH7BWBzW8gn8BlMaGR7vFD6cAAAAASUVORK5CYII=",ol="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAA+lJREFUWEftmE9oHHUUxz9v0oQ2G6FCD4IVVxBpdrfYgsUePAREqCCIRNGDh0IDCTMRK3ootEeFHpS2dHdpIIUeelBMQUFQECEHDxULrtSd3YPgijn0IFhwd/sv2df8NjNhO5mZnTTTJYf+TsvO+837/L7ze+/3nRG2+ZBtzsdjwK0+oUemYPao7hwdxQHeB/aJsDMAuwxURLjcbDLXuCS3wxYTCTju6LOW8g1CNk4FVep37/LOn/Oy5Md158IPBiyhgvUOHKmV5O9gfCRgztaPRfg8SYKOcqpWls9MrFEuk+G3TcD5KeqtFgeDSkYDOvoyynci7OkDeROLiep5+d3EFWz9UIWz5rcqty2L2WaTK41LcrP3PjlbxxSOCZz2H78qn7hl+aI3Ln4PTuiO7AHGMveYBk4HQBdVmW+3u8nX90/e0V+Bl0xsRzheK8q5uAWO23rMEua7C4KKW5KDyQGBnK3TIlzombSkMLt6o2/DEucdvQfs8BR8wi1LMw4we1R3ZzL85yvulmVXYsCco6/K2mb3E14NFkQwed5R9f+rliRRl4ibE3kDb2XXgb3e6q4Cr/VTZGCAeUfPAMc9NW7cucOh3lYS9dgGApiz9SngL7+6OsJ7taJ8laTlDASwMKsnVfnUA6pUA5UVB5qz9Za/sFaLJ4PtJTjXtBsR/vf+X66WZLhvkRRsva5CoRsovF0typUk6pmYvKOmSR/w2sxUrSgX4+b29k3gWrUkh/oC+klU+bfd5pmoczIsce8J5DXqE50OF4PFZYpwbJTJDhR7ttKGvhlaxftmNGtZTCL87Jbkl6TqmbiBHXWbgQrGmgUODfH9Js7j+soKr9cvSCN4r0SN9GFgjZK7Rpm2pGu3zJ7sNnt/mMcvQl2Vy+02pU3brbT83MMsrm+RpOnnUgdMe5P3A8yt2bpXVtvaQiLDmrafiwP0ttE/xnOqUnHLD1qttTYcGGn7uTjA/KxOoiyYGBX+cIuyv28Vp+3nYgEfPHVO1Yprrw2xRZL2YR8FWLD1XRW+7KqnGEf+nFuWG9sC8IUZfXp4iGuAcU1mnK2W5KOwxYTtwVQdcTCpcS8W/KjCYe/aUqvF/ijXM1BAo9yIxUIP3LLCEbckP0VthQ2Aafs5P3HO0TcFiv4rhLf3ZtyyzMUVUpiCqfk5r89NijAFTARATrSGmWtUaLIo5jNI6AhTcP2Lwlb8XP4DfZEOi6sFsDtOIeM5Ed6IsnUbANM66sZtPWnJ+mtDHKNpMxu+KPgTIg3rVv3c81O6d2SEr0X6fkBqrKzwVpgXNJBx78Wp+LlY6RJcfGSGNUHuRCGPARPJFBO07RW8D528KEchtA0vAAAAAElFTkSuQmCC",nl="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAAelJREFUWEftmEFKw0AUhv8HLhR04R1cSOIBFCweQUF3FnSdeIAuFCt20QOYrBXqTkGPIBX0ACa48A4uLOhCeDI2LaVJZzKTZkihXYXm9c2X/03e/H2Ein+o4nyYAxatkFJBx+NDIixEAd2oFisjVgro+nwE4FqAMaMeh3Q7CbKsWCmg4/E5EZoJYDMO6WISYFmxswvonvA+GG0Aa4lq7ww04oAes1TMq6Bu3kwFXZ8vAZxOKGcrCuhs/F4eQJO8KUDH510CHqRvLOEguqL70RgVoGneFKDrcxfAtlh8ZwNo1fsYpx3g6a1/zYzXOKQtHUDTvFmAXwCWxeLdNrD6fwV89oBaYwj4E4e0pAPoePxNhEXdvOkSGyZSljhHXgC9KKCV0QfPAnwhwqaixM9xSDVNBY3ypkvcby93speEgb3xdqNSMGkv2nlnr80MlEvagmjU68l3HyA0xtvLMD7nsaibd3aPOqGMal/pvCSmsXMFixgL8dupKKjrUHS2TmFAE4diDdDUoVgDNHUoNgFLcT5Ss6DTr0wtlDUFHY+NHIo1QFOHYg1QbIdKt5mync/UTpIyjUXhk0TXD+o+jApQTLY6CcSxbMKVTLamHqscv4mpFTN+ZZOtgYplxCoBZX+ebNybAxZVufIK/gG9vq1HywOEJwAAAABJRU5ErkJggg==",sl={class:"flex_box filter_head"},rl={class:"project_list"},il=["src"],dl={style:{"font-weight":"600","font-size":"18px",color:"#212121"}},ul={style:{display:"flex","align-items":"center","margin-top":"16px"}},pl={key:0,src:Ie,style:{width:"20px",height:"20px"}},ml={key:1,src:Oe,style:{width:"20px",height:"20px"}},cl={style:{margin:"0 8px 0 4px","font-size":"16px",color:"#666666"}},gl={class:"desc"},fl={style:{display:"flex","justify-content":"flex-end"}},vl={style:{"text-align":"center","font-weight":"600","font-size":"18px"}},Al={key:1,class:"flex_box"},yl={style:{display:"flex","justify-content":"center"}},bl={style:{display:"flex","justify-content":"center"}},_l={__name:"projectDesignList",setup(xl){const H=Le(),W={background:"#FAFAFA",fontWeight:600,fontSize:"16px",height:"40px",color:"#000"},$={fontSize:"14",height:"40px",color:"#000"},ee=r([{label:"全部",value:""},{label:"手机",value:"0"},{label:"电脑",value:"1"}]),le=r([{label:"全部",value:""},{label:"直属",value:"0"},{label:"参与",value:"1"},{label:"克隆",value:"2"}]),u=k({type:"",clientType:"",pageNo:1,pageSize:10}),M=r([]),x=r(!1),L=r(),s=r({clientType:"",icon:"",name:"",description:""}),te=k({clientType:[{required:!0,message:"请选择类型",trigger:"change"}],icon:[{required:!0,message:"请上传图片",trigger:"change"}],name:[{required:!0,message:"请输入项目名称",trigger:"change"}]}),C=r(2),U=r("first"),B=r([]),F=r(!1),w=r(""),D=r({}),V=r(!1),S=r(),p=k({id:"",mobile:"",copyName:""}),ae=k({mobile:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请检查手机号码是否有误",trigger:"change"}],copyName:[{required:!0,message:"请输入",trigger:"change"}]}),K=r(0),Q=()=>{u.pageNo=1,_()},oe=()=>{p.id="",p.mobile="",p.copyName=""},ne=()=>{S.value.validate(async o=>{if(o){console.log(p);try{await He(p),b({type:"success",message:s.value.id?"编辑成功":"创建成功"}),_(),V.value=!1}catch(e){console.log(e)}}})},se=async o=>{try{const e=await Xe(o.id);D.value=e.data,p.copyName=e.data.copyName,p.id=e.data.projectId,V.value=!0}catch{}},re=async()=>{if(!w.value){b.error("请输入授权账号");return}try{await je({mobile:w.value,projectId:s.value.id}),b({type:"success",message:"添加成功"}),w.value="",E()}catch{}},ie=async o=>{try{await ze(o.id),b({type:"success",message:"删除成功"}),E()}catch{}},de=o=>{tl.confirm("项目删除后将不可恢复，请谨慎！确定删除吗？",`删除-${o.name}`,{confirmButtonText:"确定",cancelButtonText:"取消",center:!0}).then(async()=>{try{await Pe(o.id),_(),b({type:"success",message:"删除成功"})}catch{}})},E=async()=>{try{const o=await We(s.value.id);B.value=o.data.items}catch{}},G=async(o,e)=>{s.value=e||{},C.value=o,x.value=!0,o===2&&E()},ue=()=>{U.value="first",s.value={}},pe=async()=>{if(C.value===1||U.value==="first")L.value.validate(async o=>{if(o)try{s.value.id?await qe(s.value):await Je(s.value),b({type:"success",message:s.value.id?"编辑成功":"创建成功"}),_(),x.value=!1}catch(e){console.log(e)}});else try{await Ze({data:B.value}),b({type:"success",message:"保存成功"}),F.value=!1,E()}catch{E()}},_=async()=>{try{const o=await Ye(u);M.value=o.data.items,K.value=o.data.meta.total}catch{}},me=async o=>{H.push({path:"/designFrame",query:{projectId:o.id}})};return Me(()=>{_()}),(o,e)=>{const Y=he,X=Ee,g=De,T=Be,m=Ae,f=ke,P=Fe,ce=Ve,j=Ce,h=ll,N=we,ge=Ue,fe=xe,z=_e,ve=be,q=ye,I=Se("debounce");return d(),A(O,null,[n("div",sl,[t(T,{model:u,inline:!0},{default:a(()=>[t(g,{label:"类型"},{default:a(()=>[t(X,{modelValue:u.clientType,"onUpdate:modelValue":e[0]||(e[0]=l=>u.clientType=l),placeholder:u.clientType===""?"全部":"请选择",style:{width:"111px"},onChange:Q},{default:a(()=>[(d(!0),A(O,null,J(ee.value,l=>(d(),y(Y,{key:l.value,label:l.label,value:l.value||""},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder"])]),_:1}),t(g,{label:"所属"},{default:a(()=>[t(X,{modelValue:u.type,"onUpdate:modelValue":e[1]||(e[1]=l=>u.type=l),placeholder:u.type===""?"全部":"请选择",style:{width:"111px"},onChange:Q},{default:a(()=>[(d(!0),A(O,null,J(le.value,l=>(d(),y(Y,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder"])]),_:1})]),_:1},8,["model"]),t(m,{type:"primary",class:"create_btn",onClick:e[2]||(e[2]=l=>G(1))},{icon:a(()=>e[18]||(e[18]=[n("img",{src:Te,style:{width:"18px",height:"18px"}},null,-1)])),default:a(()=>[e[19]||(e[19]=i(" 新建项目 "))]),_:1,__:[19]})]),n("div",rl,[t(P,{data:M.value,"show-header":!1},{default:a(()=>[t(f,{width:"112px"},{default:a(l=>[n("img",{style:{width:"88px",height:"88px","border-radius":"8px"},src:l.row.icon},null,8,il)]),_:1}),t(f,null,{default:a(l=>[n("div",dl,v(l.row.name),1),n("div",ul,[l.row.clientType===0?(d(),A("img",pl)):(d(),A("img",ml)),n("span",cl,v(l.row.clientType===0?"手机":"电脑"),1),n("div",{class:Ke(l.row.type===0?"tag_primary":"tag_primary tag_success")},v(["直属","参与","克隆"][l.row.type]),3)])]),_:1}),t(f,null,{default:a(l=>[n("div",gl,"创建于："+v(l.row.createTime),1),e[20]||(e[20]=n("div",{class:"desc"},"版本号：V0.0.001",-1))]),_:1}),t(f,{width:"500px"},{default:a(({row:l})=>[t(m,{type:"primary",text:"",onClick:c=>me(l),disabled:l.design===0},{icon:a(()=>e[21]||(e[21]=[n("img",{class:"btn_icon",src:al},null,-1)])),default:a(()=>[e[22]||(e[22]=i(" 设计 "))]),_:2,__:[22]},1032,["onClick","disabled"]),t(m,{type:"primary",text:"",onClick:c=>se(l),disabled:l.clone===0},{icon:a(()=>e[23]||(e[23]=[n("img",{class:"btn_icon",src:ol},null,-1)])),default:a(()=>[e[24]||(e[24]=i(" 克隆 "))]),_:2,__:[24]},1032,["onClick","disabled"]),t(m,{type:"primary",text:"",onClick:c=>G(2,l),disabled:l.manager===0},{icon:a(()=>e[25]||(e[25]=[n("img",{class:"btn_icon",src:nl},null,-1)])),default:a(()=>[e[26]||(e[26]=i(" 管理 "))]),_:2,__:[26]},1032,["onClick","disabled"]),t(m,{type:"danger",text:"",onClick:c=>de(l),disabled:l.delete===0},{icon:a(()=>e[27]||(e[27]=[n("img",{class:"btn_icon",src:Ne},null,-1)])),default:a(()=>[e[28]||(e[28]=i(" 删除 "))]),_:2,__:[28]},1032,["onClick","disabled"])]),_:1})]),_:1},8,["data"])]),n("div",fl,[t(ce,{"current-page":u.pageNo,"onUpdate:currentPage":e[3]||(e[3]=l=>u.pageNo=l),"page-size":u.pageSize,layout:"total, prev, pager, next",total:K.value,onCurrentChange:_},{total:a(({total:l})=>[i(" 共 "+v(l)+" 条数据 ",1)]),_:1},8,["current-page","page-size","total"])]),t(q,{modelValue:x.value,"onUpdate:modelValue":e[13]||(e[13]=l=>x.value=l),"destroy-on-close":"",width:"700",onClose:ue},{header:a(()=>[n("div",vl,v(C.value===1?"新建项目":`项目管理-【${s.value.id}】${s.value.name}`),1)]),footer:a(()=>[n("div",yl,[t(m,{onClick:e[12]||(e[12]=l=>x.value=!1)},{default:a(()=>e[35]||(e[35]=[i("取消")])),_:1,__:[35]}),R((d(),y(m,{type:"primary",style:{color:"#fff"}},{default:a(()=>e[36]||(e[36]=[i(" 保存 ")])),_:1,__:[36]})),[[I,pe]])])]),default:a(()=>[C.value===2?(d(),y(fe,{key:0,modelValue:U.value,"onUpdate:modelValue":e[7]||(e[7]=l=>U.value=l)},{default:a(()=>[t(j,{label:"基本资料",name:"first"}),t(j,{label:"授权用户",name:"second"},{default:a(()=>[F.value?(d(),A("div",Al,[n("div",null,[t(h,{modelValue:w.value,"onUpdate:modelValue":e[5]||(e[5]=l=>w.value=l),placeholder:"请输入授权账号",style:{width:"200px"}},null,8,["modelValue"]),R((d(),y(m,{type:"primary",style:{"margin-left":"12px"}},{default:a(()=>e[30]||(e[30]=[i(" 加入 ")])),_:1,__:[30]})),[[I,re]]),t(m,{onClick:e[6]||(e[6]=l=>F.value=!1)},{default:a(()=>e[31]||(e[31]=[i("取消")])),_:1,__:[31]})])])):(d(),A("div",{key:0,class:"add_box",onClick:e[4]||(e[4]=l=>F.value=!0)},[t(m,{icon:Qe(Ge)},null,8,["icon"]),e[29]||(e[29]=n("span",null,"增加授权用户",-1))])),t(P,{data:B.value,border:"","table-layout":"auto","header-cell-style":W,"cell-style":$,style:{"margin-top":"16px"}},{default:a(()=>[t(f,{prop:"id",label:"编号"}),t(f,{prop:"mobile",label:"账号"}),t(f,{prop:"nickName",label:"名称"}),t(f,{label:"权限"},{default:a(l=>[t(N,{"true-value":1,"false-value":0,modelValue:l.row.design,"onUpdate:modelValue":c=>l.row.design=c,label:"设计"},null,8,["modelValue","onUpdate:modelValue"]),t(N,{"true-value":1,"false-value":0,modelValue:l.row.clone,"onUpdate:modelValue":c=>l.row.clone=c,label:"克隆"},null,8,["modelValue","onUpdate:modelValue"]),t(N,{"true-value":1,"false-value":0,modelValue:l.row.manager,"onUpdate:modelValue":c=>l.row.manager=c,label:"管理"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),t(f,{label:"操作"},{default:a(l=>[t(ge,{title:"确认删除?",onConfirm:c=>ie(l.row)},{reference:a(()=>e[32]||(e[32]=[n("img",{src:Re,style:{width:"20px",height:"20px",cursor:"pointer"}},null,-1)])),_:2},1032,["onConfirm"])]),_:1})]),_:1},8,["data"])]),_:1})]),_:1},8,["modelValue"])):Z("",!0),C.value===1||U.value==="first"?(d(),y(T,{key:1,ref_key:"createFormRef",ref:L,model:s.value,rules:te,"label-width":"100px","status-icon":"","label-position":"left"},{default:a(()=>[t(g,{label:"类型",prop:"clientType"},{default:a(()=>[t(ve,{modelValue:s.value.clientType,"onUpdate:modelValue":e[8]||(e[8]=l=>s.value.clientType=l)},{default:a(()=>[t(z,{value:0},{default:a(()=>e[33]||(e[33]=[i("手机应用")])),_:1,__:[33]}),t(z,{value:1},{default:a(()=>e[34]||(e[34]=[i("电脑应用")])),_:1,__:[34]})]),_:1},8,["modelValue"])]),_:1}),t(g,{label:"项目名称",prop:"name"},{default:a(()=>[t(h,{modelValue:s.value.name,"onUpdate:modelValue":e[9]||(e[9]=l=>s.value.name=l),placeholder:"请输入项目名称，必填"},null,8,["modelValue"])]),_:1}),t(g,{label:"介绍",prop:"description"},{default:a(()=>[t(h,{modelValue:s.value.description,"onUpdate:modelValue":e[10]||(e[10]=l=>s.value.description=l),placeholder:"请输入内容",type:"textarea"},null,8,["modelValue"])]),_:1}),t(g,{label:"图片",prop:"icon"},{default:a(()=>[t($e,{placeholder:"应用图标",modelValue:s.value.icon,"onUpdate:modelValue":e[11]||(e[11]=l=>s.value.icon=l)},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])):Z("",!0)]),_:1},8,["modelValue"]),t(q,{modelValue:V.value,"onUpdate:modelValue":e[17]||(e[17]=l=>V.value=l),"destroy-on-close":"",width:"500",onClose:oe},{header:a(()=>e[37]||(e[37]=[n("div",{style:{"text-align":"center","font-weight":"600","font-size":"18px"}}," 克隆项目 ",-1)])),footer:a(()=>[n("div",bl,[t(m,{onClick:e[16]||(e[16]=l=>V.value=!1)},{default:a(()=>e[38]||(e[38]=[i("取消")])),_:1,__:[38]}),R((d(),y(m,{type:"primary",style:{color:"#fff"}},{default:a(()=>e[39]||(e[39]=[i(" 保存 ")])),_:1,__:[39]})),[[I,ne]])])]),default:a(()=>[t(T,{ref_key:"cloneFormRef",ref:S,model:p,rules:ae,"label-width":"100px","status-icon":"","label-position":"left"},{default:a(()=>[t(g,{label:"当前项目"},{default:a(()=>[i(v(D.value.projectId)+" | "+v(D.value.currentProjectName),1)]),_:1}),t(g,{label:"目标用户",prop:"mobile"},{default:a(()=>[t(h,{modelValue:p.mobile,"onUpdate:modelValue":e[14]||(e[14]=l=>p.mobile=l),placeholder:"请输入用户手机号",maxlength:11},null,8,["modelValue"])]),_:1}),t(g,{label:"项目名称",prop:"copyName"},{default:a(()=>[t(h,{modelValue:p.copyName,"onUpdate:modelValue":e[15]||(e[15]=l=>p.copyName=l),placeholder:"请输入项目名称"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])],64)}}},ql=el(_l,[["__scopeId","data-v-3cdd44ce"]]);export{ql as default};
