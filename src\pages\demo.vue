<template>
  <el-button @click="handleOpenMsgBox">显示自定义消息框</el-button>
</template>

<script setup>
import { customMessageBox } from "@/common/utils";
import { ElMessage } from "element-plus";

const handleOpenMsgBox = () => {
  customMessageBox("自定义提示内容。。。")
    .then(() => {
      // 保存按钮逻辑
      ElMessage.success("点击了保存按钮");
    })
    .catch(() => {
      // 不保存按钮逻辑
      console.log("不保存");
      ElMessage.warning("点击了不保存按钮");
    });
};
</script>
