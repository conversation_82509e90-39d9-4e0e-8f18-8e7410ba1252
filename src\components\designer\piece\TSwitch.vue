<template>
	<div class="content" >
		<div class="content-outer" :style="{backgroundColor: (props.TItem.checked ? getActiveColor() : getDisActiveColor()),
	                borderRadius:props.TItem.h + 'px',justifyContent: props.TItem.checked ? 'flex-end':'flex-start'}">
			<div class="content-inner" :style="{borderRadius: (props.TItem.h  + 'px'),width:(props.TItem.h - 5)+'px',height:(props.TItem.h - 5)+'px'}"></div>
		</div>
	</div>
	
</template>

<script lang="ts" setup>
	import { reactive,ref ,onMounted,inject,watch} from 'vue';
	const ProjectRef = inject("ProjectRef")
	const ChooseComponentList = inject('ChooseComponentList')
	
	const props = defineProps({
		TItem:{
			type:Object,
			default:{}
		},
		TParent:{
			type:Object,
			default:{id:'10001'}
		},
		TBase:{
			type:Object,
			default:{
				checkEnabled:()=>{}
			}
		}
	})
	
	function getActiveColor(){
		if(!props.TBase.checkEnabled()) return "#b0b0b0"
		if(props.TItem.hasOwnProperty("attribute") && props.TItem.attribute.hasOwnProperty("proper") && props.TItem.attribute.proper.hasOwnProperty("activeColor")){
			return props.TItem.attribute.proper.activeColor
		}
		return "#00aaff"   
	}
	
	function getDisActiveColor(){
		if(props.TItem.hasOwnProperty("attribute") && props.TItem.attribute.hasOwnProperty("proper") && props.TItem.attribute.proper.hasOwnProperty("disActiveColor")){
			return props.TItem.attribute.proper.disActiveColor
		}
		return "#dfdfdf"
	}
	
	onMounted(()=>{
		props.TItem.parent = props.TParent
	})
	
</script>

<style lang="scss" scoped>
	.content{
		height:calc(100%);
		width: calc(100%);
		// padding:2px;
		
    	.content-outer{
			padding:4px;
			display: flex;
			align-items: center;
			justify-content: flex-start;
			height:calc(100% - 10px);
			width: calc(100% - 10px);
			margin-bottom:3px;
			.content-inner{
				background-color: white;
				
			}
		}
		
		
	}
</style>