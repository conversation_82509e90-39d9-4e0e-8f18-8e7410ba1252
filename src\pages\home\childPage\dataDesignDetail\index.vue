<template>
  <el-container>
    <el-header>
      <div class="flex_box head">
        <div class="back">
          <el-button
            style="font-size: 16px; color: #212121"
            :icon="Close"
            link
            @click="handleGoBack"
          >
            退出
          </el-button>
          <span class="title">数据表管理</span>
        </div>
        <el-button
          :icon="Plus"
          type="primary"
          color="#2B6BFF"
          @click="dialogVisible = true"
        >
          新建表
        </el-button>
      </div>
    </el-header>
    <el-container class="design_container">
      <el-aside width="300px">
        <div class="tree_box">
          <el-input
            v-model="inputValue"
            placeholder="输入关键字进行过滤"
            style="margin-bottom: 16px"
            @input="handleInput"
          />
          <el-tree
            ref="tree"
            default-expand-all
            :data="treeData"
            :props="defaultProps"
            :filter-node-method="filterNode"
            :expand-on-click-node="false"
            @node-click="handleNodeClick"
          >
            <template #default="{ node, data }">
              <div class="flex_box" style="width: 100%">
                <div>
                  <el-icon v-if="node.level === 1"><Coin /></el-icon>
                  <el-icon v-else><Document /></el-icon>
                  <span style="margin-left: 8px; transform: translateY(-8px)">
                    {{ node.label }}
                  </span>
                </div>
                <el-icon
                  v-if="node.level === 2 && nodeData.id === data.id"
                  @click.stop="handleDelete"
                  style="right: 10px; position: absolute;background-color: #d9e4ff;"
                  
                  color="#666666"
                >
                  <Close />
                </el-icon>
              </div>
            </template>
          </el-tree>
        </div>
      </el-aside>
      <el-main>
        <div class="main_box">
          <!-- 数据库tab -->
          <el-tabs v-if="nodeData.level === 1" v-model="level1ActiveName">
            <el-tab-pane label="数据源" name="first">
              <dataBaseDetail :databaseValue="databaseValue" />
            </el-tab-pane>
            <el-tab-pane label="授权项目" name="second">
              <authorizeProject
                v-if="level1ActiveName === 'second'"
                :headerCellStyleData="headerCellStyle"
                :cellStyleData="cellStyle"
              />
            </el-tab-pane>
            <el-tab-pane label="授权用户" name="third">
              <authorizeUser
                v-if="level1ActiveName === 'third'"
                :headerCellStyleData="headerCellStyle"
                :cellStyleData="cellStyle"
              />
            </el-tab-pane>
          </el-tabs>
          <!-- 数据表tab -->
          <el-tabs v-else v-model="level2ActiveName">
            <el-tab-pane label="表信息" name="fourth">
              <datatableDetail
                :dataTableValue="dataTableValue"
                @openDialog="handleOpenDialog"
              />
            </el-tab-pane>
            <el-tab-pane label="字段" name="fifth">
              <fieldList
                v-if="level2ActiveName === 'fifth'"
                :dataTableValue="dataTableValue"
                :tableId="nodeData.id"
                :headerCellStyleData="headerCellStyle"
                :cellStyleData="cellStyle"
              />
            </el-tab-pane>
            <el-tab-pane label="索引" name="sixth">
              <indexList
                v-if="level2ActiveName === 'sixth'"
                :dataTableValue="dataTableValue"
                :tableId="nodeData.id"
                :headerCellStyleData="headerCellStyle"
                :cellStyleData="cellStyle"
              />
            </el-tab-pane>
            <el-tab-pane label="查询" name="seventh">
              <query
                v-if="level2ActiveName === 'seventh'"
                :dataTableValue="dataTableValue"
                :tableId="nodeData.id"
                :headerCellStyleData="headerCellStyle"
                :cellStyleData="cellStyle"
              />
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-main>
    </el-container>
  </el-container>

  <!-- 新建/编辑表 -->
  <el-dialog
    v-model="dialogVisible"
    destroy-on-close
    width="500"
    @close="handleResetForm"
  >
    <template #header>
      <div style="text-align: center; font-weight: 600; font-size: 18px">
        {{ formData.id ? "编辑表" : "新建表" }}
      </div>
    </template>
    <el-form
      ref="dialogFormRef"
      :model="formData"
      :rules="rules"
      label-width="70px"
      status-icon
      label-position="left"
    >
      <el-form-item label="表名" prop="name">
        <el-input v-model="formData.name" :disabled="formData.id !== ''" />
      </el-form-item>
      <el-form-item label="中文名" prop="fullname">
        <el-input v-model="formData.fullname" />
      </el-form-item>
      <el-form-item label="备注" prop="description">
        <el-input v-model="formData.description" type="textarea" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div style="display: flex; justify-content: center">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" v-debounce="handleSubmit">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, onMounted, reactive } from "vue";
import { useRoute, useRouter } from "vue-router";
import { Close, Plus, Coin, Document } from "@element-plus/icons-vue";
import {
  dataDesignTree,
  databaseDetail,
  dataTableDetail,
  createTable,
  saveTable,
  removeTable,
} from "@/apis/homeApi";
import { ElMessage, ElMessageBox } from "element-plus";
import authorizeProject from "./components/authorizeProject.vue";
import authorizeUser from "./components/authorizeUser.vue";
import dataBaseDetail from "./components/dataBaseDetail.vue";
import datatableDetail from "./components/datatableDetail.vue";
import fieldList from "./components/fieldList.vue";
import indexList from "./components/indexList.vue";
import query from "./components/query.vue";

const route = useRoute();
const router = useRouter();
const tree = ref(null);
const dialogFormRef = ref();

const rules = reactive({
  fullname: [{ required: true, message: "请输入中文名", trigger: "change" }],
  name: [{ required: true, message: "请输入表名", trigger: "change" }],
  description: [{ required: true, message: "请输入备注", trigger: "change" }],
});
const dialogVisible = ref(false);
const formData = ref({
  id: "",
  fullname: "",
  name: "",
  description: "",
});
const inputValue = ref("");
const treeData = ref([]); //树形数据
const databaseValue = ref({}); //数据库详情
const dataTableValue = ref({}); //数据表详情
const defaultProps = {
  children: "children",
  label: "label",
};
const headerCellStyle = {
  background: "#FAFAFA",
  fontWeight: 600,
  fontSize: "16px",
  height: "40px",
  color: "#000",
};
const cellStyle = {
  fontSize: "14",
  height: "40px",
  color: "#000",
};
const level1ActiveName = ref("first");
const level2ActiveName = ref("fourth");
//当前选中树形节点数据
const nodeData = reactive({
  id: 0,
  label: "",
  level: 1, //当前选中节点级别 1数据库 2数据表
});

const handleResetForm = () => {
  formData.value = {
    fullname: "",
    name: "",
    description: "",
    id: "",
  };
};

const handleOpenDialog = () => {
  formData.value = {
    ...dataTableValue.value,
  };
  dialogVisible.value = true;
};

const handleSubmit = () => {
  dialogFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (formData.value.id) {
          await saveTable(formData.value);
          fetchDataTableDetail(formData.value.id);
        } else {
          await createTable({
            ...formData.value,
            db_store_id: Number(route.query.dbSourceId),
          });
        }
        ElMessage({
          type: "success",
          message: formData.value.id ? "编辑成功" : "创建成功",
        });
        fetchTreeData();
        dialogVisible.value = false;
      } catch (error) {
        console.log(error);
      }
    }
  });
};

const handleGoBack = () => {
  router.go(-1);
};

const handleInput = (e) => {
  if (tree.value) {
    tree.value.filter(e);
  }
};

const filterNode = (value, data) => {
  if (!value) return true;
  return data.label.includes(value);
};

const fetchTreeData = async () => {
  try {
    const res = await dataDesignTree(route.query.dbSourceId);
    const childrenList = res.data.items.map((item) => {
      return {
        id: item.id,
        label: `${item.name} [${item.fullname}]`,
      };
    });
    treeData.value = [
      {
        id: databaseValue.value.id,
        label: databaseValue.value.fullname,
        children: childrenList,
      },
    ];
  } catch (error) {
    console.log(error);
  }
};

const fetchDatabaseDetail = async () => {
  try {
    const res = await databaseDetail(route.query.dbSourceId);
    databaseValue.value = res.data;
    fetchTreeData();
  } catch {}
};

const fetchDataTableDetail = async (tableId) => {
  try {
    const res = await dataTableDetail(tableId);
    dataTableValue.value = res.data;
  } catch {}
};

//树形节点点击
const handleNodeClick = (data, node) => {
  if (data.id === nodeData.id) {
    return;
  }
  nodeData.level = node.level;
  nodeData.label = data.label;
  nodeData.id = data.id;
  if (node.level === 2) {
    fetchDataTableDetail(data.id);
    level2ActiveName.value = "fourth";
  }
};

const handleDelete = () => {
  ElMessageBox.confirm(
    `表删除后将不可恢复，确定要删除表: [${nodeData.label.split(" ")[0]}]吗?`,
    "提示",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      center: true,
    }
  ).then(async () => {
    try {
      await removeTable(nodeData.id);
      fetchTreeData();
      nodeData.level = 1;
      ElMessage({
        type: "success",
        message: "删除成功",
      });
    } catch {}
  });
};

onMounted(() => {
  fetchDatabaseDetail();
});
</script>

<style lang="scss" scoped>
.el-header {
  padding: 0;
  height: auto;
}
.el-main {
  padding: 16px;
}
:deep(.el-descriptions__label) {
  font-size: 16px;
  color: #666666;
}
:deep(.el-descriptions__content) {
  font-weight: 600;
  font-size: 16px;
  color: #000000;
}
:deep(.el-tree-node__content) {
  height: 36px;
  line-height: 36px;
  font-weight: 600;
  font-size: 14px;
  color: #000000;
  border-radius: 6px;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #d9e4ff;
  // height: 36px;
  border-radius: 6px;
}
.head {
  box-sizing: border-box;
  padding: 0 24px;
  height: 64px;
  background: #ffffff;
  border-top: 1px solid #e5e5e5;
  border-bottom: 1px solid #e5e5e5;
  .back {
    display: flex;
    align-items: center;
    .title {
      display: inline-block;
      border-left: 2px solid #e6e6e6;
      padding-left: 16px;
      margin-left: 16px;
      font-weight: 600;
      font-size: 16px;
      color: #212121;
      line-height: 19px;
    }
  }
}
.design_container {
  height: calc(100vh - 124px);
}
.tree_box {
  box-sizing: border-box;
  background: #ffffff;
  height: 100%;
  padding: 16px;
}
.main_box {
  box-sizing: border-box;
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  height: 100%;
  padding: 0 32px;
}
</style>
