<template>
	<div class="fw-box">
	    <div class="title-box flex_box">
			<div class="title-able">Attribute--区域</div>
			<div class="close-able" @click.stop="onCloseBlock()">
				<el-icon> <Close /> </el-icon>
			</div>
			
		</div>
		<el-tabs v-model="currentName" class="demo-tabs" @tab-click="handleClick" stretch>
		    <el-tab-pane label="属性" name="first" >
				<AttributePane></AttributePane>
			</el-tab-pane>
		    <el-tab-pane label="事件" name="second">
				<EventPane></EventPane>
			</el-tab-pane>
		    <el-tab-pane label="组件" name="third">
				<PageTree></PageTree>
			</el-tab-pane>
			<el-tab-pane label="变量" name="fourth">
				<GlobalShare :varData= "FileScript.instance" title="页面实例变量" saveTip="检查语法"
				    :showRefreshBtn="false" @saveData="onSaveData()"></GlobalShare>
			</el-tab-pane>
		</el-tabs>
	</div>
</template>

<script lang="ts" setup>
	import { reactive,ref,inject } from 'vue';
	import { useExplainVar } from '../../common/useExplainVar.js'
import { ElMessage } from 'element-plus';
	const ProjectRef = inject("ProjectRef")
	const FileScript = inject("FileScript")
	const currentName = ref('first')
	
	const {parse} = useExplainVar()
	
	function handleClick(){
		
	}
	
	function onCloseBlock(){
		ProjectRef.funcObj.setBlockVisible('right')
	}
	
	function onSaveData(){
		let resList = parse(FileScript.instance.varText,'PageData')
		if(resList){
			ElMessage({
				message:"语法正确",
				type:"success"
			})
		}
		// console.log("reslist=",resList)
	}
</script>

<style lang="scss" scoped>
	.fw-box{
		background-color: #F5F5F5;
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: stretch;
		user-select: none; /* 标准语法 */
		overflow: hidden;
		.title-box{
			background-color: white;
			padding:8px 16px;
			white-space: nowrap;
			margin-bottom: 1px;
			margin-top:1px;
			.title-able{
				flex: 1;
				font-size: 13px;
				text-align: left;
			}
			.close-able{
				cursor: pointer;
			}
		}
		
		.demo-tabs > .el-tabs__content {
		  padding: 32px;
		  color: #6b778c;
		  font-size: 32px;
		  font-weight: 600;
		  background-color: white;
		}
	}
	:deep(.el-tabs){
		flex:1;
		background-color: white;
		padding:0px 5px;
		overflow: hidden;
		// border:1px solid red;
	}
	:deep(.el-tab-pane){
		// border:1px solid red;
		height: calc(100% - 6px);
        overflow: auto; 
		scrollbar-width: none;
		
	}
</style>