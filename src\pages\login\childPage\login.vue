<template>
  <div class="login_title">欢迎登录</div>
  <div class="flex_box tab">
    <div
      :class="loginType === 1 ? 'tab_item item_active' : 'tab_item'"
      @click="handleChangeTab(1)"
    >
      账号登录
    </div>
    <div
      :class="loginType === 2 ? 'tab_item item_active' : 'tab_item'"
      @click="handleChangeTab(2)"
    >
      短信登录
    </div>
  </div>
  <el-form ref="formRef" :model="formData" :rules="rules" status-icon>
    <el-form-item prop="mobile">
      <el-input
        v-model="formData.mobile"
        placeholder="请输入手机号码"
        class="input"
        :maxlength="11"
      />
    </el-form-item>
    <el-form-item v-if="loginType === 1" prop="password">
      <el-input
        v-model="formData.password"
        placeholder="请输入密码"
        class="input"
        type="password"
        show-password
      />
    </el-form-item>
    <el-form-item v-else prop="vcode" class="form_item">
      <el-input
        v-model="formData.vcode"
        placeholder="请输入验证码"
        class="input"
        :maxlength="6"
      />
      <div class="code_container">
        <div v-if="isSend">{{ count }}s</div>
        <div
          v-else
          :style="{
            color: formData.mobile.length === 11 ? '#2B6BFF' : '#999999',
          }"
          v-debounce="handleSendCode"
        >
          发送验证码
        </div>
      </div>
    </el-form-item>
    <el-form-item prop="agreement" style="margin: 0">
      <div class="flex_box read_forget">
        <div class="read_text">
          <el-checkbox v-model="formData.agreement" />
          阅读并接收
          <a href="https://baidu.com" target="_blank" rel="noopener noreferrer">
            用户协议
          </a>
          和
          <a href="https://baidu.com" target="_blank" rel="noopener noreferrer">
            隐私政策
          </a>
        </div>
        <div class="forget" @click="handleToUrl('/login/resetPassword')">
          忘记密码?
        </div>
      </div>
    </el-form-item>
  </el-form>

  <el-button
    type="primary"
    class="submit_btn"
    :disabled="
      (loginType === 1 &&
        !(formData.mobile.length === 11 && formData.password)) ||
      (loginType === 2 &&
        !(formData.mobile.length === 11 && formData.vcode.length === 6))
    "
    v-debounce="handleSubmit"
    :loading="loading"
  >
    进入
  </el-button>
  <div class="flex_box">
    <div class="flex_box other_account">
      <div>第三方账号登录</div>
      <img src="@/assets/images/icon_qq.png" @click="handleToThirdLogin" />
      <img src="@/assets/images/icon_wechat.png" @click="handleToThirdLogin" />
    </div>
    <div class="register" @click="handleToUrl('/login/register')">注册账号</div>
  </div>
</template>

<script setup>
import { useRouter } from "vue-router";
import { reactive, ref } from "vue";
import { login, fetchPhoneCode, smsLogin } from "@/apis/loginApi";
import { useUserStore } from "@/pinia/user";
import { ElMessage } from "element-plus";

const router = useRouter();
const userStore = useUserStore();

const rules = reactive({
  mobile: [
    { required: true, message: "请输入手机号", trigger: "blur" },
    {
      pattern: /^1[3-9]\d{9}$/,
      message: "请检查手机号码是否有误",
      trigger: "change",
    },
  ],
  password: [
    {
      required: true,
      message: "请输入密码",
      trigger: "change",
    },
  ],
  vcode: [
    {
      required: true,
      message: "请输入验证码",
      trigger: "change",
    },
  ],
  agreement: [
    {
      validator: (rule, value, callback) => {
        if (!value) {
          callback(new Error("请阅读并接受用户协议和隐私政策"));
        } else {
          callback();
        }
      },
      trigger: "change",
    },
  ],
});

const formRef = ref();

const isSend = ref(false);
const count = ref(60);
const timer = ref(null);
const loading = ref(false);

const loginType = ref(1); //登录方式 1账号登录 2短信登录
const formData = reactive({
  mobile: "",
  password: "",
  vcode: "",
  agreement: false,
});

const handleToUrl = (url) => {
  router.push(url);
};

const handleToThirdLogin = () => {
  ElMessage({
    message: "敬请期待！",
    type: "info",
  });
};

//倒计时
const handleCountDown = () => {
  isSend.value = true;
  count.value = 60;

  timer.value = setInterval(() => {
    if (count.value === 0) {
      isSend.value = false;
      clearInterval(timer.value);
    } else {
      count.value--;
    }
  }, 1000);
};

const handleSendCode = async () => {
  if (/^1[3-9]\d{9}$/.test(formData.mobile)) {
    try {
      await fetchPhoneCode(formData.mobile);
      ElMessage({
        message: "发送成功！",
        type: "success",
      });
      handleCountDown();
    } catch {}
  }
};

const handleChangeTab = (e) => {
  formData.mobile = "";
  formData.password = "";
  formData.vcode = "";
  loginType.value = e;
};

const handleSubmit = () => {
  formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        loading.value = true;
        const res =
          loginType.value === 1
            ? await login({
                mobile: formData.mobile,
                password: formData.password,
              })
            : await smsLogin({
                mobile: formData.mobile,
                vcode: formData.vcode,
              });
        localStorage.setItem("token", res.data.token);
        userStore.getUserInfo().then((res) => {
          loading.value = false;
          router.replace("/home");
        });
      } catch {
        loading.value = false;
      }
    }
  });
};
</script>
