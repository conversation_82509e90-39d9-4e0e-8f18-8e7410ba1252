<template>
	<div class="content" >
		<el-empty description="暂时没有文件" v-if="OpenFileMgr.listFiles.length==0" class="no-file-box" />
		<div class="item-page-box" :style="{height:'calc(100vh - 96px - '+layoutParam.bottomTabHeight+'px)'}"
		     v-for="item in OpenFileMgr.listFiles" :key="item.fileItem.filePath" v-show="item.fileItem.filePath == OpenFileMgr.currentFilePath">
			 <pageFile v-if="item.fileItem.type=='page'" :fileItem="item"></pageFile>
			 <pageFile v-else-if="item.fileItem.type=='component'" :fileItem="item"></pageFile>
			 <eventFile v-else-if="item.fileItem.type=='event'" :fileItem="item"></eventFile>
			 <eventFile v-else-if="item.fileItem.type=='methrod'" :fileItem="item" category="methrod"></eventFile>
			 <serviceFile v-else-if="item.fileItem.type=='service'" :fileItem="item"></serviceFile>
			 <resourceFile v-else-if="item.fileItem.type=='resource'" :fileItem="item"></resourceFile>
			 <setupFile v-else-if="item.fileItem.type=='setup'" :fileItem="item"></setupFile>
			 <themeFile v-else-if="item.fileItem.type=='theme'" :fileItem="item"></themeFile>
		</div>
	</div>
</template>

<script lang="ts" setup>
	import { reactive,ref,inject,onMounted } from 'vue';
	
	const ProjectRef = inject("ProjectRef")
	const layoutParam = inject("layoutParam")
	const OpenFileMgr = inject("OpenFileMgr")
	
	
</script>

<style lang="scss" scoped>
	 .content {
		  position: relative;
		  background-color: #f5f5f5;
		  
		  .no-file-box{
			  position: absolute;
			  top:0px;
			  left:0px;
			  width:100%;
			  // border:1px solid red;
			  margin-top:30px;
		  }
		 .item-page-box {
			 position: absolute;
			 top:0px;
			 left:0px;
			 width:100%;
			 // right:0px;
			 overflow: auto;
		 }
	 }
</style>