<template>
	<div class="content" :style="props.TBase.getLineStyle()"></div>
</template>

<script lang="ts" setup>
	import { reactive,ref ,onMounted,inject,watch} from 'vue';
	const ProjectRef = inject("ProjectRef")
	
	const props = defineProps({
		TItem:{
			type:Object,
			default:{}
		},
		TParent:{
			type:Object,
			default:{id:'10001'}
		},
		TBase:{
			type:Object,
			default:{
				getLineStyle:()=>{}
			}
		}
	})
	
	onMounted(()=>{
		props.TItem.parent = props.TParent
	})
	
</script>

<style lang="scss" scoped>
	.content {
		width:calc(100% - 3px);
		height:calc(100% - 3px);
		border:1px solid #dcdcdc;
		display: flex;
		justify-content: center;
		align-items: center;
		padding:1px;
	}
</style>