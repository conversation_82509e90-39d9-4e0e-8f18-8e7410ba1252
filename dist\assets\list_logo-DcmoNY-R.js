import{E as fe,u as Re,h as me}from"./el-button-B8TCQS4u.js";import{c as B,u as de,r as ke,b as Be,O as Pe,E as Ke,d as Le,w as ce}from"./el-scrollbar-DLTsEwUl.js";import{_ as L,K as D,Q as P,e as C,L as h,B as De,v as O,bg as ve,Z as U,H as Z,J as k,M as $,bh as V,bi as Me,ag as ge,aK as Ae,r as I,g as J,o as K,f as b,b as N,aT as Ge,aU as ze,ab as Ye,ai as y,c as W,D as x,aS as Ue,aE as q,n as Q,aF as Je,y as _e,N as oe,an as He,V as Ve,au as be,a2 as je,a as We,R as qe,a8 as ee,F as Qe,j as Ze,S as Xe,U as Ie}from"./index-Dgb0NZ1J.js";import{c as xe}from"./castArray-BpiBxx45.js";import{c as we}from"./refs-CFD094o4.js";import{F as eo}from"./vnode-CV7pw3rS.js";const oo=D({inheritAttrs:!1});function no(e,n,s,t,d,a){return P(e.$slots,"default")}var to=L(oo,[["render",no],["__file","collection.vue"]]);const lo=D({name:"ElCollectionItem",inheritAttrs:!1});function ro(e,n,s,t,d,a){return P(e.$slots,"default")}var so=L(lo,[["render",ro],["__file","collection-item.vue"]]);const Ee="data-el-collection-item",Ce=e=>{const n=`El${e}Collection`,s=`${n}Item`,t=Symbol(n),d=Symbol(s),a={...to,name:n,setup(){const r=C(),u=new Map;U(t,{itemMap:u,getItems:()=>{const m=O(r);if(!m)return[];const f=Array.from(m.querySelectorAll(`[${Ee}]`));return[...u.values()].sort((o,i)=>f.indexOf(o.ref)-f.indexOf(i.ref))},collectionRef:r})}},c={...so,name:s,setup(r,{attrs:u}){const v=C(),m=h(t,void 0);U(d,{collectionItemRef:v}),De(()=>{const f=O(v);f&&m.itemMap.set(f,{ref:f,...u})}),ve(()=>{const f=O(v);m.itemMap.delete(f)})}};return{COLLECTION_INJECTION_KEY:t,COLLECTION_ITEM_INJECTION_KEY:d,ElCollection:a,ElCollectionItem:c}},ao=Z({style:{type:k([String,Array,Object])},currentTabId:{type:k(String)},defaultCurrentTabId:String,loop:Boolean,dir:{type:String,values:["ltr","rtl"],default:"ltr"},orientation:{type:k(String)},onBlur:Function,onFocus:Function,onMousedown:Function}),{ElCollection:io,ElCollectionItem:co,COLLECTION_INJECTION_KEY:ne,COLLECTION_ITEM_INJECTION_KEY:uo}=Ce("RovingFocusGroup"),te=Symbol("elRovingFocusGroup"),he=Symbol("elRovingFocusGroupItem"),po={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"},fo=(e,n)=>e,mo=(e,n,s)=>{const t=fo(e.code);return po[t]},vo=(e,n)=>e.map((s,t)=>e[(t+n)%e.length]),le=e=>{const{activeElement:n}=document;for(const s of e)if(s===n||(s.focus(),n!==document.activeElement))return},ue="currentTabIdChange",pe="rovingFocusGroup.entryFocus",go={bubbles:!1,cancelable:!0},_o=D({name:"ElRovingFocusGroupImpl",inheritAttrs:!1,props:ao,emits:[ue,"entryFocus"],setup(e,{emit:n}){var s;const t=C((s=e.currentTabId||e.defaultCurrentTabId)!=null?s:null),d=C(!1),a=C(!1),c=C(),{getItems:r}=h(ne,void 0),u=$(()=>[{outline:"none"},e.style]),v=p=>{n(ue,p)},m=()=>{d.value=!0},f=B(p=>{var g;(g=e.onMousedown)==null||g.call(e,p)},()=>{a.value=!0}),w=B(p=>{var g;(g=e.onFocus)==null||g.call(e,p)},p=>{const g=!O(a),{target:M,currentTarget:F}=p;if(M===F&&g&&!O(d)){const A=new Event(pe,go);if(F==null||F.dispatchEvent(A),!A.defaultPrevented){const _=r().filter(S=>S.focusable),R=_.find(S=>S.active),E=_.find(S=>S.id===O(t)),z=[R,E,..._].filter(Boolean).map(S=>S.ref);le(z)}}a.value=!1}),o=B(p=>{var g;(g=e.onBlur)==null||g.call(e,p)},()=>{d.value=!1}),i=(...p)=>{n("entryFocus",...p)};U(te,{currentTabbedId:Me(t),loop:V(e,"loop"),tabIndex:$(()=>O(d)?-1:0),rovingFocusGroupRef:c,rovingFocusGroupRootStyle:u,orientation:V(e,"orientation"),dir:V(e,"dir"),onItemFocus:v,onItemShiftTab:m,onBlur:o,onFocus:w,onMousedown:f}),ge(()=>e.currentTabId,p=>{t.value=p??null}),Ae(c,pe,i)}});function bo(e,n,s,t,d,a){return P(e.$slots,"default")}var Io=L(_o,[["render",bo],["__file","roving-focus-group-impl.vue"]]);const wo=D({name:"ElRovingFocusGroup",components:{ElFocusGroupCollection:io,ElRovingFocusGroupImpl:Io}});function Eo(e,n,s,t,d,a){const c=I("el-roving-focus-group-impl"),r=I("el-focus-group-collection");return K(),J(r,null,{default:b(()=>[N(c,Ge(ze(e.$attrs)),{default:b(()=>[P(e.$slots,"default")]),_:3},16)]),_:3})}var Co=L(wo,[["render",Eo],["__file","roving-focus-group.vue"]]);const ho=Z({trigger:Be.trigger,triggerKeys:{type:k(Array),default:()=>[y.enter,y.numpadEnter,y.space,y.down]},effect:{...de.effect,default:"light"},type:{type:k(String)},placement:{type:k(String),default:"bottom"},popperOptions:{type:k(Object),default:()=>({})},id:String,size:{type:String,default:""},splitButton:Boolean,hideOnClick:{type:Boolean,default:!0},loop:{type:Boolean,default:!0},showTimeout:{type:Number,default:150},hideTimeout:{type:Number,default:150},tabindex:{type:k([Number,String]),default:0},maxHeight:{type:k([Number,String]),default:""},popperClass:{type:String,default:""},disabled:Boolean,role:{type:String,values:ke,default:"menu"},buttonProps:{type:k(Object)},teleported:de.teleported,persistent:{type:Boolean,default:!0}}),ye=Z({command:{type:[Object,String,Number],default:()=>({})},disabled:Boolean,divided:Boolean,textValue:String,icon:{type:Ye}}),yo=Z({onKeydown:{type:k(Function)}}),To=[y.down,y.pageDown,y.home],Te=[y.up,y.pageUp,y.end],$o=[...To,...Te],{ElCollection:Oo,ElCollectionItem:Fo,COLLECTION_INJECTION_KEY:So,COLLECTION_ITEM_INJECTION_KEY:No}=Ce("Dropdown"),X=Symbol("elDropdown"),$e="elDropdown",{ButtonGroup:Ro}=fe,ko=D({name:"ElDropdown",components:{ElButton:fe,ElButtonGroup:Ro,ElScrollbar:Le,ElDropdownCollection:Oo,ElTooltip:Ke,ElRovingFocusGroup:Co,ElOnlyChild:Pe,ElIcon:_e,ArrowDown:Je},props:ho,emits:["visible-change","click","command"],setup(e,{emit:n}){const s=be(),t=oe("dropdown"),{t:d}=He(),a=C(),c=C(),r=C(),u=C(),v=C(null),m=C(null),f=C(!1),w=$(()=>({maxHeight:Ve(e.maxHeight)})),o=$(()=>[t.m(_.value)]),i=$(()=>xe(e.trigger)),p=me().value,g=$(()=>e.id||p);ge([a,i],([l,T],[Y])=>{var se,ae,ie;(se=Y==null?void 0:Y.$el)!=null&&se.removeEventListener&&Y.$el.removeEventListener("pointerenter",E),(ae=l==null?void 0:l.$el)!=null&&ae.removeEventListener&&l.$el.removeEventListener("pointerenter",E),(ie=l==null?void 0:l.$el)!=null&&ie.addEventListener&&T.includes("hover")&&l.$el.addEventListener("pointerenter",E)},{immediate:!0}),ve(()=>{var l,T;(T=(l=a.value)==null?void 0:l.$el)!=null&&T.removeEventListener&&a.value.$el.removeEventListener("pointerenter",E)});function M(){F()}function F(){var l;(l=r.value)==null||l.onClose()}function A(){var l;(l=r.value)==null||l.onOpen()}const _=Re();function R(...l){n("command",...l)}function E(){var l,T;(T=(l=a.value)==null?void 0:l.$el)==null||T.focus()}function G(){}function z(){const l=O(u);i.value.includes("hover")&&(l==null||l.focus()),m.value=null}function S(l){m.value=l}function re(l){f.value||(l.preventDefault(),l.stopImmediatePropagation())}function j(){n("visible-change",!0)}function H(l){var T;(l==null?void 0:l.type)==="keydown"&&((T=u.value)==null||T.focus())}function Ne(){n("visible-change",!1)}return U(X,{contentRef:u,role:$(()=>e.role),triggerId:g,isUsingKeyboard:f,onItemEnter:G,onItemLeave:z}),U($e,{instance:s,dropdownSize:_,handleClick:M,commandHandler:R,trigger:V(e,"trigger"),hideOnClick:V(e,"hideOnClick")}),{t:d,ns:t,scrollbar:v,wrapStyle:w,dropdownTriggerKls:o,dropdownSize:_,triggerId:g,currentTabId:m,handleCurrentTabIdChange:S,handlerMainButtonClick:l=>{n("click",l)},handleEntryFocus:re,handleClose:F,handleOpen:A,handleBeforeShowTooltip:j,handleShowTooltip:H,handleBeforeHideTooltip:Ne,onFocusAfterTrapped:l=>{var T,Y;l.preventDefault(),(Y=(T=u.value)==null?void 0:T.focus)==null||Y.call(T,{preventScroll:!0})},popperRef:r,contentRef:u,triggeringElementRef:a,referenceElementRef:c}}});function Bo(e,n,s,t,d,a){var c;const r=I("el-dropdown-collection"),u=I("el-roving-focus-group"),v=I("el-scrollbar"),m=I("el-only-child"),f=I("el-tooltip"),w=I("el-button"),o=I("arrow-down"),i=I("el-icon"),p=I("el-button-group");return K(),W("div",{class:Q([e.ns.b(),e.ns.is("disabled",e.disabled)])},[N(f,{ref:"popperRef",role:e.role,effect:e.effect,"fallback-placements":["bottom","top"],"popper-options":e.popperOptions,"gpu-acceleration":!1,"hide-after":e.trigger==="hover"?e.hideTimeout:0,"manual-mode":!0,placement:e.placement,"popper-class":[e.ns.e("popper"),e.popperClass],"reference-element":(c=e.referenceElementRef)==null?void 0:c.$el,trigger:e.trigger,"trigger-keys":e.triggerKeys,"trigger-target-el":e.contentRef,"show-after":e.trigger==="hover"?e.showTimeout:0,"stop-popper-mouse-event":!1,"virtual-ref":e.triggeringElementRef,"virtual-triggering":e.splitButton,disabled:e.disabled,transition:`${e.ns.namespace.value}-zoom-in-top`,teleported:e.teleported,pure:"",persistent:e.persistent,onBeforeShow:e.handleBeforeShowTooltip,onShow:e.handleShowTooltip,onBeforeHide:e.handleBeforeHideTooltip},Ue({content:b(()=>[N(v,{ref:"scrollbar","wrap-style":e.wrapStyle,tag:"div","view-class":e.ns.e("list")},{default:b(()=>[N(u,{loop:e.loop,"current-tab-id":e.currentTabId,orientation:"horizontal",onCurrentTabIdChange:e.handleCurrentTabIdChange,onEntryFocus:e.handleEntryFocus},{default:b(()=>[N(r,null,{default:b(()=>[P(e.$slots,"dropdown")]),_:3})]),_:3},8,["loop","current-tab-id","onCurrentTabIdChange","onEntryFocus"])]),_:3},8,["wrap-style","view-class"])]),_:2},[e.splitButton?void 0:{name:"default",fn:b(()=>[N(m,{id:e.triggerId,ref:"triggeringElementRef",role:"button",tabindex:e.tabindex},{default:b(()=>[P(e.$slots,"default")]),_:3},8,["id","tabindex"])])}]),1032,["role","effect","popper-options","hide-after","placement","popper-class","reference-element","trigger","trigger-keys","trigger-target-el","show-after","virtual-ref","virtual-triggering","disabled","transition","teleported","persistent","onBeforeShow","onShow","onBeforeHide"]),e.splitButton?(K(),J(p,{key:0},{default:b(()=>[N(w,q({ref:"referenceElementRef"},e.buttonProps,{size:e.dropdownSize,type:e.type,disabled:e.disabled,tabindex:e.tabindex,onClick:e.handlerMainButtonClick}),{default:b(()=>[P(e.$slots,"default")]),_:3},16,["size","type","disabled","tabindex","onClick"]),N(w,q({id:e.triggerId,ref:"triggeringElementRef"},e.buttonProps,{role:"button",size:e.dropdownSize,type:e.type,class:e.ns.e("caret-button"),disabled:e.disabled,tabindex:e.tabindex,"aria-label":e.t("el.dropdown.toggleDropdown")}),{default:b(()=>[N(i,{class:Q(e.ns.e("icon"))},{default:b(()=>[N(o)]),_:1},8,["class"])]),_:1},16,["id","size","type","class","disabled","tabindex","aria-label"])]),_:3})):x("v-if",!0)],2)}var Po=L(ko,[["render",Bo],["__file","dropdown.vue"]]);const Ko=D({components:{ElRovingFocusCollectionItem:co},props:{focusable:{type:Boolean,default:!0},active:{type:Boolean,default:!1}},emits:["mousedown","focus","keydown"],setup(e,{emit:n}){const{currentTabbedId:s,loop:t,onItemFocus:d,onItemShiftTab:a}=h(te,void 0),{getItems:c}=h(ne,void 0),r=me(),u=C(),v=B(o=>{n("mousedown",o)},o=>{e.focusable?d(O(r)):o.preventDefault()}),m=B(o=>{n("focus",o)},()=>{d(O(r))}),f=B(o=>{n("keydown",o)},o=>{const{code:i,shiftKey:p,target:g,currentTarget:M}=o;if(i===y.tab&&p){a();return}if(g!==M)return;const F=mo(o);if(F){o.preventDefault();let _=c().filter(R=>R.focusable).map(R=>R.ref);switch(F){case"last":{_.reverse();break}case"prev":case"next":{F==="prev"&&_.reverse();const R=_.indexOf(M);_=t.value?vo(_,R+1):_.slice(R+1);break}}je(()=>{le(_)})}}),w=$(()=>s.value===O(r));return U(he,{rovingFocusGroupItemRef:u,tabIndex:$(()=>O(w)?0:-1),handleMousedown:v,handleFocus:m,handleKeydown:f}),{id:r,handleKeydown:f,handleFocus:m,handleMousedown:v}}});function Lo(e,n,s,t,d,a){const c=I("el-roving-focus-collection-item");return K(),J(c,{id:e.id,focusable:e.focusable,active:e.active},{default:b(()=>[P(e.$slots,"default")]),_:3},8,["id","focusable","active"])}var Do=L(Ko,[["render",Lo],["__file","roving-focus-item.vue"]]);const Mo=D({name:"DropdownItemImpl",components:{ElIcon:_e},props:ye,emits:["pointermove","pointerleave","click","clickimpl"],setup(e,{emit:n}){const s=oe("dropdown"),{role:t}=h(X,void 0),{collectionItemRef:d}=h(No,void 0),{collectionItemRef:a}=h(uo,void 0),{rovingFocusGroupItemRef:c,tabIndex:r,handleFocus:u,handleKeydown:v,handleMousedown:m}=h(he,void 0),f=we(d,a,c),w=$(()=>t.value==="menu"?"menuitem":t.value==="navigation"?"link":"button"),o=B(i=>{if([y.enter,y.numpadEnter,y.space].includes(i.code))return i.preventDefault(),i.stopImmediatePropagation(),n("clickimpl",i),!0},v);return{ns:s,itemRef:f,dataset:{[Ee]:""},role:w,tabIndex:r,handleFocus:u,handleKeydown:o,handleMousedown:m}}});function Ao(e,n,s,t,d,a){const c=I("el-icon");return K(),W(Qe,null,[e.divided?(K(),W("li",{key:0,role:"separator",class:Q(e.ns.bem("menu","item","divided"))},null,2)):x("v-if",!0),We("li",q({ref:e.itemRef},{...e.dataset,...e.$attrs},{"aria-disabled":e.disabled,class:[e.ns.be("menu","item"),e.ns.is("disabled",e.disabled)],tabindex:e.tabIndex,role:e.role,onClick:r=>e.$emit("clickimpl",r),onFocus:e.handleFocus,onKeydown:ee(e.handleKeydown,["self"]),onMousedown:e.handleMousedown,onPointermove:r=>e.$emit("pointermove",r),onPointerleave:r=>e.$emit("pointerleave",r)}),[e.icon?(K(),J(c,{key:0},{default:b(()=>[(K(),J(qe(e.icon)))]),_:1})):x("v-if",!0),P(e.$slots,"default")],16,["aria-disabled","tabindex","role","onClick","onFocus","onKeydown","onMousedown","onPointermove","onPointerleave"])],64)}var Go=L(Mo,[["render",Ao],["__file","dropdown-item-impl.vue"]]);const Oe=()=>{const e=h($e,{}),n=$(()=>e==null?void 0:e.dropdownSize);return{elDropdown:e,_elDropdownSize:n}},zo=D({name:"ElDropdownItem",components:{ElDropdownCollectionItem:Fo,ElRovingFocusItem:Do,ElDropdownItemImpl:Go},inheritAttrs:!1,props:ye,emits:["pointermove","pointerleave","click"],setup(e,{emit:n,attrs:s}){const{elDropdown:t}=Oe(),d=be(),a=C(null),c=$(()=>{var o,i;return(i=(o=O(a))==null?void 0:o.textContent)!=null?i:""}),{onItemEnter:r,onItemLeave:u}=h(X,void 0),v=B(o=>(n("pointermove",o),o.defaultPrevented),ce(o=>{if(e.disabled){u(o);return}const i=o.currentTarget;i===document.activeElement||i.contains(document.activeElement)||(r(o),o.defaultPrevented||i==null||i.focus())})),m=B(o=>(n("pointerleave",o),o.defaultPrevented),ce(u)),f=B(o=>{if(!e.disabled)return n("click",o),o.type!=="keydown"&&o.defaultPrevented},o=>{var i,p,g;if(e.disabled){o.stopImmediatePropagation();return}(i=t==null?void 0:t.hideOnClick)!=null&&i.value&&((p=t.handleClick)==null||p.call(t)),(g=t.commandHandler)==null||g.call(t,e.command,d,o)}),w=$(()=>({...e,...s}));return{handleClick:f,handlePointerMove:v,handlePointerLeave:m,textContent:c,propsAndAttrs:w}}});function Yo(e,n,s,t,d,a){var c;const r=I("el-dropdown-item-impl"),u=I("el-roving-focus-item"),v=I("el-dropdown-collection-item");return K(),J(v,{disabled:e.disabled,"text-value":(c=e.textValue)!=null?c:e.textContent},{default:b(()=>[N(u,{focusable:!e.disabled},{default:b(()=>[N(r,q(e.propsAndAttrs,{onPointerleave:e.handlePointerLeave,onPointermove:e.handlePointerMove,onClickimpl:e.handleClick}),{default:b(()=>[P(e.$slots,"default")]),_:3},16,["onPointerleave","onPointermove","onClickimpl"])]),_:3},8,["focusable"])]),_:3},8,["disabled","text-value"])}var Fe=L(zo,[["render",Yo],["__file","dropdown-item.vue"]]);const Uo=D({name:"ElDropdownMenu",props:yo,setup(e){const n=oe("dropdown"),{_elDropdownSize:s}=Oe(),t=s.value,{focusTrapRef:d,onKeydown:a}=h(eo,void 0),{contentRef:c,role:r,triggerId:u}=h(X,void 0),{collectionRef:v,getItems:m}=h(So,void 0),{rovingFocusGroupRef:f,rovingFocusGroupRootStyle:w,tabIndex:o,onBlur:i,onFocus:p,onMousedown:g}=h(te,void 0),{collectionRef:M}=h(ne,void 0),F=$(()=>[n.b("menu"),n.bm("menu",t==null?void 0:t.value)]),A=we(c,v,d,f,M),_=B(E=>{var G;(G=e.onKeydown)==null||G.call(e,E)},E=>{const{currentTarget:G,code:z,target:S}=E;if(G.contains(S),y.tab===z&&E.stopImmediatePropagation(),E.preventDefault(),S!==O(c)||!$o.includes(z))return;const j=m().filter(H=>!H.disabled).map(H=>H.ref);Te.includes(z)&&j.reverse(),le(j)});return{size:t,rovingFocusGroupRootStyle:w,tabIndex:o,dropdownKls:F,role:r,triggerId:u,dropdownListWrapperRef:A,handleKeydown:E=>{_(E),a(E)},onBlur:i,onFocus:p,onMousedown:g}}});function Jo(e,n,s,t,d,a){return K(),W("ul",{ref:e.dropdownListWrapperRef,class:Q(e.dropdownKls),style:Ze(e.rovingFocusGroupRootStyle),tabindex:-1,role:e.role,"aria-labelledby":e.triggerId,onBlur:e.onBlur,onFocus:e.onFocus,onKeydown:ee(e.handleKeydown,["self"]),onMousedown:ee(e.onMousedown,["self"])},[P(e.$slots,"default")],46,["role","aria-labelledby","onBlur","onFocus","onKeydown","onMousedown"])}var Se=L(Uo,[["render",Jo],["__file","dropdown-menu.vue"]]);const xo=Xe(Po,{DropdownItem:Fe,DropdownMenu:Se}),en=Ie(Fe),on=Ie(Se),nn="/assets/list_logo-BHJ91Vnl.png";export{xo as E,nn as _,on as a,en as b,ho as d};
