import http from "@/axios/netRequest";

export const projectList = (data) => {
  return http.post(`/api/project/list`, data);
};

export const databaseList = (params) => {
  return http.get(`/api/dbstore/list`, { params });
};

export const applicationList = (data) => {
  return http.post(`/yhyApi/project/applicationlist`, data);
};

export const dataDesignTree = (id) => {
  return http.get(`/api/dynamictable/list/${id}/1/100`);
};

export const databaseDetail = (id) => {
  return http.get(`/api/dbstore/get/${id}`);
};

export const dataTableDetail = (id) => {
  return http.get(`/api/dynamictable/get/${id}`);
};

export const createTable = (data) => {
  return http.post(`/api/dynamictable/create`, data);
};

export const removeDatabase = (id) => {
  return http.delete(`/api/dbstore/delete/${id}`);
};

export const createDatabase = (data) => {
  return http.post(`/api/dbstore/create`, data);
};

export const updateDatabase = (data) => {
  return http.put(`/api/dbstore/update`, data);
};

export const saveTable = (data) => {
  return http.put(`/api/dynamictable/update`, data);
};

export const removeTable = (id) => {
  return http.delete(`/api/dynamictable/delete/${id}`);
};

export const authProjects = (dbStoreId) => {
  return http.post(`/api/authDbProject/listByDbStoreId/${dbStoreId}`);
};

export const saveAuthProject = (data) => {
  return http.post(`/api/authDbProject/saveAuthDbProject`, data);
};

export const delAuthProject = (id) => {
  return http.delete(`/api/authDbProject/deleteById/${id}`);
};

export const authUsers = (data) => {
  return http.post(`/api/authDbUser/listByPage`, data);
};

export const addAuthUser = (data) => {
  return http.post(`/api/authDbUser/addAuthDbUser`, data);
};

export const delAuthUser = (id) => {
  return http.delete(`/api/authDbUser/deleteById/${id}`);
};

export const updateAuthUser = (data) => {
  return http.post(`/api/authDbUser/batchUpdateAuthDbUser`, data);
};

export const columnList = (id) => {
  return http.get(`/api/dynamiccolumn/list/${id}/1/300`);
};

export const indexList = (tableId) => {
  return http.post(`/api/dynamicIndex/queryList/${tableId}`);
};

export const saveIndexList = (tableId, data) => {
  return http.post(
    `/api/dynamicIndex/createOrUpdateIndexBatch/${tableId}`,
    data
  );
};

export const deleteIndex = (id) => {
  return http.delete(`/api/dynamicIndex/deleteById/${id}`);
};

export const deleteColumn = (id) => {
  return http.delete(`/api/dynamiccolumn/delete/${id}`);
};

export const saveColumn = (tableId, data) => {
  return http.post(`/api/dynamiccolumn/createOrUpdateBatch/${tableId}`, data);
};

export const generateSql = (data) => {
  return http.post(`/api/dynamictable/generate-sql`, data);
};

export const addProject = (data) => {
  return http.post(`/api/project/add`, data);
};

export const updateProject = (data) => {
  return http.post(`/api/project/update/${data.id}`, data);
};

export const deleteProject = (id) => {
  return http.get(`/api/project/deleteById/${id}`);
};

export const findUsersByPid = (projectId) => {
  return http.post(`/api/project/findUsersByPid/${projectId}`, {
    pageNo: 1,
    pageSize: 100,
  });
};

export const deleteAuthUserById = (id) => {
  return http.post(`/api/project/deleteAuthUserById/${id}`);
};

export const addUpdateAuth = (data) => {
  return http.post(`/api/project/addUpdateAuth`, data);
};

export const queryDesign = (projectId) => {
  return http.get(`/api/project/queryDesign/${projectId}`);
};

export const authUserByMobile = (data) => {
  return http.post(`/api/project/authUserByMobile`, data);
};

export const cloneDetail = (id) => {
  return http.post(`/api/project/toClone/${id}`);
};

export const doClone = (data) => {
  return http.post(`/api/project/doClone/${data.id}`, data);
};
