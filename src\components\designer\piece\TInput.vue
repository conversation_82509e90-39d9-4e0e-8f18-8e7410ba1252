<template>
	<div class="content-box">
		<input  class="content"  type="text"  v-model="currentValue" :placeholder="props.TItem.value"  v-if="props.TItem.key=='TInput'" 
		      @mousedown="onMouseDown" @click.stop="onClick()" @input="onChange" :style="props.TBase.getInputStyle()"/>
		<textarea  class="content" v-model="currentValue" :placeholder="props.TItem.value"  v-else-if="props.TItem.key=='TArea'" 
		      @mousedown="onMouseDown" @click.stop="onClick()" @input="onChange" :style="props.TBase.getInputStyle()"/>
	</div>
	
</template>

<script lang="ts" setup>
	import { reactive,ref ,onMounted,inject,watch} from 'vue';
	const ProjectRef = inject("ProjectRef")
	const ChooseComponentList = inject('ChooseComponentList')
	
	const props = defineProps({
		TItem:{
			type:Object,
			default:{}
		},
		TParent:{
			type:Object,
			default:{id:'10001'}
		},
		TBase:{
			type:Object,
			default:{
				checkEnabled:()=>{},
				getInputStyle:()=>{}
			}
		}
	})
	
	const currentValue = ref('')
	
	function getFontColor(){
		if(props.TItem.hasOwnProperty('fontColor')){
			return props.TItem.fontColor
		}else{
			return ProjectRef.BasicData.theme.currentTheme.color
		}
	}
	function getBorder(){
		if(props.TItem.hasOwnProperty('border')){
			return props.TItem.border
		}else{
			return '1px solid '+ProjectRef.BasicData.theme.currentTheme.color
		}
	}
	
	onMounted(()=>{
		props.TItem.parent = props.TParent
		// if(props.TItem.key=='TInput'){
		// 	props.TItem.w = 180
		// 	props.TItem.h = 38
		// }else{
		// 	props.TItem.w = 180
		// 	props.TItem.h = 120
		// }
		currentValue.value = props.TItem.value
	})
	
	watch(()=>props.TItem.value, (newValue)=>{
		currentValue.value = newValue
	})
	
	function onChange(e){
		props.TItem.value = currentValue.value
	}

	function onMouseDown(e){
		e.stopPropagation(); 
	}
	
	function onClick(){
		ChooseComponentList.setKey(props.TItem)
	}
	
</script>

<style lang="scss" scoped>
	.content-box{
		height:calc(100% - 2px);
		width: calc(100% - 2px);
		background-color: transparent;
		position: relative;
		overflow: hidden;
		cursor: move;
		.content {
			position: absolute;
			top:2px;
			left:2px;
			width:calc(100% - 12px);
			height:calc(100% - 12px);
			border:0px;
			background-color: transparent;
			padding:3px;
			user-select: auto;
			font-size: 16px;
		}
		.content:focus{
			outline: none; 
			box-shadow: 0 0 0 2px transparent; 
		}
	}
</style>