<template>
  <div class="deployment_container">
    <div class="flex_box head">
      <div class="back">
        <el-button
          style="font-size: 16px; color: #212121"
          :icon="Close"
          link
          @click="handleGoBack"
        >
          退出
        </el-button>
        <span class="title">布署</span>
      </div>
    </div>
    <div class="platform_list">
      <div
        v-for="item in platformOptions"
        :key="item.label"
        class="platform_item"
      >
        <img :src="item.icon" />
        <div class="name">{{ item.label }}</div>
        <el-button color="#000">打包小程序</el-button>
        <el-button color="#000">参数配置</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, reactive, ref } from "vue";
import { useRouter } from "vue-router";
import { Close } from "@element-plus/icons-vue";
import weixinIcon from "@/assets/images/xmfb_icon_weixinxcx.png";
import androidIcon from "@/assets/images/xmfb_icon_android.png";
import iosIcon from "@/assets/images/xmfb_icon_ios.png";
import webIcon from "@/assets/images/xmfb_icon_houtai.png";

const router = useRouter();

const platformOptions = ref([
  { label: "微信小程序", icon: weixinIcon },
  { label: "Android/HMS", icon: androidIcon },
  { label: "IOS应用", icon: iosIcon },
  { label: "后台服务部署（含H5应用）", icon: webIcon },
]);

const handleGoBack = () => {
  router.go(-1);
};
</script>

<style lang="scss" scoped>
.deployment_container {
  box-sizing: border-box;
  border-top: 1px solid #e5e5e5; // 顶部边框;
  height: 100%;
  background-color: #ffffff;

  .head {
    box-sizing: border-box;
    padding: 0 24px;
    height: 64px;
    background: #ffffff;

    .back {
      display: flex;
      align-items: center;

      .title {
        display: inline-block;
        border-left: 2px solid #e6e6e6;
        padding-left: 16px;
        margin-left: 16px;
        font-weight: 600;
        font-size: 16px;
        color: #212121;
        line-height: 19px;
      }
    }
  }

  .platform_list {
    display: flex;
    justify-content: center;

    .platform_item {
      text-align: center;
      margin-right: 194px;

      img {
        width: 150px;
        height: 150px;
      }

      .name {
        font-weight: 600;
        font-size: 24px;
        color: #000000;
        line-height: 30px;
        margin: 12px 0 32px 0;
      }

      .el-button {
        display: block;
        margin: 0 auto 12px auto;
        background-color: #ffffff;
        color: #000;
      }
    }
  }
}
</style>
