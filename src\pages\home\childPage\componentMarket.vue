<template>
  <div v-if="!isMine" class="market_header">
    <el-input
      v-model="searchText"
      placeholder="请输入关键字搜索"
      :prefix-icon="Search"
    />
    <div>
      <el-button v-for="item in btnList" :key="item">{{ item }}</el-button>
    </div>
  </div>
  <div v-else class="flex_box my_header">
    <div class="title">我的组件仓库</div>
    <el-input
      v-model="searchText"
      placeholder="请输入关键字搜索"
      :prefix-icon="Search"
    />
  </div>
  <div class="comp_list">
    <div
      class="flex_box comp_list_item"
      v-for="(item, index) in 6"
      :key="index"
    >
      <img class="comp_list_item_logo" src="@/assets/logo.png" alt="" />
      <div class="detail">
        <div class="title">列表组建（V1.0.25）</div>
        <div class="flex_box tag">
          <div class="type">列表</div>
          <div class="type_icon_box">
            <img class="type_icon" :src="true ? computImg : phoneImg" />
          </div>
          <div class="time">2025.03.12</div>
          <el-rate v-model="rateValue" disabled />
        </div>
        <div class="author">作者：九天站祖（NO.234590）</div>
        <div class="desc">
          说明：这个列表控制可以多选这个列表控制可以多选这个列表控制可以多选这个列表控制可以多选这个列表控这个列表控制可以多选
        </div>
        <div v-if="!isMine" class="operation">
          <el-button type="primary" color="#2B6BFF" @click="handleDownload">
            下载 (12次)
          </el-button>
          <el-button
            type="primary"
            plain
            color="#2B6BFF"
            class="custom-button"
            @click="detailDialogVisible = true"
          >
            接口说明
          </el-button>
        </div>
        <div v-else class="operation">
          <el-button
            type="primary"
            color="#2B6BFF"
            @click="uploadDialogVisible = true"
          >
            上传市场
          </el-button>
          <el-button type="primary" plain color="#2B6BFF" class="custom-button">
            编辑组件
          </el-button>
          <el-button :icon="Delete" @click="handleDelete" />
          <el-button :icon="Search" />
        </div>
      </div>
    </div>
  </div>
  <div class="pagination">
    <el-pagination layout="prev, pager, next" :total="1000" />
  </div>
  <!-- 接口说明 -->
  <el-dialog v-model="detailDialogVisible" destroy-on-close width="1000">
    <template #header>
      <div style="text-align: center; font-weight: 600; font-size: 18px">
        接口说明
      </div>
    </template>
    <div class="flex_box comp_list_item dialog_comp_list_item">
      <img class="comp_list_item_logo" src="@/assets/logo.png" alt="" />
      <div class="detail">
        <div class="title">列表组建（V1.0.25）</div>
        <div class="flex_box tag">
          <div class="type">列表</div>
          <div class="type_icon_box">
            <img class="type_icon" :src="index % 2 ? computImg : phoneImg" />
          </div>
          <div class="time">2025.03.12</div>
          <el-rate v-model="rateValue" disabled />
        </div>
        <div class="author">作者：九天站祖（NO.234590）</div>
        <div class="desc">
          说明：这个列表控制可以多选这个列表控制可以多选这个列表控制可以多选这个列表控制可以多选这个列表控这个列表控制可以多选
        </div>
      </div>
    </div>
    <div class="descripetion">
      1、数据：列表类型，单个列表项 { id：234， image：’xxxxx.png’，
      text：‘好货’ }
    </div>
  </el-dialog>
  <!-- 上传市场 -->
  <el-dialog
    v-model="uploadDialogVisible"
    destroy-on-close
    width="600"
    @close="handleResetForm"
  >
    <template #header>
      <div style="text-align: center; font-weight: 600; font-size: 18px">
        上传市场
      </div>
    </template>
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
      status-icon
      label-position="left"
    >
      <el-form-item label="类型" prop="clientType">
        <el-radio-group v-model="formData.clientType">
          <el-radio :value="0">手机</el-radio>
          <el-radio :value="1">pc</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="图片" prop="icon">
        <UploadImg placeholder="应用图标" v-model="formData.icon" />
      </el-form-item>
      <el-form-item label="名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入组件名称" />
      </el-form-item>
      <el-form-item label="分类" prop="name">
        <el-select v-model="value" placeholder="请选择组件分类">
          <el-option
            v-for="item in []"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="版本号" prop="name">
        <el-input v-model="formData.name" placeholder="请填写版本号" />
      </el-form-item>
      <el-form-item label="说明" prop="description">
        <el-input
          v-model="formData.description"
          placeholder="请输入内容"
          type="textarea"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div style="display: flex; justify-content: center">
        <el-button @click="uploadDialogVisible = false">取消</el-button>
        <el-button type="primary" v-debounce="handleSubmit" color="#2B6BFF">
          保存
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive } from "vue";
import { Search, Delete } from "@element-plus/icons-vue";
import computImg from "@/assets/images/list_icon_comput.png";
import phoneImg from "@/assets/images/list_icon_pone.png";

const isMine = ref(false);
const searchText = ref("");
const rateValue = ref(3);
const btnList = ref([
  "列表",
  "按钮",
  "图片",
  "广告滚动",
  "Tabbar",
  "Fragment",
  "下拉窗",
  "富文本编辑",
  "WebView",
]);
const detailDialogVisible = ref(false);
const uploadDialogVisible = ref(false);
const formRef = ref();
const formData = ref({});
const rules = reactive({});

const handleResetForm = () => {};
const handleSubmit = () => {};
const handleDelete = () => {
  ElMessageBox.confirm(`是否删除组件`, `删除组件`, {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    center: true,
    customClass: "custom_message_box", // 添加自定义类名
  }).then(() => {});
};
const handleDownload = () => {
  ElMessageBox.confirm(
    `组件下载成功，已经放入【我的仓库】中，可以进入查看`,
    `下载组件`,
    {
      confirmButtonText: "转到我的仓库",
      cancelButtonText: "确定",
      center: true,
      customClass: "custom_message_box", // 添加自定义类名
    }
  ).then(() => {
    isMine.value = true;
  });
};
</script>

<style lang="scss">
.market_header {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32px 0;
  .el-input {
    width: 824px;
    margin-bottom: 16px;
    height: 48px;
    .el-input__wrapper {
      border-radius: 12px;
      background: #f5f5f5;
      font-size: 18px;
      color: #999999;
    }
  }
}
.my_header {
  padding: 26px 20px 32px;
  justify-content: flex-start;
  .title {
    font-weight: 600;
    font-size: 18px;
    color: #212121;
    line-height: 30px;
    margin-right: 12px;
  }
  .el-input {
    width: 200px;
    height: 34px;
    .el-input__wrapper {
      background: #f5f5f5;
      border-radius: 8px 8px 8px 8px;
    }
  }
}
.comp_list {
  height: 600px;
  overflow-y: auto;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}
.comp_list_item {
  box-sizing: border-box;
  width: 684px;
  height: 250px;
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #e6e6e6;
  padding: 20px;
  justify-content: flex-start;
  align-items: flex-start;
  margin: 0 24px 24px 0;
  .comp_list_item_logo {
    width: 280px;
    height: 210px;
    background: #e6e6e6;
    border-radius: 8px 8px 8px 8px;
    margin-right: 16px;
    flex-shrink: 0;
  }
  .detail {
    flex-grow: 1;
    .title {
      font-weight: 600;
      font-size: 22px;
      color: #000000;
      line-height: 30px;
    }
    .tag {
      margin: 8px 0;
      justify-content: flex-start;
      .type {
        padding: 2px 4px;
        background: #f0f0f0;
        border-radius: 3px;
        font-size: 12px;
        color: #212121;
        line-height: 16px;
      }
      .type_icon_box {
        width: 24px;
        height: 20px;
        background: #f0f0f0;
        border-radius: 3px 3px 3px 3px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 0 12px 0 6px;
        .type_icon {
          width: 16px;
          height: 16px;
        }
      }
      .time {
        font-size: 14px;
        color: #999999;
        line-height: 16px;
        margin-right: 12px;
      }
    }
    .author {
      font-size: 14px;
      color: #666666;
      line-height: 16px;
    }
    .desc {
      font-size: 14px;
      color: #999999;
      line-height: 16px;
      margin-top: 8px;
    }
    .operation {
      margin-top: 24px;
      .custom-button {
        background-color: white !important;
        border-color: #2b6bff !important;
        color: #2b6bff !important;
      }

      .custom-button:hover {
        background-color: white !important;
        border-color: #2b6bff !important;
        color: #2b6bff !important;
      }
    }
  }
}
.dialog_comp_list_item {
  width: 100%;
  border: none;
  border-top: 1px solid #f0f0f0;
  padding: 24px 8px;
  margin: 0;
}
.descripetion {
  height: 384px;
  background: #fafafa;
  border-radius: 8px 8px 8px 8px;
  font-size: 14px;
  color: #000000;
  line-height: 16px;
  box-sizing: border-box;
  padding: 24px;
  margin: 0 8px;
}
.pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  .el-pagination .btn-prev {
    margin-right: 8px;
  }
  .el-pager li {
    width: 32px;
    height: 32px;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #e6e6e6;
    margin-right: 8px;
    font-size: 16px;
    color: #000000;
    line-height: 19px;
  }
  .el-pager li.is-active {
    width: 32px;
    height: 32px;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #2b6bff;
    font-weight: 400;
    font-size: 16px;
    color: #2b6bff;
    line-height: 19px;
  }
}
.el-rate .el-rate__icon {
  margin-right: 0;
}
.custom_message_box {
  .el-message-box__header.show-close {
    padding-right: 0 !important;
  }
}
</style>
