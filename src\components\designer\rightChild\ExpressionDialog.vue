<template>
  <el-dialog width="800" style="text-align: left" :model-value="props.modelValue" @close="handleCancel"
    :title="props.openType === 'expression' ? `编辑表达式（返回类型：${props.returnType}）` : '选择变量'">
    <div class="dialog-content">
      <div class="textarea-box">
        <textarea ref="textareaRef" placeholder="请输入/选择" v-model="expressionTxt" 
                  @click="updateCursorPosition" @input="updateCursorPosition" @keyup="updateCursorPosition" >
		</textarea>
        <el-popconfirm title="确认清空并关闭弹窗吗?" @confirm="handleClear" v-if="expressionTxt" >
          <template #reference>
            <el-button class="clear-btn">清空并关闭</el-button>
          </template>
        </el-popconfirm>
        <div>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
          <el-button type="primary" @click="handleCheck(true)">检查</el-button>
          <el-button @click="handleCancel">取消</el-button>
        </div>
      </div>
      <el-tabs v-model="activeName" type="card">
        <el-tab-pane label="运算符" name="1" v-if="props.openType === 'expression'" >
          <div class="tab-pane-box">
            <div class="operator-box">
              <div class="operator-box-item">
                <el-tooltip v-for="item in operatorList.slice(0, 4)" effect="dark" :content="item.label" placement="top" :show-after="500" >
                  <el-button plain  @click="handleRowClick(3, item)" class="tab-pane-box-btn" > {{ item.value }} </el-button>
                </el-tooltip>
              </div>
              <div class="operator-box-item">
                <el-tooltip v-for="item in operatorList.slice(4, 10)" effect="dark" :content="item.label" placement="top" :show-after="500" >
                  <el-button plain @click="handleRowClick(3, item)" class="tab-pane-box-btn" >{{ item.value }}</el-button>
                </el-tooltip>
              </div>
              <div class="operator-box-item">
                <el-tooltip  v-for="item in operatorList.slice(10)" effect="dark" :content="item.label" placement="top" :show-after="500" >
                  <el-button plain @click="handleRowClick(3, item)" class="tab-pane-box-btn" > {{ item.value }} </el-button>
                </el-tooltip>
                <el-color-picker v-model="pickerColor" size="large" @change="handlePickerColorChange" />
              </div>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="函数表" name="2" v-if="props.openType === 'expression'" >
          <div style="min-height: 200px">
            <el-table :data="functionList" max-height="200" border :header-cell-style="headerCellStyle"
              :cell-style="cellStyle" @row-click="handleRowClick(4, $event)" >
              <el-table-column prop="value" label="名称">
                <template #default="scope">
                  <span>{{ scope.row.value.slice(1) }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="label" label="说明" />
            </el-table>
          </div>
        </el-tab-pane>
        <el-tab-pane label="页面变量" name="3" v-if="props.pageData.length">
          <div style="min-height: 200px">
            <el-table :data="pageDataList" max-height="200"  border :header-cell-style="headerCellStyle" :cell-style="cellStyle"
              row-key="value" @row-click="handleRowClick(1, $event)" >
              <el-table-column prop="name" label="名称" />
              <el-table-column prop="type" label="类型" />
            </el-table>
          </div>
        </el-tab-pane>
        <el-tab-pane label="全局变量" name="4" v-if="props.globalData.length">
          <div style="min-height: 200px">
            <el-table :data="globalDataList" max-height="200" border :header-cell-style="headerCellStyle" :cell-style="cellStyle"
              row-key="value" @row-click="handleRowClick(2, $event)" >
              <el-table-column prop="name" label="名称" />
              <el-table-column prop="type" label="类型" />
            </el-table>
          </div>
        </el-tab-pane>
        <el-tab-pane label="页面参数" name="5" v-if="props.pageParams.length">
          <div style="min-height: 200px">
            <el-table :data="pageParamsDataList" max-height="200" border :header-cell-style="headerCellStyle" :cell-style="cellStyle"
              @row-click="handleRowClick(5, $event)" >
              <el-table-column prop="name" label="参数名" />
              <el-table-column prop="type" label="数据类型" />
            </el-table>
          </div>
        </el-tab-pane>
        <el-tab-pane label="枚举变量" name="6" v-if="props.openType === 'variablesEnum'" >
          <div style="min-height: 200px">
            <el-table :data="expressionList" max-height="200" border :header-cell-style="headerCellStyle"
              :cell-style="cellStyle" @row-click="handleRowClick(6, $event)" >
              <el-table-column prop="label" label="类型" />
              <el-table-column prop="value" label="值" />
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-dialog>
</template>

<script setup>
import { watch, ref, onMounted, inject, nextTick, computed } from "vue";
const props = defineProps({
  openType: {
    // 类型 expression-表达式 variables-变量  variablesEnum-枚举变量
    type: String,
    default: "expression",
  },
  returnType: {
    type: String,
    default: "String", // 输出的变量类型，首字母需大些，例：String，Number，Boolean
  },
  modelValue: {
    // 对话框是否显示
    type: Boolean,
    default: false,
  },
  pageData: {
    // 页面变量
    type: Array,
    default: () => [
      // {
      //   name: "",
      //   type: "",
      //   value: "",
      //   children: [],
      // },
    ],
  },
  globalData: {
    // 全局变量
    type: Array,
    default: () => [
      // {
      //   name: "",
      //   type: "",
      //   value: "",
      //   children: [],
      // },
    ],
  },
  pageParams: {
    // 页面参数
    type: Array,
    default: () => [
      // {
      //   name: "",
      //   value: "",
      //   type: "",
      // },
    ],
  },
  textareaDefaultValue: {
    // 默认值
    type: String,
    default: "",
  },
});
const emit = defineEmits(["update:modelValue", "success"]);

const headerCellStyle = {
  background: "#FAFAFA",
  fontWeight: 500,
  fontSize: "14px",
  padding: "4px 0",
  color: "#000",
};
const cellStyle = {
  fontSize: "14",
  padding: "4px 0",
  color: "#000",
  cursor: "pointer",
};
const activeName = ref("1");
// :concat( ref:PageData.userInfo.name.firstName , ref:PageParams.status ) + ref:PageParams.status
const expressionTxt = ref(""); //表达式内容

const textareaRef = ref(null);
const cursorPosition = ref(0); // 光标位置

const operatorList = ref([
  {
    label: "加",
    value: "+",
  },
  {
    label: "减",
    value: "-",
  },
  {
    label: "乘",
    value: "*",
  },
  {
    label: "除",
    value: "/",
  },
  {
    label: "等于",
    value: "==",
  },
  {
    label: "不等于",
    value: "!=",
  },
  {
    label: "大于",
    value: ">",
  },
  {
    label: "小于",
    value: "<",
  },
  {
    label: "大于等于",
    value: ">=",
  },
  {
    label: "小于等于",
    value: "<=",
  },
  {
    label: "且",
    value: "&&",
  },
  {
    label: "或",
    value: "||",
  },
  {
    label: "取反",
    value: "!",
  },
  {
    label: "括号",
    value: "(",
  },
  {
    label: "括号",
    value: ")",
  },
]);
const functionList = ref([
  {
    label: "拼接",
    value: ":concat()",
    paramsLength: 2,
  },
  {
    label: "字符串截取",
    value: ":substring()",
    paramsLength: 2,
  },
  {
    label: "字符串替换",
    value: ":replace()",
    paramsLength: 2,
  },
  {
    label: "数组截取",
    value: ":slice()",
    paramsLength: 2,
  },
  {
    label: "数组替换",
    value: ":splice()",
    paramsLength: 2,
  },
  {
    label: "数组反转",
    value: ":reverse()",
    paramsLength: 1,
  },
  {
    label: "数组过滤",
    value: ":filter()",
    paramsLength: 2,
  },
  {
    label: "数组查找",
    value: ":find()",
    paramsLength: 2,
  },
  {
    label: "数组查找索引",
    value: ":findIndex()",
    paramsLength: 2,
  },
  {
    label: "数组是否包含",
    value: ":includes()",
    paramsLength: 2,
  },
  {
    label: "条件判断",
    value: ":if()",
    paramsLength: 3,
  },
]);
const expressionList = ref([
  {
    label: "radio",
    value: "radio( ref:xxx , name , value )",
  },
  {
    label: "checkbox",
    value: "checkbox( ref:xxx , name , value )",
  },
  {
    label: "select",
    value: "select( ref:xxx , name , value )",
  },
]);
const pickerColor = ref("");

const typeEnum = {
  string: "''",
  number: 0,
  boolean: true,
  object: {},
  array: [],
};

const pageDataList = computed(() => {
  // 绑定变量/绑定枚举变量弹窗 且 返回类型为 Array 过滤出array类型数据供用户选择
  if (props.openType !== "expression" && props.returnType === "Array") {
    return filterArrayTypeNodes(props.pageData);
  } else {
    return props.pageData;
  }
});

const globalDataList = computed(() => {
  console.log(props.globalData);
  // 绑定变量/绑定枚举变量弹窗 且 返回类型为 Array 过滤出array类型数据供用户选择
  if (props.openType !== "expression" && props.returnType === "Array") {
    return filterArrayTypeNodes(props.globalData);
  } else {
    return props.globalData;
  }
});

const pageParamsDataList = computed(() => {
  // 绑定变量/绑定枚举变量弹窗 且 返回类型为 Array 过滤出array类型数据供用户选择
  if (props.openType !== "expression" && props.returnType === "Array") {
    return filterArrayTypeNodes(props.pageParams);
  } else {
    return props.pageParams;
  }
});

//过滤出array类型数据
const filterArrayTypeNodes = (data) => {
  return data
    .filter((item) => {
      // 先处理子节点
      if (item.children && item.children.length > 0) {
        item.children = filterArrayTypeNodes(item.children);
      }
      // 保留当前节点或者有符合条件的子节点
      return (
        item.type === "array" || (item.children && item.children.length > 0)
      );
    })
    .map((item) => {
      // 如果当前节点不符合条件，但子节点符合，则返回一个只包含子节点的新节点
      if (item.type !== "array" && item.children && item.children.length > 0) {
        return {
          ...item,
          children: item.children,
        };
      }
      return item;
    });
};

const handleClear = () => {
  expressionTxt.value = ""; // 清空表达式内容
  emit("success", ""); //
};

const handlePickerColorChange = (e) => {
  console.log(e);
  if (e) {
    expressionTxt.value += ` '${e}'`;
  }
};

// 更新光标位置
const updateCursorPosition = () => {
  cursorPosition.value = textareaRef.value.selectionStart;
};

const handleCancel = () => {
  expressionTxt.value = ""; // 清空表达式内容
  emit("update:modelValue", false); // 关闭对话框
};

// 定义递归函数
const flattenChildren = (arr) => {
  let result = [];
  arr.forEach((item) => {
    result.push(item);
    if (item.children && item.children.length) {
      result = result.concat(flattenChildren(item.children));
    }
  });
  return result;
};

// 定义数组转换函数
const convertArray = (arr) => {
  const arrCopy = [...arr]; // 复制数组，避免修改原始数组
  console.log(arrCopy);
  // 函数下标数组
  const functionIndexArr = [];
  for (let i = 0; i < arrCopy.length; i++) {
    const item = arrCopy[i]; // 获取当前元素
    // 使用递归函数将页面变量数组 props.pageData 扁平化
    const flatPageData = flattenChildren(props.pageData);
    // 使用递归函数将全局变量数组 props.globalData 扁平化
    const flatGlobalData = flattenChildren(props.globalData);

    if (item === "") {
      // 如果元素为空，则不执行任何操作
      continue;
    } else if (
      (functionList.value.some((fn) => fn.value.indexOf(item) >= 0) &&
        item !== "(" &&
        item !== ")") ||
      (item == ")" &&
        functionIndexArr.length &&
        functionIndexArr.length % 2 == 1)
    ) {
      console.log(i);
      // 检查当前元素是否为函数
      functionIndexArr.push(i); // 记录函数的下标
      continue;
    } else if (operatorList.value.some((op) => op.value === item)) {
      // 如果是运算符，则不执行任何操作
      continue;
    } else if (flatPageData.some((page) => page.value === item)) {
      // 检查当前元素是否为页面变量
      // 获取当前元素的类型
      const type = flatPageData.find((page) => page.value === item).type;
      arrCopy.splice(i, 1, typeEnum[type]); // 替换当前元素为对应的类型值
      continue;
    } else if (flatGlobalData.some((global) => global.value === item)) {
      // 检查当前元素是否为全局变量
      // 获取当前元素的类型
      const type = flatGlobalData.find((global) => global.value === item).type;
      arrCopy.splice(i, 1, typeEnum[type]); // 替换当前元素为对应的类型值
      continue;
    } else if (props.pageParams.some((param) => param.value === item)) {
      // 检查当前元素是否为页面参数
      const type = props.pageParams.find((param) => param.value === item).type;
      arrCopy.splice(i, 1, typeEnum[type]); // 替换当前元素为对应的类型值
      continue;
    }
    // else if (item !== "," && item !== "''") {
    //   // 如果都不是，则不执行任何操作
    //   console.log("表达式验证失败", item);
    //   return []; // 表达式验证失败
    // }
  }
  console.log(functionIndexArr);
  if (functionIndexArr.length) {
    // TODO: 处理函数 判断函数参数个数
    const functionParams = arrCopy
      .slice(functionIndexArr[0] + 1, functionIndexArr[1])
      .filter((item) => item !== ","); // 获取函数参数数组;

    console.log("函数参数数组", functionParams);

    // 检查函数参数个数是否正确
    const functionItem = functionList.value.find(
      (fn) => fn.value.indexOf(arrCopy[functionIndexArr[0]]) >= 0
    ); // 获取函数对象
    if (functionParams.length !== functionItem.paramsLength) {
      console.log("表达式验证失败", functionParams);
      return [];
    }

    // 删除arrCopy中functionIndexArr[0]到functionIndexArr[1]的元素
    arrCopy.splice(
      functionIndexArr[0],
      functionIndexArr[1] - functionIndexArr[0] + 1,
      "''"
    );
  }
  console.log(arrCopy);
  // 判断数组中是否还有函数
  if (
    arrCopy.some(
      (item) =>
        functionList.value.some((fn) => fn.value.indexOf(item) >= 0) &&
        item !== "(" &&
        item !== ")" &&
        item !== ""
    )
  ) {
    console.log(arrCopy);
    const newArr = convertArray(arrCopy); // 递归调用函数
    console.log(newArr); // 输出新数组，用于调试
    return newArr; // 返回新数组
  } else {
    console.log(arrCopy);
    return arrCopy;
  }
};

const handleCheck = (bool) => {
  if (!expressionTxt.value) {
    // 如果表达式内容为空，则不执行任何操作
    ElMessage.error(
      props.openType === "expression"
        ? "表达式内容不能为空"
        : "变量内容不能为空"
    );
    return false;
  } else {
    // if (props.openType === "expression") {
    //   // 表达式校验
    //   const txtArr = expressionTxt.value.trim().split(" "); // 将表达式内容转换为数组, 空格分隔
    //   const newArr = convertArray(txtArr);

    //   if (newArr.length > 0) {
    //     // 尝试执行表达式
    //     console.log(newArr.join(" "));
    //     try {
    //       const evalValue = eval(newArr.join(" "));
    //       console.log(evalValue);
    //       // 判断表达式输出类型
    //       if (
    //         Object.prototype.toString
    //           .call(evalValue)
    //           .indexOf(props.returnType) >= 0
    //       ) {
    //         if (bool) {
    //           ElMessage.success("验证通过"); // 表达式验证通过
    //         }
    //         return true; // 表达式验证通过
    //       } else {
    //         ElMessage.error("表达式返回类型验证失败"); // 表达式返回值类型验证失败
    //         return false;
    //       }
    //     } catch (error) {
    //       ElMessage.error("表达式验证失败"); // 表达式验证失败
    //       return false; // 表达式验证失败
    //     }
    //   } else {
    //     ElMessage.error("表达式验证失败"); // 表达式验证失败
    //     return false; // 表达式验证失败
    //   }
    // } else if (props.openType === "variables") {
    //   // 变量校验
    //   if (bool) {
    //     ElMessage.success("验证通过"); // 表达式验证通过
    //   }
    //   return true; // 表达式验证通过
    // } else {
    //   // 枚举变量校验
    //   const txtArray = expressionTxt.value.trim().split(" "); // 将表达式内容转换为数组, 空格分隔
    //   console.log(txtArray);
    //   // 判断是否存在枚举变量 expressionTxt.value 下任意一项
    //   const index = expressionList.value.findIndex(
    //     (item) => txtArray.findIndex((e) => e.includes(item.label)) >= 0
    //   );
    //   if (index >= 0) {
    //     // 判断是否存在全局/页面/参数变量
    //     // 使用递归函数将页面变量数组 props.pageData 扁平化
    //     const flatPageData = flattenChildren(props.pageData);
    //     // 使用递归函数将全局变量数组 props.globalData 扁平化
    //     const flatGlobalData = flattenChildren(props.globalData);
    //     if (
    //       flatPageData.some((item) => item.value === txtArray[1]) ||
    //       flatGlobalData.some(
    //         (item) =>
    //           item.value === txtArray[1] ||
    //           props.pageParams.some((item) => item.value === txtArray[1])
    //       )
    //     ) {
    //       if (bool) {
    //         ElMessage.success("验证通过"); // 表达式验证通过
    //       }
    //       return true; // 表达式验证通过
    //     } else {
    //       ElMessage.error("表达式验证失败");
    //       return false;
    //     }
    //   } else {
    //     ElMessage.error("表达式验证失败");
    //     return false;
    //   }
    // }
	return true
  }
};

// 在光标位置插入文本
const insertText = (textToInsert) => {
  const currentText = expressionTxt.value; // 获取当前文本内容

  // 在光标位置插入文本
  expressionTxt.value =
    currentText.substring(0, cursorPosition.value) +
    textToInsert +
    currentText.substring(cursorPosition.value);

  // 更新光标位置到插入文本之后
  const newCursorPosition = cursorPosition.value + textToInsert.length;

  // 使用 nextTick 确保 DOM 更新后再设置光标位置
  nextTick(() => {
    textareaRef.value.setSelectionRange(newCursorPosition, newCursorPosition);
    textareaRef.value.focus();
    updateCursorPosition();
  });
};

const handleRowClick = (type, row) => {
  if (props.openType === "expression" || props.openType === "variablesEnum") {
    if (type === 6) {
      //选择枚举变量覆盖
      expressionTxt.value = row.value;
    } else {
      insertText(` ${row.value}`); // 拼接表达式
    }
  } else {
    // 绑定变量
    expressionTxt.value = row.value; // 覆盖变量
  }
};
const handleSubmit = () => {
  if (handleCheck(false)) {
    emit("success", expressionTxt.value); //
    expressionTxt.value = ""; // 清空表达式内容
  }
};

watch(
  () => props.modelValue,
  (val) => {
    if (val) {
      console.log(props.openType);
      expressionTxt.value = props.textareaDefaultValue || ""; // 初始化表达式内容
      // 初始化选中的标签页
      if (props.openType === "expression") {
        activeName.value = "1";
      } else {
        if (props.pageData.length) {
          activeName.value = "3";
        } else if (props.globalData.length) {
          activeName.value = "4";
        } else if (props.pageParams.length) {
          activeName.value = "5";
        } else {
          activeName.value = "";
        }
      }
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.dialog-content {
  .textarea-box {
    display: flex;
    margin-bottom: 10px;
    position: relative;
    textarea {
      flex-grow: 1;
      margin-right: 16px;
      box-sizing: border-box;
      height: 110px;
      resize: none;
      border: none;
      border-radius: 6px;
      padding: 10px;
      outline: none;
      background-color: #f5f5f5;
    }
    .clear-btn {
      position: absolute;
      top: 70px;
      right: 83px;
    }
    .el-button {
      display: block;
      margin: 0 0 6px 0;
    }
  }
  .tab-pane-box {
    min-height: 200px;
    //  display: flex;
    flex-wrap: wrap;
    .tab-pane-box-btn {
      width: 50px;
      height: 50px;
      font-size: 18px;
      margin: 0 10px 10px 0;
    }
    .operator-box {
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
      .operator-box-item {
        width: 120px;
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;
        flex-wrap: wrap;
        margin: 10px 30px 0 0;
        :deep(.el-color-picker--large .el-color-picker__trigger) {
          width: 50px;
          height: 50px;
        }
      }
      .operator-box-item:nth-child(2),
      .operator-box-item:nth-child(3) {
        width: 180px;
      }
    }
  }
}
</style>
