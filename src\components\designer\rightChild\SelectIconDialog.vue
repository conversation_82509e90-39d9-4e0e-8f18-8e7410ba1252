<template>
  <el-dialog
    :model-value="props.dialogVisible"
    title="选择图标"
    @close="handleClose"
    width="850px"
  >
    <el-tabs tab-position="left">
      <el-tab-pane label="图标列表">
        <div class="icon-list">
          <div
            v-for="(icon, index) in allIcons"
            :key="index"
            :class="['icon-item', { selected: selectedIcon === icon.name }]"
            @click="selectIcon(icon.name)"
          >
            <el-icon style="font-size: 24px">
              <component :is="icon.component" />
            </el-icon>
            <div class="icon-name">{{ icon.name }}</div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="hancleCancel">取消</el-button>
        <el-button type="primary" @click="submitIcon">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref} from "vue";
import * as ElIcons from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";

// 定义 props
const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
});

// 定义 emits
const emits = defineEmits(["update:dialogVisible", "change"]);

// 选中的图标名称
const selectedIcon = ref("");

// 所有 Element Plus 图标
const allIcons = Object.entries(ElIcons).map(([name, component]) => ({
  name,
  component,
}));

// 处理对话框关闭事件
const handleClose = () => {
  selectedIcon.value = "";
  emits("update:dialogVisible", false);
};

// 选择图标
const selectIcon = (iconName) => {
  selectedIcon.value = iconName;
};

// 提交选中的图标
const submitIcon = () => {
  if (!selectedIcon.value) {
    ElMessage.error("请选择图标");
    return;
  }
  console.log("选中的图标名称:", selectedIcon.value);
  // 这里可以添加回调函数，将选中的图标名称返回给父组件
  emits("change", selectedIcon.value);
};

// 取消按钮
const hancleCancel = () => {
  emits("update:dialogVisible", false);
};
</script>

<style scoped lang="scss">
.icon-list {
  display: flex;
  flex-wrap: wrap;
  height: 400px;
  overflow-y: auto;
}

.icon-item {
  cursor: pointer;
  border: 1px solid transparent;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 5px;
  width: 120px;
  padding: 10px 0;
  .icon-name {
    font-size: 14px;
    margin-top: 10px;
  }
}

.icon-item.selected {
  border-color: #409eff;
  border-radius: 4px;
}
</style>
