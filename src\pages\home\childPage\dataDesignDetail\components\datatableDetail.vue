<template>
  <el-descriptions :column="1" label-width="110">
    <el-descriptions-item label="表ID">
      {{ dataTableValue.id }}
    </el-descriptions-item>
    <el-descriptions-item label="表名">
      {{ dataTableValue.name }}--[{{ dataTableValue.fullname }}]
    </el-descriptions-item>
    <el-descriptions-item label="数据库">
      {{ dataTableValue.dbStoreName }}
    </el-descriptions-item>
    <el-descriptions-item label="创建人">
      {{ dataTableValue.creatorName }}
    </el-descriptions-item>
    <el-descriptions-item label="创建时间">
      {{ dataTableValue.createTime }}
    </el-descriptions-item>
    <el-descriptions-item label="备注">
      {{ dataTableValue.description }}
    </el-descriptions-item>
  </el-descriptions>
  <el-button type="primary" color="#2B6BFF" @click="handleUpdate">
    修改
  </el-button>
</template>

<script setup>
import { toRefs, } from "vue";

const props = defineProps({
  dataTableValue: {
    type: Object,
    default: {},
  },
});

const { dataTableValue } = toRefs(props);

const emit = defineEmits(["openDialog"]);

const handleUpdate = () => {
  emit("openDialog");
};
</script>
