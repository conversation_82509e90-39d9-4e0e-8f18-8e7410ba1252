<template>
	<div class="content" >
		<div class="title-panel">
			<div class="basic-btn" >
				<el-popover
				    placement="bottom-start" title="书写格式"
				    :width="200"
				    trigger="hover"
				  >
				    <template #reference>
				      <el-icon :size="24" color="#a7a7a7"><QuestionFilled /> </el-icon>
				    </template>
					<div>
						如定义number<br>
						number xxx = 0; <br>
						<br>
						如定义字符 <br>
						string xxx = "我是字符串"; <br>
						<br>
						如定义列表 <br>
						array xxx = ["sss",23,"567"]; <br>
						<br>
						如定义对象 <br>
						object xxx = { <br>
						&nbsp;name: "某人", <br>
						 &nbsp;age:0, <br>
						 &nbsp;school:{ <br>
						  &nbsp;&nbsp;address:"xxxxx路32号" <br>
						 &nbsp;} <br>
						}
					</div>
				  </el-popover>
			</div>
			<div class="title-label">{{props.title}}</div>
			<div class="basic-btn save-btn"  @click.stop="saveGlobalData()" v-if="props.showSaveBtn">
				<el-tooltip class="box-item" effect="light" :content="props.saveTip" placement="top-start" :show-after="500">
				<el-icon :size="24" color="#a7a7a7"><Finished /> </el-icon>
				</el-tooltip>
			</div>
			<div class="basic-btn refresh-btn" @click.stop="refreshData()" v-if="props.showRefreshBtn">
				<el-tooltip class="box-item" effect="light" :content="props.refreshTip" placement="top-start" :show-after="500">
				<el-icon :size="24" color="#a7a7a7"><Refresh /> </el-icon>
				</el-tooltip>
			</div>
		</div>
		<div class="text-panel" :style="{height:'calc(100vh - 245px - '+layoutParam.bottomTabHeight+'px)'}">
			<textarea class="text-input" :maxlength="-1" resize="none"
			       v-model="props.varData.varText" :placeholder="textholder" >
			</textarea>
		</div>
		
	</div>
</template>

<script lang="ts" setup>
	import {reactive,ref,inject,computed} from 'vue'
	// import { useExplainVar } from '../../../common/useExplainVar'
	const layoutParam = inject("layoutParam")
	// const { parse } = useExplainVar()
	const textholder = ref(`如定义number 
number xxx = 0; 

如定义字符 
string xxx = "我是字符串"; 

如定义列表
array xxx = ["123","556"]

如定义布尔值
boolean xxx = true

如定义对象 
object xxx = { 
 name: "某人", 
 age:0, 
 school:{ 
  address:"xxxxx路32号"
 } 
}`)

   const emit = defineEmits(["refreshData","saveData"])

    const props = defineProps({
		varData:{
			type:Object,
			default:{}
		},
		title:{
			type:String,
			default:""
		},
		showSaveBtn:{
			type:Boolean,
			default:true
		},
		showRefreshBtn:{
			type:Boolean,
			default:true
		},
		saveTip:{
			type:String,
			default:"保存变量"
		},
		refreshTip:{
			type:String,
			default:"刷新变量"
		}
	})

	function saveGlobalData(){
		emit('saveData',props.varData.varText)
	}
	
	function refreshData(){
		emit('refreshData')
	}
	
	const tipPropmt = computed(()=>{
		return textholder.value.replaceAll("\n","\t")
	})
	
</script>

<style lang="scss" scoped>
	 .content {
		 
		 .title-panel{
			 display: flex;
			 justify-content: space-between;
			 align-items: center;
			 padding-bottom: 5px;
			 border-bottom: 1px solid #d8d8d8;
			 .title-label{
				 flex:1;
				 font-size: 15px;
				 text-align: left;
			 }
			 .basic-btn {
				 width:26px;
				 height:26px;
				 border-radius: 20px;
				 display: flex;
				 justify-content: center;
				 align-items: center;
				 margin:2px 6px;
				 cursor:pointer;
				 transition:all 0.4s;
			 }
			 .refresh-btn{}
			 .refresh-btn:hover{
				 transform: rotate(90deg);
			 }
			 .refresh-btn:active{
			 	transform: rotate(-10deg);
			 }
			 .save-btn{}
			 .save-btn:hover{
				 transform: rotate(20deg);
			 }
			 .save-btn:active{
			 	transform: rotate(-10deg);
			 }
		 }
		 .text-panel{
			 padding-top: 10px;
			 display: flex;
			 justify-content: space-between;
			 align-items: stretch;
			 .text-input{
				 flex:1;
				 white-space: pre;
				 overflow: auto;
				 padding:10px;
				 outline:none;
				  background: #FAFAFA;
				  border:1px solid #d3d3d3;
				  text-align: left;
				  font-size: 15px;
			 }
		 }
		 
		 
	 }
	 textarea::placeholder{
		 color:#cacaca;
	 }
</style>