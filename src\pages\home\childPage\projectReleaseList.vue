<template>
  <div class="flex_box filter_head">
    <el-form :model="queryData" :inline="true">
      <el-form-item label="类型">
        <el-select
          v-model="queryData.type"
          placeholder="请选择"
          style="width: 111px"
        >
          <el-option
            v-for="item in typeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="所属">
        <el-select
          v-model="queryData.type"
          placeholder="请选择"
          style="width: 111px"
        >
          <el-option
            v-for="item in ownerTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
  </div>
  <el-table :data="listData" :show-header="false">
    <el-table-column width="112px">
      <template #default="scope">
        <img
          style="width: 88px; height: 88px; border-radius: 8px"
          :src="scope.row.pic"
        />
      </template>
    </el-table-column>
    <el-table-column>
      <template #default="scope">
        <div class="flex_box" style="justify-content: flex-start">
          <div style="font-weight: 600; font-size: 18px; color: #212121">
            {{ scope.row.applicationId }}
            &nbsp;&nbsp;|&nbsp;&nbsp;
            {{ scope.row.title }}
          </div>
          <div style="display: flex; align-items: center; margin-left: 24px">
            <img
              v-if="scope.row.category === 1"
              src="@/assets/images/list_icon_pone.png"
              style="width: 20px; height: 20px"
            />
            <img
              v-else
              src="@/assets/images/list_icon_comput.png"
              style="width: 20px; height: 20px"
            />
            <span style="margin: 0 8px 0 4px; font-size: 16px; color: #666666">
              {{ scope.row.category === 1 ? "手机" : "电脑" }}
            </span>
            <div
              :class="
                scope.row.ower === 0 ? 'tag_primary' : 'tag_primary tag_success'
              "
            >
              {{ scope.row.ower === 0 ? "直属" : "参与" }}
            </div>
          </div>
        </div>
        <div class="desc">
          发布：{{ scope.row.creator }}&nbsp;&nbsp;{{ scope.row.createTime }}
        </div>
        <div class="desc">版本号：{{ scope.row.versioncode }}</div>
      </template>
    </el-table-column>
    <el-table-column>
      <template #default="scope">
        <div style="font-size: 16px; color: #999999">
          {{ scope.row.description }}
        </div>
      </template>
    </el-table-column>
    <el-table-column width="100px">
      <template #default="{ row }">
        <el-button
          type="primary"
          color="#2B6BFF"
          @click="handleDeployment(row)"
        >
          布署
        </el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup>
import { onMounted, reactive, ref } from "vue";
import { applicationList } from "@/apis/homeApi";
import { useRouter } from "vue-router";

const router = useRouter();

const typeOptions = ref([
  {
    label: "全部",
    value: "0",
  },
  {
    label: "手机",
    value: "1",
  },
  {
    label: "电脑",
    value: "2",
  },
]);

const ownerTypeOptions = ref([
  {
    label: "全部",
    value: "0",
  },
  {
    label: "直属",
    value: "1",
  },
  {
    label: "参与",
    value: "2",
  },
]);

const queryData = reactive({
  type: "0",
  ownerType: "",
});

const listData = ref([]);

const handleGetList = async () => {
  try {
    const res = await applicationList(queryData);
    listData.value = res.data;
  } catch (error) {}
};

const handleDeployment = (row) => {
  router.push({ path: "/home/<USER>", query: { id: row.id } });
};

onMounted(() => {
  handleGetList();
});
</script>

<style lang="scss" scoped>
.filter_head {
  padding: 0 24px;
  height: 64px;
  background: #ffffff;
  box-shadow: inset 0px -1px 0px 0px #ededed;

  .el-form-item {
    margin-bottom: 0;
  }
}

.tag_primary {
  width: 36px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  background: rgba(43, 107, 255, 0.1);
  border-radius: 3px;
  font-size: 14px;
  color: #2b6bff;
}

.tag_success {
  background: rgba(44, 219, 102, 0.1);
  color: #2cdb66;
}

.desc {
  font-size: 16px;
  color: #999999;
  line-height: 19px;
}

.desc:nth-child(2) {
  margin: 8px 0;
}
</style>
