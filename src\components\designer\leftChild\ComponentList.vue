<template>
	<div class="content">
		<div class="list-box">
			<div class="item-box" v-for="(item,index) in props.dataList" :key="index" draggable="true" @dragstart.stop="onDragStart(index,$event)">
				<div class="item-icon">
					<div :style="{backgroundColor: ProjectRef.BasicData.theme.currentTheme.color}" class="cpt-line"
					   v-if="item.icon=='local:line'"></div>
					<div :style="{border:'3px solid'+ ProjectRef.BasicData.theme.currentTheme.color}" class="cpt-box"
					   v-if="item.icon=='local:box'"></div>
					<div :style="{color: ProjectRef.BasicData.theme.currentTheme.color}" class="cpt-text"
					   v-if="item.icon=='local:text'">T</div>
					<div :style="{backgroundColor: ProjectRef.BasicData.theme.currentTheme.color}" class="cpt-button"
					   v-if="item.icon=='local:button'">OK</div>
					<div  v-if="item.icon=='local:image'" style="margin-top: 10px;">
						<el-icon :size="38" :color="ProjectRef.BasicData.theme.currentTheme.color"><PictureFilled /></el-icon>
					</div>
					<div :style="{border: '2px solid '+ProjectRef.BasicData.theme.currentTheme.color}" class="cpt-radio"
					     v-if="item.icon=='local:radio'" >
                        <div :style="{backgroundColor: ProjectRef.BasicData.theme.currentTheme.color}"  class="cpt-radio-incycle"></div>
					</div>
					<div  v-if="item.icon=='local:checkbox'" style="margin-top: 6px;">
						<el-icon :size="38" :color="ProjectRef.BasicData.theme.currentTheme.color"><SuccessFilled /></el-icon>
					</div>
					<div  v-if="item.icon=='local:input'" class="cpt-input">
						<div class="cpt-input-img">
							<div class="cpt-input-a" :style="{border:'3px solid '+ ProjectRef.BasicData.theme.currentTheme.color}"></div>
							<div class="cpt-input-b" :style="{backgroundColor: ProjectRef.BasicData.theme.currentTheme.color}"></div>
						</div>
					</div>
					<div  v-if="item.icon=='local:area'" class="cpt-area" :style="{border:'3px solid '+ ProjectRef.BasicData.theme.currentTheme.color}">
						<div class="cpt-area-box" :style="{color:ProjectRef.BasicData.theme.currentTheme.color}">AI</div>
					</div>
					<div v-if="item.icon=='local:list'">
					   <el-icon  :size="50" :color="ProjectRef.BasicData.theme.currentTheme.color"><List /></el-icon>
					</div>
					<div v-if="item.icon=='local:icon'" style="margin-top: 10px;">
						<el-icon :size="38" :color="ProjectRef.BasicData.theme.currentTheme.color"><PictureRounded /></el-icon>
					</div>
					<div v-if="item.icon=='local:switch'" style="margin-top: 10px;">
						<div class="cpt-switch" :style="{border:'2px solid '+ProjectRef.BasicData.theme.currentTheme.color}">
							<div class="cpt-switch-inner" :style="{backgroundColor:ProjectRef.BasicData.theme.currentTheme.color}"></div>
						</div>
					</div>
					<div v-if="item.icon=='local:select'" style="margin-top: 10px;">
						<div class="cpt-select" :style="{border:'2px solid ' + ProjectRef.BasicData.theme.currentTheme.color }">
							<div class="cpt-select-inner" :style="{backgroundColor:ProjectRef.BasicData.theme.currentTheme.color}">
								<div class="cpt-select-arrow" ></div>
							</div>
						</div>
					</div>
					<div v-if="item.icon=='local:tab'" style="margin-top:10px;">
						<el-icon :size="38" :color="ProjectRef.BasicData.theme.currentTheme.color"><Files /></el-icon>
					</div>
					<div v-if="item.icon=='local:swiper'" style="margin-top:10px;">
						<!-- <el-icon :size="38" :color="ProjectRef.BasicData.theme.currentTheme.color"><Files /></el-icon> -->
						<div class="cpt-swiper" :style="{backgroundColor:  ProjectRef.BasicData.theme.currentTheme.color }"> 
							 ...
						</div>
					</div>
				</div>
				<div class="item-label">{{item.name}}</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
	import {reactive ,onMounted,inject,toRaw} from 'vue'
	const ProjectRef = inject("ProjectRef")
	const props = defineProps({
		dataList:{
			type:Array,
			default:[]
		}
	})
	
	// onMounted(()=>{
		
	// })
	
	function onDragStart(index,e){
		
		let tmpItem = JSON.parse(JSON.stringify(props.dataList[index])) 
		if(tmpItem.hasOwnProperty("events")){
			delete tmpItem.events
		}
		if(tmpItem.hasOwnProperty("attributes")){
			delete tmpItem.attributes
		}
		if(tmpItem.hasOwnProperty("icon")){
			delete tmpItem.icon
		}
		if(tmpItem.hasOwnProperty("others")){
			delete tmpItem.others
		}
		// console.log(JSON.stringify(tmpItem))
		e.dataTransfer.effectAllowed = 'move'
		e.dataTransfer.setData('componentItem',JSON.stringify(tmpItem))
	}
</script>

<style lang="scss" scoped>
	 .content {
		 
		 .list-box{
			 display: flex;
			 justify-content: flex-start;
			 align-items: flex-start;
			 flex-wrap: wrap;
			 margin-left:10px;
			 
			 .item-box{
				 width:90px;
				 height:90px;
				 border-radius: 6px; 
				 background-color: #F5F5F5;
				 margin:6px;
				 display: flex;
				 justify-content: center;
				 flex-direction: column;
				 align-items: stretch;
				 
				 .item-icon{
					 flex:1;
					 display: flex;
					 justify-content: center;
					 align-items: center;
					 margin-top:10px;
					 
					 .cpt-line {
						 width:35px;
						 height:2px;
						 transform: rotate(45deg);
						 background-color: #2B6BFF;
					 }
					 .cpt-button {
						 font-size: 14px;
						 color: white;
						 background-color: #2B6BFF;
						 border-radius: 5px;
						 padding:2px 10px;
					 }
					 .cpt-radio {
						 width:30px;
						 height:30px;
						 border-radius: 30px;
						 border:2px solid #2B6BFF;
						 display: flex;
						 justify-content: center;
						 align-items: center;
						 
						 .cpt-radio-incycle{
							 width:24px;
							 height:24px;
							 background-color: #2B6BFF;
							 border-radius: 30px;
						 }
					 }
					 .cpt-text {
						 font-size: 36px;
						 color: #2B6BFF;
					 }
					 .cpt-box{
						 width:30px;
						 height:30px;
						 border:3px solid #2B6BFF;
					 }
					 .cpt-list{
						 width:30px;
						 height:30px;
						 // border:3px solid #2B6BFF;
					 }
					 .cpt-input {
						 
						 .cpt-input-img {
							 position: relative;
							 // border:1px solid red;
							 width:30px;
							 height:30px;
							 .cpt-input-a {
								 position: absolute;
								 top:9px;
								 left:-2px;
								 width:30px;
								 height:12px;
								 border:3px solid #2B6BFF;
								 border-radius: 5px;
							 }
							 .cpt-input-b {
								 position: absolute;
								 top:4px;
								 left:8px;
								 height:26px;
								 width:3px;
								 background-color: #2B6BFF;
							 }
						 }
					 }
					 .cpt-area{
						 width:26px;
						 height:26px;
						 border:3px solid #2B6BFF;
						 border-radius: 5px;
						 display: flex;
						 justify-content: center;
						 align-items: center;
						 .cpt-area-box{
							 color:#2B6BFF;
							 font-size: 18px;
							 font-weight: 600;
						 }
					 }
					 .cpt-switch{
						 width:26px;
						 height:15px;
						 display: flex;
						 justify-content: flex-start;
						 align-items: center;
						 border-radius: 15px;
						 padding:2px;
						 .cpt-switch-inner{
							 width:14px;
							 height:14px;
							 border-radius: 10px;
						 }
					 }
					 .cpt-select{
						 border:1px solid blue;
						 width:35px;
						 height:16px;
						 display: flex;
						 justify-content: flex-end;
						 align-items: center;
						 border-radius: 2px;
						 .cpt-select-inner{
							 width:15px;
							 height:15px;
							 background-color: blue;
							 border-radius: 2px;
							 display: flex;
							 justify-content: center;
							 align-items: center;
							 padding-bottom: 3px;
							 .cpt-select-arrow{
								 background-color: transparent;
								 border-left: 3px solid white;
								 border-bottom:3px solid white;
								 width:4px;
								 height:4px;
								 transform: rotate(-45deg);
							 }
						 }
					 }
					 .cpt-swiper{
						width:35px;
						 height:22px;
						 padding-bottom: 5px;
						 color:white;
						 font-size: 28px;
						 text-align: center;
						 line-height: 22px;
						 border-radius: 5px;
					 }
					 
				 }
				 
				 .item-label{
					 font-size: 13px;
					 color:#666666;
					 margin-bottom: 10px;
					 text-align: center;
					 // border:1px solid red;
				 }
			 }
		 }
	 }
</style>