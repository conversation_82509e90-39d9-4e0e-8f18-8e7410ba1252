import{H,ab as le,_ as Q,K as z,an as ae,L as _,M as I,c as K,o as w,a as N,D as j,Q as E,n as p,v as e,t as se,b as O,f as g,g as q,R as te,bZ as ne,y as re,j as Y,aA as ie,J as de,au as ue,aI as ce,e as S,b_ as fe,V as ve,ag as U,B as ye,b$ as me,c0 as J,aB as pe,a2 as ge,T as Ce,N as be,aL as he,w as Ee,aE as Se,aS as Ie,ah as ke,Z as we,S as De}from"./index-Dgb0NZ1J.js";import{u as Be,a as Te,b as Ae,c as Fe}from"./index-DzgKufUD.js";import{F as Pe,E as $e}from"./vnode-CV7pw3rS.js";import{t as Le,a as Re}from"./el-scrollbar-DLTsEwUl.js";import{c as Me}from"./refs-CFD094o4.js";import{U as W}from"./index-CINbulG0.js";import{h as G,c as Ne}from"./el-button-B8TCQS4u.js";const X=Symbol("dialogInjectionKey"),x=H({center:Boolean,alignCenter:Boolean,closeIcon:{type:le},draggable:Boolean,overflow:Boolean,fullscreen:Boolean,headerClass:String,bodyClass:String,footerClass:String,showClose:{type:Boolean,default:!0},title:{type:String,default:""},ariaLevel:{type:String,default:"2"}}),Oe={close:()=>!0},ze=z({name:"ElDialogContent"}),Ve=z({...ze,props:x,emits:Oe,setup(o,{expose:i}){const n=o,{t:P}=ae(),{Close:r}=ne,{dialogRef:f,headerRef:C,bodyId:b,ns:a,style:d}=_(X),{focusTrapRef:y}=_(Pe),m=I(()=>[a.b(),a.is("fullscreen",n.fullscreen),a.is("draggable",n.draggable),a.is("align-center",n.alignCenter),{[a.m("center")]:n.center}]),h=Me(y,f),u=I(()=>n.draggable),v=I(()=>n.overflow),{resetPosition:D,updatePosition:B}=Be(f,C,u,v);return i({resetPosition:D,updatePosition:B}),(s,k)=>(w(),K("div",{ref:e(h),class:p(e(m)),style:Y(e(d)),tabindex:"-1"},[N("header",{ref_key:"headerRef",ref:C,class:p([e(a).e("header"),s.headerClass,{"show-close":s.showClose}])},[E(s.$slots,"header",{},()=>[N("span",{role:"heading","aria-level":s.ariaLevel,class:p(e(a).e("title"))},se(s.title),11,["aria-level"])]),s.showClose?(w(),K("button",{key:0,"aria-label":e(P)("el.dialog.close"),class:p(e(a).e("headerbtn")),type:"button",onClick:$=>s.$emit("close")},[O(e(re),{class:p(e(a).e("close"))},{default:g(()=>[(w(),q(te(s.closeIcon||e(r))))]),_:1},8,["class"])],10,["aria-label","onClick"])):j("v-if",!0)],2),N("div",{id:e(b),class:p([e(a).e("body"),s.bodyClass])},[E(s.$slots,"default")],10,["id"]),s.$slots.footer?(w(),K("footer",{key:0,class:p([e(a).e("footer"),s.footerClass])},[E(s.$slots,"footer")],2)):j("v-if",!0)],6))}});var Ke=Q(Ve,[["__file","dialog-content.vue"]]);const Ue=H({...x,appendToBody:Boolean,appendTo:{type:Le.to.type,default:"body"},beforeClose:{type:de(Function)},destroyOnClose:Boolean,closeOnClickModal:{type:Boolean,default:!0},closeOnPressEscape:{type:Boolean,default:!0},lockScroll:{type:Boolean,default:!0},modal:{type:Boolean,default:!0},openDelay:{type:Number,default:0},closeDelay:{type:Number,default:0},top:{type:String},modelValue:Boolean,modalClass:String,headerClass:String,bodyClass:String,footerClass:String,width:{type:[String,Number]},zIndex:{type:Number},trapFocus:Boolean,headerAriaLevel:{type:String,default:"2"}}),je={open:()=>!0,opened:()=>!0,close:()=>!0,closed:()=>!0,[W]:o=>ie(o),openAutoFocus:()=>!0,closeAutoFocus:()=>!0},qe=(o,i)=>{var n;const r=ue().emit,{nextZIndex:f}=ce();let C="";const b=G(),a=G(),d=S(!1),y=S(!1),m=S(!1),h=S((n=o.zIndex)!=null?n:f());let u,v;const D=fe("namespace",me),B=I(()=>{const t={},c=`--${D.value}-dialog`;return o.fullscreen||(o.top&&(t[`${c}-margin-top`]=o.top),o.width&&(t[`${c}-width`]=ve(o.width))),t}),s=I(()=>o.alignCenter?{display:"flex"}:{});function k(){r("opened")}function $(){r("closed"),r(W,!1),o.destroyOnClose&&(m.value=!1)}function V(){r("close")}function L(){v==null||v(),u==null||u(),o.openDelay&&o.openDelay>0?{stop:u}=J(()=>R(),o.openDelay):R()}function T(){u==null||u(),v==null||v(),o.closeDelay&&o.closeDelay>0?{stop:v}=J(()=>M(),o.closeDelay):M()}function A(){function t(c){c||(y.value=!0,d.value=!1)}o.beforeClose?o.beforeClose(t):T()}function F(){o.closeOnClickModal&&A()}function R(){pe&&(d.value=!0)}function M(){d.value=!1}function l(){r("openAutoFocus")}function Z(){r("closeAutoFocus")}function ee(t){var c;((c=t.detail)==null?void 0:c.focusReason)==="pointer"&&t.preventDefault()}o.lockScroll&&Te(d);function oe(){o.closeOnPressEscape&&A()}return U(()=>o.zIndex,()=>{var t;h.value=(t=o.zIndex)!=null?t:f()}),U(()=>o.modelValue,t=>{var c;t?(y.value=!1,L(),m.value=!0,h.value=(c=o.zIndex)!=null?c:f(),ge(()=>{r("open"),i.value&&(i.value.parentElement.scrollTop=0,i.value.parentElement.scrollLeft=0,i.value.scrollTop=0)})):d.value&&T()}),U(()=>o.fullscreen,t=>{i.value&&(t?(C=i.value.style.transform,i.value.style.transform=""):i.value.style.transform=C)}),ye(()=>{o.modelValue&&(d.value=!0,m.value=!0,L())}),{afterEnter:k,afterLeave:$,beforeLeave:V,handleClose:A,onModalClick:F,close:T,doClose:M,onOpenAutoFocus:l,onCloseAutoFocus:Z,onCloseRequested:oe,onFocusoutPrevented:ee,titleId:b,bodyId:a,closed:y,style:B,overlayDialogStyle:s,rendered:m,visible:d,zIndex:h}},Ze=z({name:"ElDialog",inheritAttrs:!1}),_e=z({...Ze,props:Ue,emits:je,setup(o,{expose:i}){const n=o,P=Ce();Ne({scope:"el-dialog",from:"the title slot",replacement:"the header slot",version:"3.0.0",ref:"https://element-plus.org/en-US/component/dialog.html#slots"},I(()=>!!P.title));const r=be("dialog"),f=S(),C=S(),b=S(),{visible:a,titleId:d,bodyId:y,style:m,overlayDialogStyle:h,rendered:u,zIndex:v,afterEnter:D,afterLeave:B,beforeLeave:s,handleClose:k,onModalClick:$,onOpenAutoFocus:V,onCloseAutoFocus:L,onCloseRequested:T,onFocusoutPrevented:A}=qe(n,f);we(X,{dialogRef:f,headerRef:C,bodyId:y,ns:r,rendered:u,style:m});const F=Fe($),R=I(()=>n.draggable&&!n.fullscreen);return i({visible:a,dialogContentRef:b,resetPosition:()=>{var l;(l=b.value)==null||l.resetPosition()},handleClose:k}),(l,Z)=>(w(),q(e(Re),{to:l.appendTo,disabled:l.appendTo!=="body"?!1:!l.appendToBody},{default:g(()=>[O(he,{name:"dialog-fade",onAfterEnter:e(D),onAfterLeave:e(B),onBeforeLeave:e(s),persisted:""},{default:g(()=>[Ee(O(e(Ae),{"custom-mask-event":"",mask:l.modal,"overlay-class":l.modalClass,"z-index":e(v)},{default:g(()=>[N("div",{role:"dialog","aria-modal":"true","aria-label":l.title||void 0,"aria-labelledby":l.title?void 0:e(d),"aria-describedby":e(y),class:p(`${e(r).namespace.value}-overlay-dialog`),style:Y(e(h)),onClick:e(F).onClick,onMousedown:e(F).onMousedown,onMouseup:e(F).onMouseup},[O(e($e),{loop:"",trapped:e(a),"focus-start-el":"container",onFocusAfterTrapped:e(V),onFocusAfterReleased:e(L),onFocusoutPrevented:e(A),onReleaseRequested:e(T)},{default:g(()=>[e(u)?(w(),q(Ke,Se({key:0,ref_key:"dialogContentRef",ref:b},l.$attrs,{center:l.center,"align-center":l.alignCenter,"close-icon":l.closeIcon,draggable:e(R),overflow:l.overflow,fullscreen:l.fullscreen,"header-class":l.headerClass,"body-class":l.bodyClass,"footer-class":l.footerClass,"show-close":l.showClose,title:l.title,"aria-level":l.headerAriaLevel,onClose:e(k)}),Ie({header:g(()=>[l.$slots.title?E(l.$slots,"title",{key:1}):E(l.$slots,"header",{key:0,close:e(k),titleId:e(d),titleClass:e(r).e("title")})]),default:g(()=>[E(l.$slots,"default")]),_:2},[l.$slots.footer?{name:"footer",fn:g(()=>[E(l.$slots,"footer")])}:void 0]),1040,["center","align-center","close-icon","draggable","overflow","fullscreen","header-class","body-class","footer-class","show-close","title","aria-level","onClose"])):j("v-if",!0)]),_:3},8,["trapped","onFocusAfterTrapped","onFocusAfterReleased","onFocusoutPrevented","onReleaseRequested"])],46,["aria-label","aria-labelledby","aria-describedby","onClick","onMousedown","onMouseup"])]),_:3},8,["mask","overlay-class","z-index"]),[[ke,e(a)]])]),_:3},8,["onAfterEnter","onAfterLeave","onBeforeLeave"])]),_:3},8,["to","disabled"]))}});var Je=Q(_e,[["__file","dialog.vue"]]);const eo=De(Je);export{eo as E};
