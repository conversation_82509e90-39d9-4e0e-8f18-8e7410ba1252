import{j as fe,E as be}from"./el-button-B8TCQS4u.js";import{aC as ke,bx as xe,H as q,by as Re,ab as Oe,_ as le,K as M,an as ze,N as H,e as N,M as L,V as Ae,g as Z,o as U,f as D,Q as j,D as ne,a as ee,n as F,v as h,k as te,y as K,j as ye,R as Le,t as ae,b,aE as Me,S as ge,I as Te,J as re,L as ie,ag as I,bz as oe,bg as Ve,c as Ce,au as ce,a2 as ue,bA as Fe,bB as Ie,B as _e,aY as De,ax as Ue,aw as Ke,ai as z,a5 as je,af as qe,O as He,G as We,aX as ve,Z as Xe,T as Ye,bC as pe,d as Ge,b8 as Je,w as Qe,ah as Ze,U as et}from"./index-Dgb0NZ1J.js";import{u as me,E as tt}from"./el-scrollbar-DLTsEwUl.js";import{t as de,U as Be}from"./index-CINbulG0.js";import{c as A}from"./el-select-DNFSsBSe.js";import{f as at}from"./vnode-CV7pw3rS.js";const st=(e,o,c)=>at(e.subTree).filter(n=>{var u;return xe(n)&&((u=n.type)==null?void 0:u.name)===o&&!!n.component}).map(n=>n.component.uid).map(n=>c[n]).filter(n=>!!n),nt=(e,o)=>{const c={},f=ke([]);return{children:f,addChild:u=>{c[u.uid]=u,f.value=st(e,o,c)},removeChild:u=>{delete c[u],f.value=f.value.filter(v=>v.uid!==u)}}},ot=q({title:String,confirmButtonText:String,cancelButtonText:String,confirmButtonType:{type:String,values:fe,default:"primary"},cancelButtonType:{type:String,values:fe,default:"text"},icon:{type:Oe,default:()=>Re},iconColor:{type:String,default:"#f90"},hideIcon:{type:Boolean,default:!1},hideAfter:{type:Number,default:200},teleported:me.teleported,persistent:me.persistent,width:{type:[String,Number],default:150}}),lt={confirm:e=>e instanceof MouseEvent,cancel:e=>e instanceof MouseEvent},rt=M({name:"ElPopconfirm"}),it=M({...rt,props:ot,emits:lt,setup(e,{emit:o}){const c=e,{t:f}=ze(),a=H("popconfirm"),n=N(),u=()=>{var t,l;(l=(t=n.value)==null?void 0:t.onClose)==null||l.call(t)},v=L(()=>({width:Ae(c.width)})),m=t=>{o("confirm",t),u()},C=t=>{o("cancel",t),u()},y=L(()=>c.confirmButtonText||f("el.popconfirm.confirmButtonText")),d=L(()=>c.cancelButtonText||f("el.popconfirm.cancelButtonText"));return(t,l)=>(U(),Z(h(tt),Me({ref_key:"tooltipRef",ref:n,trigger:"click",effect:"light"},t.$attrs,{"popper-class":`${h(a).namespace.value}-popover`,"popper-style":h(v),teleported:t.teleported,"fallback-placements":["bottom","top","right","left"],"hide-after":t.hideAfter,persistent:t.persistent}),{content:D(()=>[ee("div",{class:F(h(a).b())},[ee("div",{class:F(h(a).e("main"))},[!t.hideIcon&&t.icon?(U(),Z(h(K),{key:0,class:F(h(a).e("icon")),style:ye({color:t.iconColor})},{default:D(()=>[(U(),Z(Le(t.icon)))]),_:1},8,["class","style"])):ne("v-if",!0),te(" "+ae(t.title),1)],2),ee("div",{class:F(h(a).e("action"))},[j(t.$slots,"actions",{confirm:m,cancel:C},()=>[b(h(be),{size:"small",type:t.cancelButtonType==="text"?"":t.cancelButtonType,text:t.cancelButtonType==="text",onClick:C},{default:D(()=>[te(ae(h(d)),1)]),_:1},8,["type","text"]),b(h(be),{size:"small",type:t.confirmButtonType==="text"?"":t.confirmButtonType,text:t.confirmButtonType==="text",onClick:m},{default:D(()=>[te(ae(h(y)),1)]),_:1},8,["type","text"])])],2)],2)]),default:D(()=>[t.$slots.reference?j(t.$slots,"reference",{key:0}):ne("v-if",!0)]),_:3},16,["popper-class","popper-style","teleported","hide-after","persistent"]))}});var ct=le(it,[["__file","popconfirm.vue"]]);const kt=ge(ct),X=Symbol("tabsRootContextKey"),ut=q({tabs:{type:re(Array),default:()=>Te([])}}),Ne="ElTabBar",dt=M({name:Ne}),ft=M({...dt,props:ut,setup(e,{expose:o}){const c=e,f=ce(),a=ie(X);a||de(Ne,"<el-tabs><el-tab-bar /></el-tabs>");const n=H("tabs"),u=N(),v=N(),m=()=>{let l=0,_=0;const g=["top","bottom"].includes(a.props.tabPosition)?"width":"height",R=g==="width"?"x":"y",r=R==="x"?"left":"top";return c.tabs.every(w=>{var P,k;const B=(k=(P=f.parent)==null?void 0:P.refs)==null?void 0:k[`tab-${w.uid}`];if(!B)return!1;if(!w.active)return!0;l=B[`offset${A(r)}`],_=B[`client${A(g)}`];const $=window.getComputedStyle(B);return g==="width"&&(_-=Number.parseFloat($.paddingLeft)+Number.parseFloat($.paddingRight),l+=Number.parseFloat($.paddingLeft)),!1}),{[g]:`${_}px`,transform:`translate${A(R)}(${l}px)`}},C=()=>v.value=m(),y=[],d=()=>{var l;y.forEach(g=>g.stop()),y.length=0;const _=(l=f.parent)==null?void 0:l.refs;if(_){for(const g in _)if(g.startsWith("tab-")){const R=_[g];R&&y.push(oe(R,C))}}};I(()=>c.tabs,async()=>{await ue(),C(),d()},{immediate:!0});const t=oe(u,()=>C());return Ve(()=>{y.forEach(l=>l.stop()),y.length=0,t.stop()}),o({ref:u,update:C}),(l,_)=>(U(),Ce("div",{ref_key:"barRef",ref:u,class:F([h(n).e("active-bar"),h(n).is(h(a).props.tabPosition)]),style:ye(v.value)},null,6))}});var bt=le(ft,[["__file","tab-bar.vue"]]);const vt=q({panes:{type:re(Array),default:()=>Te([])},currentName:{type:[String,Number],default:""},editable:Boolean,type:{type:String,values:["card","border-card",""],default:""},stretch:Boolean}),pt={tabClick:(e,o,c)=>c instanceof Event,tabRemove:(e,o)=>o instanceof Event},he="ElTabNav",mt=M({name:he,props:vt,emits:pt,setup(e,{expose:o,emit:c}){const f=ie(X);f||de(he,"<el-tabs><tab-nav /></el-tabs>");const a=H("tabs"),n=Fe(),u=Ie(),v=N(),m=N(),C=N(),y=N(),d=N(!1),t=N(0),l=N(!1),_=N(!0),g=L(()=>["top","bottom"].includes(f.props.tabPosition)?"width":"height"),R=L(()=>({transform:`translate${g.value==="width"?"X":"Y"}(-${t.value}px)`})),r=()=>{if(!v.value)return;const i=v.value[`offset${A(g.value)}`],p=t.value;if(!p)return;const s=p>i?p-i:0;t.value=s},w=()=>{if(!v.value||!m.value)return;const i=m.value[`offset${A(g.value)}`],p=v.value[`offset${A(g.value)}`],s=t.value;if(i-s<=p)return;const E=i-s>p*2?s+p:i-p;t.value=E},P=async()=>{const i=m.value;if(!d.value||!C.value||!v.value||!i)return;await ue();const p=C.value.querySelector(".is-active");if(!p)return;const s=v.value,E=["top","bottom"].includes(f.props.tabPosition),T=p.getBoundingClientRect(),S=s.getBoundingClientRect(),W=E?i.offsetWidth-S.width:i.offsetHeight-S.height,O=t.value;let x=O;E?(T.left<S.left&&(x=O-(S.left-T.left)),T.right>S.right&&(x=O+T.right-S.right)):(T.top<S.top&&(x=O-(S.top-T.top)),T.bottom>S.bottom&&(x=O+(T.bottom-S.bottom))),x=Math.max(x,0),t.value=Math.min(x,W)},k=()=>{var i;if(!m.value||!v.value)return;e.stretch&&((i=y.value)==null||i.update());const p=m.value[`offset${A(g.value)}`],s=v.value[`offset${A(g.value)}`],E=t.value;s<p?(d.value=d.value||{},d.value.prev=E,d.value.next=E+s<p,p-E<s&&(t.value=p-s)):(d.value=!1,E>0&&(t.value=0))},B=i=>{let p=0;switch(i.code){case z.left:case z.up:p=-1;break;case z.right:case z.down:p=1;break;default:return}const s=Array.from(i.currentTarget.querySelectorAll("[role=tab]:not(.is-disabled)"));let T=s.indexOf(i.target)+p;T<0?T=s.length-1:T>=s.length&&(T=0),s[T].focus({preventScroll:!0}),s[T].click(),$()},$=()=>{_.value&&(l.value=!0)},Y=()=>l.value=!1;return I(n,i=>{i==="hidden"?_.value=!1:i==="visible"&&setTimeout(()=>_.value=!0,50)}),I(u,i=>{i?setTimeout(()=>_.value=!0,50):_.value=!1}),oe(C,k),_e(()=>setTimeout(()=>P(),0)),De(()=>k()),o({scrollToActiveTab:P,removeFocus:Y,tabListRef:m,tabBarRef:y}),()=>{const i=d.value?[b("span",{class:[a.e("nav-prev"),a.is("disabled",!d.value.prev)],onClick:r},[b(K,null,{default:()=>[b(Ue,null,null)]})]),b("span",{class:[a.e("nav-next"),a.is("disabled",!d.value.next)],onClick:w},[b(K,null,{default:()=>[b(Ke,null,null)]})])]:null,p=e.panes.map((s,E)=>{var T,S,W,O;const x=s.uid,G=s.props.disabled,J=(S=(T=s.props.name)!=null?T:s.index)!=null?S:`${E}`,Q=!G&&(s.isClosable||e.editable);s.index=`${E}`;const Se=Q?b(K,{class:"is-icon-close",onClick:V=>c("tabRemove",s,V)},{default:()=>[b(je,null,null)]}):null,we=((O=(W=s.slots).label)==null?void 0:O.call(W))||s.props.label,$e=!G&&s.active?0:-1;return b("div",{ref:`tab-${x}`,class:[a.e("item"),a.is(f.props.tabPosition),a.is("active",s.active),a.is("disabled",G),a.is("closable",Q),a.is("focus",l.value)],id:`tab-${J}`,key:`tab-${x}`,"aria-controls":`pane-${J}`,role:"tab","aria-selected":s.active,tabindex:$e,onFocus:()=>$(),onBlur:()=>Y(),onClick:V=>{Y(),c("tabClick",s,J,V)},onKeydown:V=>{Q&&(V.code===z.delete||V.code===z.backspace)&&c("tabRemove",s,V)}},[we,Se])});return b("div",{ref:C,class:[a.e("nav-wrap"),a.is("scrollable",!!d.value),a.is(f.props.tabPosition)]},[i,b("div",{class:a.e("nav-scroll"),ref:v},[b("div",{class:[a.e("nav"),a.is(f.props.tabPosition),a.is("stretch",e.stretch&&["top","bottom"].includes(f.props.tabPosition))],ref:m,style:R.value,role:"tablist",onKeydown:B},[e.type?null:b(bt,{ref:y,tabs:[...e.panes]},null),p])])])}}}),ht=q({type:{type:String,values:["card","border-card",""],default:""},closable:Boolean,addable:Boolean,modelValue:{type:[String,Number]},editable:Boolean,tabPosition:{type:String,values:["top","right","bottom","left"],default:"top"},beforeLeave:{type:re(Function),default:()=>!0},stretch:Boolean}),se=e=>qe(e)||He(e),yt={[Be]:e=>se(e),tabClick:(e,o)=>o instanceof Event,tabChange:e=>se(e),edit:(e,o)=>["remove","add"].includes(o),tabRemove:e=>se(e),tabAdd:()=>!0},gt=M({name:"ElTabs",props:ht,emits:yt,setup(e,{emit:o,slots:c,expose:f}){var a;const n=H("tabs"),u=L(()=>["left","right"].includes(e.tabPosition)),{children:v,addChild:m,removeChild:C}=nt(ce(),"ElTabPane"),y=N(),d=N((a=e.modelValue)!=null?a:"0"),t=async(r,w=!1)=>{var P,k;if(!(d.value===r||ve(r)))try{let B;if(e.beforeLeave){const $=e.beforeLeave(r,d.value);B=$ instanceof Promise?await $:$}else B=!0;B!==!1&&(d.value=r,w&&(o(Be,r),o("tabChange",r)),(k=(P=y.value)==null?void 0:P.removeFocus)==null||k.call(P))}catch{}},l=(r,w,P)=>{r.props.disabled||(o("tabClick",r,P),t(w,!0))},_=(r,w)=>{r.props.disabled||ve(r.props.name)||(w.stopPropagation(),o("edit",r.props.name,"remove"),o("tabRemove",r.props.name))},g=()=>{o("edit",void 0,"add"),o("tabAdd")};I(()=>e.modelValue,r=>t(r)),I(d,async()=>{var r;await ue(),(r=y.value)==null||r.scrollToActiveTab()}),Xe(X,{props:e,currentName:d,registerPane:r=>{v.value.push(r)},sortPane:m,unregisterPane:C}),f({currentName:d,tabNavRef:y});const R=({render:r})=>r();return()=>{const r=c["add-icon"],w=e.editable||e.addable?b("div",{class:[n.e("new-tab"),u.value&&n.e("new-tab-vertical")],tabindex:"0",onClick:g,onKeydown:B=>{[z.enter,z.numpadEnter].includes(B.code)&&g()}},[r?j(c,"add-icon"):b(K,{class:n.is("icon-plus")},{default:()=>[b(We,null,null)]})]):null,P=b("div",{class:[n.e("header"),u.value&&n.e("header-vertical"),n.is(e.tabPosition)]},[b(R,{render:()=>{const B=v.value.some($=>$.slots.label);return b(mt,{ref:y,currentName:d.value,editable:e.editable,type:e.type,panes:v.value,stretch:e.stretch,onTabClick:l,onTabRemove:_},{$stable:!B})}},null),w]),k=b("div",{class:n.e("content")},[j(c,"default")]);return b("div",{class:[n.b(),n.m(e.tabPosition),{[n.m("card")]:e.type==="card",[n.m("border-card")]:e.type==="border-card"}]},[k,P])}}});var Tt=gt;const Ct=q({label:{type:String,default:""},name:{type:[String,Number]},closable:Boolean,disabled:Boolean,lazy:Boolean}),Pe="ElTabPane",_t=M({name:Pe}),Bt=M({..._t,props:Ct,setup(e){const o=e,c=ce(),f=Ye(),a=ie(X);a||de(Pe,"usage: <el-tabs><el-tab-pane /></el-tabs/>");const n=H("tab-pane"),u=N(),v=L(()=>o.closable||a.props.closable),m=pe(()=>{var l;return a.currentName.value===((l=o.name)!=null?l:u.value)}),C=N(m.value),y=L(()=>{var l;return(l=o.name)!=null?l:u.value}),d=pe(()=>!o.lazy||C.value||m.value);I(m,l=>{l&&(C.value=!0)});const t=Ge({uid:c.uid,slots:f,props:o,paneName:y,active:m,index:u,isClosable:v});return a.registerPane(t),_e(()=>{a.sortPane(t)}),Je(()=>{a.unregisterPane(t.uid)}),(l,_)=>h(d)?Qe((U(),Ce("div",{key:0,id:`pane-${h(y)}`,class:F(h(n).b()),role:"tabpanel","aria-hidden":!h(m),"aria-labelledby":`tab-${h(y)}`},[j(l.$slots,"default")],10,["id","aria-hidden","aria-labelledby"])),[[Ze,h(m)]]):ne("v-if",!0)}});var Ee=le(Bt,[["__file","tab-pane.vue"]]);const xt=ge(Tt,{TabPane:Ee}),Rt=et(Ee);export{xt as E,Rt as a,kt as b};
