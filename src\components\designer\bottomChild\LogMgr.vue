<template>
	<div class="content" :style="{height: (layoutParam.bottomTabHeight - 46) + 'px'}" ref="logbox">
		<div v-for="(logItem,index) in logList" :key="index" class="log-pane">
			<div class="log-time">{{logItem.xTime}}</div>
			<div class="log-content">{{logItem.xContent}}</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
	import {ref,reactive,inject,onMounted ,onUnmounted, nextTick} from 'vue'
	const ProjectRef = inject("ProjectRef")
	const layoutParam = inject('layoutParam')
	const logList = reactive([])
	const logbox = ref(null)
	
	onMounted(()=>{
		ProjectRef.funcObj.otherFunc.writeLog = writeLog
		ProjectRef.funcObj.otherFunc.clearLog = clearLog
	})
	
	onUnmounted(()=>{
		ProjectRef.funcObj.otherFunc.writeLog = null
		ProjectRef.funcObj.otherFunc.clearLog = null
	})
	
	function writeLog(msg:string){
		if(msg && msg.trim().length > 0){
			logList.push({
			  xTime:(new Date()).Format('yyyy.MM.dd HH:mm:ss'),
			  xContent:msg,
			})
		}
		// nextTick 确保DOM更新后再滚动
		nextTick(()=>{
			if(logbox.value){
				logbox.value.scrollTo({
				  top: logbox.value.scrollHeight, 
				  behavior: 'smooth' // 平滑滚动（可选）
				});
			}
		})
	}
	
	function clearLog(){
		logList.splice(0,logList.length)
	}
	
</script>

<style lang="scss" scoped>
	.content{
		// border:1px solid red;
		background-color: white;
		overflow: auto;
		padding:1px 30px;
		font-size: 15px;
		color:#4f4f4f;
		padding-top:8px;
		margin-top: 1px;
		
		
		.log-pane {
			display: flex;
			justify-content: space-between;
			align-items: flex-start;
			.log-time {
				width:160px;
				white-space: nowrap;
			}
			.log-content {
				flex:1;
				min-width:100px;
				// border:1px solid red;
			}
		}
	}
</style>