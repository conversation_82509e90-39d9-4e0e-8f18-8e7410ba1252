<template>
  <div class="current_table">
    <div class="title">
      当前表：{{ dataTableValue.name }}--[{{ dataTableValue.fullname }}]
    </div>
    <div>
      <el-button :icon="Plus" @click="handleAdd">
        新增
      </el-button>
      <el-button :icon="Refresh" @click="fetchFieldList(true)">
        刷新
      </el-button>
      <el-button
        :icon="DocumentAdd"
        type="primary"
        color="#2B6BFF"
        :disabled="!hasNewItems"
        v-debounce="handleSave"
      >
        保存
      </el-button>
    </div>
  </div>
  <el-form :model="fieldList" ref="formRef">
    <el-table
      :data="fieldList"
      border
      table-layout="fixed"
      :header-cell-style="headerCellStyleData"
      :cell-style="cellStyleData"
      :height="tableHeight"
      ref="tableRef"
    >
      <el-table-column label="列名">
        <template #default="{ row, $index }">
          <el-form-item
            v-if="!row.id || editingIndexes.includes($index)"
            :prop="`${$index}.colname`"
            :rules="rules.colname"
          >
            <el-input v-model="row.colname"   :ref="el => { if (el) nameInputRefs[$index] = el }"/>
          </el-form-item>
          <div v-else>{{ row.colname }}</div>
        </template>
      </el-table-column>
      <el-table-column label="中文名">
        <template #default="{ row, $index }">
          <el-form-item
            v-if="!row.id || editingIndexes.includes($index)"
            :prop="`${$index}.fullname`"
            :rules="rules.fullname"
          >
            <el-input v-model="row.fullname" />
          </el-form-item>
          <div v-else>{{ row.fullname }}</div>
        </template>
      </el-table-column>
      <el-table-column label="数据类型">
        <template #default="{ row, $index }">
          <el-form-item
            v-if="!row.id || editingIndexes.includes($index)"
            :prop="`${$index}.type`"
            :rules="rules.type"
          >
            <el-select v-model="row.type" placeholder="请选择">
              <el-option
                v-for="(item, index) in options"
                :key="index"
                :label="item"
                :value="index"
              ></el-option>
            </el-select>
          </el-form-item>
          <div v-else>{{ options[row.type] }}</div>
        </template>
      </el-table-column>
      <el-table-column label="长度">
        <template #default="{ row, $index }">
          <el-form-item
            v-if="!row.id || editingIndexes.includes($index)"
            :prop="`${$index}.lenVal`"
            :rules="rules.lenVal"
          >
            <el-input v-model="row.lenVal" />
          </el-form-item>
          <div v-else>{{ row.lenVal }}</div>
        </template>
      </el-table-column>
      <el-table-column label="默认值">
        <template #default="{ row, $index }">
          <el-form-item v-if="!row.id || editingIndexes.includes($index)">
            <el-input v-model="row.defaultValue" />
          </el-form-item>
          <div v-else>{{ row.defaultValue }}</div>
        </template>
      </el-table-column>
      <el-table-column label="主键">
        <template #default="{ row, $index }">
          <el-form-item v-if="!row.id || editingIndexes.includes($index)">
            <el-checkbox
              v-model="row.isIdentity"
              :true-value="1"
              :false-value="0"
            />
          </el-form-item>
          <el-checkbox
            v-else
            v-model="row.isIdentity"
            :true-value="1"
            :false-value="0"
            disabled
          />
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注">
        <template #default="{ row, $index }">
          <el-form-item
            v-if="!row.id || editingIndexes.includes($index)"
            :prop="`${$index}.description`"
            :rules="rules.description"
          >
            <el-input v-model="row.description" />
          </el-form-item>
          <div v-else>{{ row.description }}</div>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template #default="{ row, $index }">
          <div
            class="flex_box"
            style="justify-content: flex-start"
            v-if="!row.id || row.orderNo > 3"
          >
            <el-button
              type="primary"
              link
              v-if="row.id && !editingIndexes.includes($index)"
              @click="editingIndexes.push($index)"
            >
              编辑
            </el-button>
            <el-form-item v-if="row.id && editingIndexes.includes($index)">
              <el-button type="danger" link @click="handleCancel($index)">
                取消
              </el-button>
            </el-form-item>
            <el-popconfirm title="确认删除?" @confirm="handleDel($index)">
              <template #reference>
                <el-form-item v-if="!row.id || editingIndexes.includes($index)">
                  <img
                    src="@/assets/images/form_icon_del.png"
                    class="results_btn"
                  />
                </el-form-item>
                <img
                  v-else
                  src="@/assets/images/form_icon_del.png"
                  class="results_btn"
                />
              </template>
            </el-popconfirm>
          </div>
        </template>
      </el-table-column> </el-table
  ></el-form>
</template>

<script setup>
import { toRefs, ref, onMounted, reactive, computed, nextTick} from "vue";
import { Refresh, DocumentAdd, Plus } from "@element-plus/icons-vue";
import { columnList, deleteColumn, saveColumn } from "@/apis/homeApi";
import { ElMessage } from "element-plus";

const tableRef = ref(null);
const nameInputRefs = ref({});

const props = defineProps({
  dataTableValue: {
    type: Object,
    default: {},
  },
  tableId: {
    type: String,
    default: "",
  },
  headerCellStyleData: {
    type: Object,
    default: {},
  },
  cellStyleData: {
    type: Object,
    default: {},
  },
});

const { dataTableValue } = toRefs(props);

const formRef = ref(null);
const rules = reactive({
  colname: [
    { required: true, message: "请输入", trigger: "change" },
    {
      pattern: /^[a-z_,]+$/,
      message: "只能包含小写字母和下划线",
      trigger: "change",
    },
  ],
  fullname: [{ required: true, message: "请输入", trigger: "change" }],
  type: [{ required: true, message: "请选择", trigger: "change" }],
  lenVal: [{ required: true, message: "请输入", trigger: "change" }],
  description: [{ required: true, message: "请输入", trigger: "change" }],
});
const fieldList = ref([]);
const editIndex = ref(-1); // 当前新增或编辑行的下标
const editingIndexes = ref([]); // 正在编辑的行索引数组
const options = computed(() => {
  return dataTableValue.value.typeDb === 0
    ? ["varchar", "char", "int", "bigint", "decimal", "timestamp", "text"]
    : ["string", "double", "long", "date", "datetime(系统生成)", "boolean","point"];
});

// 计算属性：是否有需要保存的项目（新增项目或正在编辑的项目）
const hasNewItems = computed(() => {
  return fieldList.value.some(item => !item.id) || editingIndexes.value.length > 0;
});

// 计算表格的响应式高度（适用于电脑屏幕）
const tableHeight = computed(() => {
  const currentHeight = window.innerHeight;
  const reservedHeight = 380; // 预留空间
  const availableHeight = currentHeight - reservedHeight;

  if (currentHeight <= 900) {
    return Math.max(350, Math.round(availableHeight * 1.2));
  } else if (currentHeight <= 1200) {
    return Math.max(450, Math.round(availableHeight * 1.2));
  } else {
    return Math.max(500, Math.min(800, Math.round(availableHeight * 1.2)));
  }
});

const handleCancel = (index) => {
  if (typeof index === 'number') {
    // 取消单个编辑
    const item = fieldList.value[index];
    if (!item.id) {
      // 如果是新增项，直接删除
      fieldList.value.splice(index, 1);
    } else {
      // 如果是编辑项，从编辑数组中移除
      const editIndex = editingIndexes.value.indexOf(index);
      if (editIndex > -1) {
        editingIndexes.value.splice(editIndex, 1);
      }
    }
  } else {
    // 取消所有编辑
    fetchFieldList();
    editingIndexes.value = [];
  }
  editIndex.value = -1;
};

const handleSave = async () => {
  try {
    await formRef.value.validate();

    // 获取所有需要保存的数据（新增的和正在编辑的）
    const itemsToSave = fieldList.value.filter((item, index) =>
      !item.id || editingIndexes.value.includes(index)
    );

    await saveColumn(props.tableId, {
      createOrUpdateColumnReqVos: JSON.stringify(itemsToSave),
    });
    fetchFieldList();
    ElMessage({
      type: "success",
      message: "保存成功",
    });
    editIndex.value = -1;
    editingIndexes.value = [];
  } catch {}
};

const handleAdd = () => {
  fieldList.value.push({
    colname: "",
    fullname: "",
    type: "",
    lenVal: "",
    defaultValue: "",
    isIdentity: 0,
    description: "",
  });

  nextTick(() => {
    // 滚动到表格底部
    if (tableRef.value) {
      const tableBody = tableRef.value.$el.querySelector('.el-table__body-wrapper');
      if (tableBody) {
        tableBody.scrollTop = tableBody.scrollHeight;
      }
    }

    // 聚焦到新增行的列名输入框
    const newRowIndex = fieldList.value.length - 1;
    const inputRef = nameInputRefs.value[newRowIndex];
    if (inputRef) {
      inputRef.focus();
    }
  });
};

const handleDel = async (index) => {
  if (!fieldList.value[index].id) {
    // 新增未保存的数据，直接删除
    fieldList.value.splice(index, 1);
    editIndex.value = -1;

    // 更新编辑索引数组，因为删除后索引会发生变化
    editingIndexes.value = editingIndexes.value
      .map(editIndex => editIndex > index ? editIndex - 1 : editIndex)
      .filter(editIndex => editIndex !== index);

    ElMessage({
      type: "success",
      message: "删除成功",
    });
  } else {
    try {
      await deleteColumn(fieldList.value[index].id);
      ElMessage({
        type: "success",
        message: "删除成功",
      });
      fetchFieldList();
    } catch {}
  }
};

const fetchFieldList = async (bool) => {
  try {
    const res = await columnList(props.tableId);
    fieldList.value = res.data.items;
    if (bool) {
      ElMessage({
        type: "success",
        message: "刷新成功",
      });
      editIndex.value = -1;
    }
  } catch {}
};

onMounted(() => {
  fetchFieldList();
});
</script>

<style lang="scss">
.current_table {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 0 16px 0;
  .title {
    font-weight: 600;
    font-size: 16px;
    color: #212121;
    line-height: 19px;
  }
}
.results_btn {
  width: 20px;
  height: 20px;
  margin-left: 13px;
  cursor: pointer;
}
</style>
