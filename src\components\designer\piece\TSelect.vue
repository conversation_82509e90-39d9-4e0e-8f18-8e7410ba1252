<template>
	<div class="content" ref="mySelectBox">
		<div class="show-text" :style="{lineHeight:props.TItem.h + 'px',textAlign : props.TBase.getTextAlign()}">
		     {{props.TItem.value.length > 0 ? props.TItem.attribute.choose.valueLabel : getPlaceHolder()}}
		</div>
		<div class="btn-box" @click.stop="openPopup()" @mouseup.stop="" @mousedown.stop="">
			<div class="btn-arrow" :style="{transform: isOpen ? 'rotate(135deg)' : 'rotate(-45deg)',
			    borderLeft:'1px solid #00aaff',
			    borderBottom:'1px solid #00aaff'}"></div>
		</div>
	</div>
	<Teleport to="body">
		<div class="popup-div" :style="getPopupWinStyle()" v-show="isOpen">
			<div v-for="(item,index) in getDdlpList()" :key="index" @click="selectRow(item)" 
			   style="cursor:pointer;padding: 4px 3px;text-align: center;">
				{{item.label}}
			</div>
		</div>
	</Teleport>
</template>

<script lang="ts" setup>
	import { reactive,ref ,onMounted,inject,watch,onDeactivated} from 'vue';
	const ProjectRef = inject("ProjectRef")
	const ChooseComponentList = inject('ChooseComponentList')
	
	const props = defineProps({
		TItem:{
			type:Object,
			default:{}
		},
		TParent:{
			type:Object,
			default:{id:'10001'}
		},
		TBase:{
			type:Object,
			default:{
				checkEnabled:()=>{}
			}
		}
	})
	
	const isOpen = ref(false)
	const mySelectBox = ref(null)
	const dropdownPosition = reactive({
		x:0,
		y:0,
		w:0
	})
	
	function getPlaceHolder(){
		if(props.TItem.hasOwnProperty("attribute") && props.TItem.attribute.hasOwnProperty("placeHolder") 
		    && props.TItem.attribute.placeHolder.hasOwnProperty("placeHolderText")){
				return props.TItem.attribute.placeHolder.placeHolderText
			}
		return ""
	}
	
	
	function getDdlpList(){
		if(props.TItem.hasOwnProperty("attribute") && props.TItem.attribute.hasOwnProperty("choose")
		    && props.TItem.attribute.choose.dataList.length > 0){
				return props.TItem.attribute.choose.dataList
		}
		return []
	}
	
	function getPopupWinStyle(){
		return {
			     top:dropdownPosition.y + 'px',
				 left:dropdownPosition.x + 'px',
				 width:dropdownPosition.w+'px',
			   }
	}
	
	function selectRow(item){
		props.TItem.value = item.value
		props.TItem.attribute.choose.valueLabel = item.label
	}
	
	// 计算下拉框位置
	function calculateLocation(){
		var select_button_dom = mySelectBox.value.getBoundingClientRect()
		dropdownPosition.w = select_button_dom.width
		dropdownPosition.x = select_button_dom.left
		dropdownPosition.y = select_button_dom.top + select_button_dom.height
	}
	
	function openPopup(){
		isOpen.value = !isOpen.value
		if(isOpen.value){
			calculateLocation()
		}
	}
	
	onMounted(()=>{
		props.TItem.parent = props.TParent
		window.addEventListener('click',winClick)
		// 当页面滚动或改变大小时重新计算位置
		window.addEventListener('resize',winResize)
		window.addEventListener('scroll',winScroll)
	})
	
	onDeactivated(()=>{
		window.removeEventListener("click",winClick)
		window.removeEventListener('resize',winResize)
		window.removeEventListener('scroll',winScroll)
	})
	
	const winClick = (event)=>{
		isOpen.value = false
	}
	const winResize = ()=>{
		// 计算面板位置
		calculateLocation();
	}
	const winScroll = ()=>{
		// 计算面板位置
		calculateLocation();
	}
	
</script>

<style lang="scss" scoped>
	.content{
		height:calc(100%);
		width: calc(100%);
		padding:2px;
		position: relative;
		overflow: hidden;
		
		.show-text{
			position: absolute;
			width:calc(100% - 50px);
			top:0px ;
			left:0px;
			bottom:6px;
			padding:0px 6px;
		}
		.btn-box{
			position: absolute;
			width:30px;
			top:0px;
			right:2px;
			bottom:6px;
			background-color: transparent;
			border-radius: 2px;
			display: flex;
			justify-content: center;
			align-items: center;
			cursor: pointer;
			.btn-arrow{
				width:10px;
				height:10px;
				transform: rotate(-45deg);
				// border-left:1px solid white;
				// border-bottom:1px solid white;
				transition:all 0.5s;
				margin-bottom: 3px;
			}
		}
	}
	.popup-div{
		width:100px;
		padding-bottom: 10px;
		padding-top:10px;
		border:1px solid #d6d6d6;
		z-index:1000;
		position:fixed;
		background-color: white;
	}

</style>