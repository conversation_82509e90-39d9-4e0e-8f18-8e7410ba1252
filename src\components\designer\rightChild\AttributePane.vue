<template>
	<div class="content">
		<!-- 组件名称、组件ID -->
		<div class="content-block" v-if="currentCptAttribute.attributeSetup.basic.show">
			<div class="info-item">
				<div class="info-item-title" style="width:60px;">组件ID</div>
				<el-input class="name-input" v-model="currentCptAttribute.attributeSetup.basic.setup.id" disabled  />
			</div>
			<div class="info-item">
				<div class="info-item-title" style="width:60px;">名称</div>
				<el-input class="name-input" v-model="currentCptAttribute.attributeSetup.basic.setup.name" @change="onchangeName()"/>
			</div>
		</div>
		<!-- 禁用/可见 -->
		<div class="content-block" v-if="currentCptAttribute.attributeSetup.basic.show">
			<div class="info-item-title" style="width: 200px">显示/可用</div>
			<div class="info-item">
				<div class="info-item-title-grey">显示：</div>
				<el-switch v-model="currentCptAttribute.attributeSetup.basic.setup.visible" size="small" />
				<el-icon class="expression-icon" @click="expressionMgr.handleOpenExpression(currentCptAttribute.attributeSetup.basic.setup,'visibleExpression','','Boolean')">
					<DocumentChecked color="#409eff" v-if="currentCptAttribute.attributeSetup.basic.setup.visibleExpression" />
					<DocumentDelete v-else />
				</el-icon>
			</div>
			<div class="info-item" v-if="currentCptAttribute.attributeSetup.basic.setup.group != 'layout'">
				<div class="info-item-title-grey">可用：</div>
				<el-switch v-model="currentCptAttribute.attributeSetup.basic.setup.enabled" size="small" />
				<el-icon class="expression-icon" @click="expressionMgr.handleOpenExpression(currentCptAttribute.attributeSetup.basic.setup,'enabledExpression','','Boolean')">
					<DocumentChecked color="#409eff" v-if="currentCptAttribute.attributeSetup.basic.setup.enabledExpression" />
					<DocumentDelete v-else />
				</el-icon>
			</div>
			<div class="info-item">
				<div class="info-item-title-grey">选中：</div>
				<el-switch v-model="currentCptAttribute.attributeSetup.basic.setup.checked" size="small" :disabled="!currentCptAttribute.attributeSetup.basic.setup.enabled"/>
				<el-icon class="expression-icon" @click="expressionMgr.handleOpenExpression(currentCptAttribute.attributeSetup.basic.setup,'checkedExpression','variables','Boolean')">
					<DocumentChecked color="#409eff" v-if="currentCptAttribute.attributeSetup.basic.setup.checkedExpression" />
					<DocumentDelete v-else />
				</el-icon>
			</div>
		</div>
		<!-- 位置 -->
		<div class="content-block" v-if="currentCptAttribute.attributeSetup.position.show">
			<div class="info-item-title">位置</div>
			<div class="info-item">
				<el-input-number v-model="currentCptAttribute.attributeSetup.position.setup.x" :controls="false" :min="0" :precision="0"
					class="common-input" @blur="handleNumberBlur(currentCptAttribute.attributeSetup.position.setup, 'x', 0)">
					<template #prefix>
						<div>X</div>
					</template>
				</el-input-number>
				<el-input-number v-model="currentCptAttribute.attributeSetup.position.setup.y" :controls="false" :min="0" :precision="0"
					class="common-input" @blur="handleNumberBlur(currentCptAttribute.attributeSetup.position.setup, 'y', 0)">
					<template #prefix>
						<div>Y</div>
					</template>
				</el-input-number>
				<el-input-number v-model="currentCptAttribute.attributeSetup.position.setup.attribute.rotation" :controls="false" :min="0" :precision="0"
					class="common-input" @blur="handleNumberBlur(currentCptAttribute.attributeSetup.position.setup.attribute, 'rotation', 0)">
					<template #prefix>
						<img src="@/assets/images/rotation.png" />
					</template>
				</el-input-number>
			</div>
			<div class="info-item">
				<el-input-number v-model="currentCptAttribute.attributeSetup.position.setup.w" :controls="false" :min="0" :precision="0"
					class="common-input input-select" @input="handleDimensionChange('width', $event)" @blur="handleNumberBlur(currentCptAttribute.attributeSetup.position.setup, 'w', 0)">
					<template #prefix>
						<div>W</div>
					</template>
					<template #suffix>
						<el-select v-model="currentCptAttribute.attributeSetup.position.setup.wUnit" class="width-unit">
							<el-option label="px" value="px" />
							<el-option label="%" value="%" />
							<el-option label="vh" value="vh" />
							<el-option label="vw" value="vw" />
							<el-option label="auto" value="auto" />
						</el-select>
					</template>
				</el-input-number>
				<el-input-number v-model="currentCptAttribute.attributeSetup.position.setup.h" :controls="false" :min="0" :precision="0"
					class="common-input input-select" @input="handleDimensionChange('height', $event)" @blur="handleNumberBlur(currentCptAttribute.attributeSetup.position.setup, 'h', 0)">
					<template #prefix>
						<div>H</div>
					</template>
					<template #suffix>
						<el-select v-model="currentCptAttribute.attributeSetup.position.setup.hUnit" class="width-unit">
							<el-option label="px" value="px" />
							<el-option label="%" value="%" />
							<el-option label="vh" value="vh" />
							<el-option label="vw" value="vw" />
							<el-option label="auto" value="auto" />
						</el-select>
					</template>
				</el-input-number>
				<el-tooltip effect="dark" content="固定宽高比例" placement="top">
					<div class="control-icon" @click="currentCptAttribute.layoutDisabled = !currentCptAttribute.layoutDisabled">
						<img v-if="currentCptAttribute.layoutDisabled" src="@/assets/images/lock.png" />
						<img v-else src="@/assets/images/lock_open.png" />
					</div>
				</el-tooltip>
			</div>
			<div v-if="offSetOn()">
				<div class="info-item">
				   <div class="info-item-title" style="margin-right:10px;">开启宽高偏量设置</div>
				   <el-switch v-model="currentCptAttribute.attributeSetup.position.setup.whOffsetOpen" size="small" />
				</div>
				<div class="info-item" v-if="currentCptAttribute.attributeSetup.position.setup.whOffsetOpen && currentCptAttribute.attributeSetup.position.setup.wUnit!='px' && currentCptAttribute.attributeSetup.position.setup.wUnit!='auto'">
					<div class="info-item-title-grey">宽度偏量值 (可正负):</div>
					<el-input-number v-model="currentCptAttribute.attributeSetup.position.setup.offsetW" :controls="false" :precision="0"
						class="common-input" style="padding-left: 10px;" @blur="handleNumberBlur(currentCptAttribute.attributeSetup.position.setup, 'offsetW', 0)">
						<template #suffix><div>px</div></template>
					</el-input-number>
				</div>
				<div class="info-item" v-if="currentCptAttribute.attributeSetup.position.setup.whOffsetOpen && currentCptAttribute.attributeSetup.position.setup.hUnit!='px' && currentCptAttribute.attributeSetup.position.setup.hUnit!='auto'">
					<div class="info-item-title-grey">高度偏量值 (可正负):</div>
					<el-input-number v-model="currentCptAttribute.attributeSetup.position.setup.offsetH" :controls="false" :precision="0"
						class="common-input" style="padding-left: 10px;" @blur="handleNumberBlur(currentCptAttribute.attributeSetup.position.setup, 'offsetH', 0)">
						<template #suffix><div>px</div></template>
					</el-input-number>
				</div>
			</div>
			<div v-if="!checkParentFreeLayout()">
				<div class="info-item">
				   <div class="info-item-title" style="margin-right:10px;">设置 Flex 值</div>
				   <el-switch v-model="currentCptAttribute.attributeSetup.position.setup.flexOpen" size="small" />
				   <el-input-number v-if="currentCptAttribute.attributeSetup.position.setup.flexOpen"
				     v-model="currentCptAttribute.attributeSetup.position.setup.flex" :controls="false" :min="1" :precision="0"
				   	class="common-input" style="padding-left: 10px;width:50px;" @blur="handleNumberBlur(currentCptAttribute.attributeSetup.position.setup, 'flex', 1)">
				   </el-input-number>
				</div>
			</div>
			<div v-if="!checkParentFreeLayout()">
				<div class="info-item">
					<div class="move-btn" @click.stop="onMoveItem('up')">上移</div>
					<div class="move-btn" style="margin-left:20px;" @click.stop="onMoveItem('down')">下移</div>
				</div>
			</div>
			<div v-if="currentCptAttribute.attributeSetup.position.setup.parent && 
			           currentCptAttribute.attributeSetup.position.setup.parent.id=='10001' &&
					   checkParentFreeLayout()">
				<div class="info-item">
			       <div class="info-item-title" style="margin-right:10px;">开启漂浮模式</div>
				   <el-switch v-model="currentCptAttribute.attributeSetup.position.setup.attribute.fixed.fixedOpen" size="small" />
				</div>
				<div class="info-item" v-if="currentCptAttribute.attributeSetup.position.setup.attribute.fixed.fixedOpen">
					<div class="info-item-title-grey">漂浮位置</div>
					<el-select v-model="currentCptAttribute.attributeSetup.position.setup.attribute.fixed.fixedMode" placeholder="请选择"
						class="font-size-select select-cursor" :style="{width: '200px',marginLeft: '10px'}">
						<el-option label="自由方式" value="free" />
						<el-option label="置屏幕顶部" value="top" />
						<el-option label="置屏幕底部" value="bottom" />
					</el-select>
				</div>
			</div>
		</div>
		<!-- 布局 -->
		<div class="content-block" v-if="currentCptAttribute.attributeSetup.layout.show && currentCptAttribute.attributeSetup.basic.setup.key != 'TTab'">
			<div class="info-item-title">布局方式</div>
			<div class="info-item">
				<div class="custom-radio">
					<div v-for="item in layoutDirectionList" :key="item.value" :style="{backgroundColor: currentCptAttribute.attributeSetup.layout.setup.layoutDirection === item.value ? '#FFFFFF' : 'transparent',}" 
					    class="shadow-item" @click="currentCptAttribute.attributeSetup.layout.setup.layoutDirection = item.value">
						{{ item.label }}
					</div>
				</div>
			</div>
			<div class="info-item-title-grey" style="margin-top: 8px" v-if="currentCptAttribute.attributeSetup.layout.setup.layoutDirection != 'absolute'">横向对齐方式</div>
			<div class="info-item" style="margin-bottom: 8px" v-if="currentCptAttribute.attributeSetup.layout.setup.layoutDirection != 'absolute'">
				<el-select v-model="currentCptAttribute.attributeSetup.layout.setup.hAlign" placeholder="请选择"
					class="font-size-select select-cursor" :style="{width: '245px',margin: '0 0 8px 0'}">
					<el-option label="左对齐" value="align-left" />
					<el-option label="右对齐" value="align-right" />
					<el-option label="居中对齐" value="align-center" />
					<el-option label="拉伸" value="align-stretch" />
				</el-select>
			</div>
			<div class="info-item-title-grey" style="margin-top: 8px" v-if="currentCptAttribute.attributeSetup.layout.setup.layoutDirection != 'absolute'">纵向对齐方式</div>
			<div class="info-item" style="margin-bottom: 8px" v-if="currentCptAttribute.attributeSetup.layout.setup.layoutDirection != 'absolute'">
				<el-select v-model="currentCptAttribute.attributeSetup.layout.setup.vAlign" placeholder="请选择"
					class="font-size-select select-cursor" :style="{width: '245px',margin: '0 0 8px 0'}">
					<el-option label="上对齐" value="align-top" />
					<el-option label="下对齐" value="align-bottom" />
					<el-option label="居中对齐" value="align-center" />
					<el-option label="拉伸" value="align-stretch" />
				</el-select>
			</div>
			<div class="info-item">
				<div class="info-item-title-grey" style="margin-right:10px;">横向滚动条:</div>
				<el-switch v-model="currentCptAttribute.attributeSetup.layout.setup.scrollHOpen" size="small" />
			</div>
			<div class="info-item">
				<div class="info-item-title-grey" style="margin-right:10px;">竖向滚动条:</div>
				<el-switch v-model="currentCptAttribute.attributeSetup.layout.setup.scrollVOpen" size="small" />
			</div>
		</div>	
		<!-- 填充 -->
		<div class="content-block" v-if="currentCptAttribute.attributeSetup.background.show">
			<div class="info-item">
				<div class="info-item-title">背景模式</div>
				<el-switch
				    v-model="currentCptAttribute.attributeSetup.background.setup.backgroundImageOpen"
				    inline-prompt active-text="背景图" inactive-text="背景色"
					style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ffaa00;font-size:15px;margin-left:20px;"
				  />
			</div>
			<div class="info-item" v-if="!currentCptAttribute.attributeSetup.background.setup.backgroundImageOpen">
				<div class="info-item-title-grey">使用主题样式：</div>
				<el-switch v-model="currentCptAttribute.attributeSetup.background.setup.theme" size="small" />
			</div>
			<div class="info-item" v-if="currentCptAttribute.attributeSetup.background.setup.theme && !currentCptAttribute.attributeSetup.background.setup.backgroundImageOpen">
				<el-select v-model="currentCptAttribute.attributeSetup.background.setup.themeKey" class="font-family-select"
				    placeholder="请选择" style="width: calc(100% - 46px);min-width:100px;" >
					<el-option v-for="(item,index) in  ProjectRef.funcObj.getThemeList('color')" :key="item.keyName" :label="item.keyName" :value="item.keyName" />
					<!-- <el-option key="vertical" label="theme2" value="vertical" /> -->
				</el-select>
			</div>
			<div class="info-item" v-if="!currentCptAttribute.attributeSetup.background.setup.theme && !currentCptAttribute.attributeSetup.background.setup.backgroundImageOpen">
				<div class="filled-box">
					<el-color-picker v-model="currentCptAttribute.attributeSetup.background.setup.backgroundColor" show-alpha />
					<el-input v-model="currentCptAttribute.attributeSetup.background.setup.backgroundColor" placeholder="请输入背景颜色" />
				</div>
				<el-icon class="expression-icon" @click="expressionMgr.handleOpenExpression(currentCptAttribute.attributeSetup.background.setup,'backgroundExpression','','String')">
					<DocumentChecked color="#409eff" v-if="currentCptAttribute.attributeSetup.background.setup.backgroundExpression" />
					<DocumentDelete v-else />
				</el-icon>
			</div>
			<div class="info-item" v-if="!currentCptAttribute.attributeSetup.background.setup.backgroundImageOpen">
				<div class="info-item-title-grey" style="margin-right:10px;">使用背景渐变色</div>
				<el-switch v-model="currentCptAttribute.attributeSetup.background.setup.gradientOpen" size="small" />
			</div>
			<div class="info-item" v-if="!currentCptAttribute.attributeSetup.background.setup.backgroundImageOpen && currentCptAttribute.attributeSetup.background.setup.gradientOpen">
				<div class="info-item-title-grey" style="margin-right: 10px;">渐变副色:</div>
				<el-color-picker v-model="currentCptAttribute.attributeSetup.background.setup.gradientColor2"/>
			</div>
			<div class="info-item" v-if="!currentCptAttribute.attributeSetup.background.setup.backgroundImageOpen && currentCptAttribute.attributeSetup.background.setup.gradientOpen">
				<div class="info-item-title-grey">渐变方向(角度)</div>
				<el-input-number v-model="currentCptAttribute.attributeSetup.background.setup.gradientAngle" :controls="false" :min="0" :precision="0"
					class="common-input" style="padding-left: 10px;" @blur="handleNumberBlur(currentCptAttribute.attributeSetup.background.setup, 'gradientAngle', 0)">
					<template #suffix><div>deg</div></template>
				</el-input-number>
			</div>
			<div class="info-item-title-grey" v-if="currentCptAttribute.attributeSetup.background.setup.backgroundImageOpen">
				设置背景图片
			</div>
			<div class="info-item" v-if="currentCptAttribute.attributeSetup.background.setup.backgroundImageOpen">
				<div class="filled-box" style="width:calc(100% - 30px);" @dragover.prevent="onDragover" @drop="onBackDrop">
					<el-input v-model="currentCptAttribute.attributeSetup.background.setup.backgroundImageUrl" placeholder="请输入图片地址" style="width:calc(100%);"/>
				</div>
		    </div>
		</div>
		<!-- 窗体内边框 -->
		<div class="content-block" v-if="currentCptAttribute.currentType=='body'">
			<div class="info-item-title">内边距</div>
			<div class="info-item">
				<el-input-number v-model="currentCptAttribute.attributeSetup.box.setup.paddingLeft" :controls="false" :min="0" :precision="0"
					class="common-input" @input="handleChangePadding" @blur="handlePaddingBlur('Left')">
					<template #prefix>
						<img src="@/assets/images/pLeft.png" />
					</template>
				</el-input-number>
				<el-input-number v-model="currentCptAttribute.attributeSetup.box.setup.paddingTop" :controls="false" :min="0" :precision="0"
					class="common-input" @input="handleChangePadding" @blur="handlePaddingBlur('Top')">
					<template #prefix>
						<img src="@/assets/images/pTop.png" />
					</template>
				</el-input-number>
				<el-tooltip effect="dark" content="整体设置" placement="top">
					<div class="control-icon" @click="currentCptAttribute.paddingControl = !currentCptAttribute.paddingControl">
						<img v-if="currentCptAttribute.paddingControl" src="@/assets/images/separate.png" />
						<img v-else src="@/assets/images/overall.png" />
					</div>
				</el-tooltip>
				<el-input-number v-model="currentCptAttribute.attributeSetup.box.setup.paddingRight" :controls="false" :min="0" :precision="0"
					class="common-input" @input="handleChangePadding" @blur="handlePaddingBlur('Right')">
					<template #prefix>
						<img src="@/assets/images/pRight.png" />
					</template>
				</el-input-number>
				<el-input-number v-model="currentCptAttribute.attributeSetup.box.setup.paddingBottom" :controls="false" :min="0" :precision="0"
					class="common-input" @input="handleChangePadding" @blur="handlePaddingBlur('Bottom')">
					<template #prefix>
						<img src="@/assets/images/pBottom.png" />
					</template>
				</el-input-number>
			</div>
		</div>
		<!-- 外观 -->
		<div class="content-block" v-if="currentCptAttribute.attributeSetup.box.show">
			<div class="info-item-title" style="margin-bottom: 10px;">外观</div>
			<div class="info-item-title-grey">外边距</div>
			<div class="info-item">

				<el-input-number v-model="currentCptAttribute.attributeSetup.box.setup.marginTop" :controls="false"  :precision="0"
					class="common-input" @input="handleChangeMargin" @blur="handleMarginBlur('Top')">
					<template #prefix>
						<img src="@/assets/images/mTop.png" />
					</template>
				</el-input-number>
				<el-input-number v-model="currentCptAttribute.attributeSetup.box.setup.marginRight" :controls="false"  :precision="0"
					class="common-input" @input="handleChangeMargin" @blur="handleMarginBlur('Right')">
					<template #prefix>
						<img src="@/assets/images/mRight.png" />
					</template>
				</el-input-number>
				<el-tooltip effect="dark" content="整体设置" placement="top">
					<div class="control-icon" @click="currentCptAttribute.marginControl = !currentCptAttribute.marginControl">
						<img v-if="currentCptAttribute.marginControl" src="@/assets/images/separate.png" />
						<img v-else src="@/assets/images/overall.png" />
					</div>
				</el-tooltip>
				<el-input-number v-model="currentCptAttribute.attributeSetup.box.setup.marginBottom" :controls="false"  :precision="0"
					class="common-input" @input="handleChangeMargin" @blur="handleMarginBlur('Bottom')">
					<template #prefix>
						<img src="@/assets/images/mBottom.png" />
					</template>
				</el-input-number>

				<el-input-number v-model="currentCptAttribute.attributeSetup.box.setup.marginLeft" :controls="false"  :precision="0"
					class="common-input" @input="handleChangeMargin" @blur="handleMarginBlur('Left')">
					<template #prefix>
						<img src="@/assets/images/mLeft.png" />
					</template>
				</el-input-number>

			</div>
			<div class="info-item-title-grey">内边距</div>
			<div class="info-item">
				<el-input-number v-model="currentCptAttribute.attributeSetup.box.setup.paddingLeft" :controls="false" :min="0" :precision="0"
					class="common-input" @input="handleChangePadding" @blur="handlePaddingBlur('Left')">
					<template #prefix>
						<img src="@/assets/images/pLeft.png" />
					</template>
				</el-input-number>
				<el-input-number v-model="currentCptAttribute.attributeSetup.box.setup.paddingTop" :controls="false" :min="0" :precision="0"
					class="common-input" @input="handleChangePadding" @blur="handlePaddingBlur('Top')">
					<template #prefix>
						<img src="@/assets/images/pTop.png" />
					</template>
				</el-input-number>
				<el-tooltip effect="dark" content="整体设置" placement="top">
					<div class="control-icon" @click="currentCptAttribute.paddingControl = !currentCptAttribute.paddingControl">
						<img v-if="currentCptAttribute.paddingControl" src="@/assets/images/separate.png" />
						<img v-else src="@/assets/images/overall.png" />
					</div>
				</el-tooltip>
				<el-input-number v-model="currentCptAttribute.attributeSetup.box.setup.paddingRight" :controls="false" :min="0" :precision="0"
					class="common-input" @input="handleChangePadding" @blur="handlePaddingBlur('Right')">
					<template #prefix>
						<img src="@/assets/images/pRight.png" />
					</template>
				</el-input-number>
				<el-input-number v-model="currentCptAttribute.attributeSetup.box.setup.paddingBottom" :controls="false" :min="0" :precision="0"
					class="common-input" @input="handleChangePadding" @blur="handlePaddingBlur('Bottom')">
					<template #prefix>
						<img src="@/assets/images/pBottom.png" />
					</template>
				</el-input-number>
			</div>
			<div class="info-item-title-grey">四角弧度</div>
			<div class="info-item">
				<el-input-number v-model="currentCptAttribute.attributeSetup.box.setup.radiusLeftTop" :controls="false" :min="0"
					:precision="0" class="common-input" @input="handleRadiusChange" @blur="handleRadiusBlur('LeftTop')">
					<template #prefix>
						<img src="@/assets/images/rLeftTop.png" />
					</template>
				</el-input-number>
				<el-input-number v-model="currentCptAttribute.attributeSetup.box.setup.radiusRightTop" :controls="false" :min="0" :precision="0" class="common-input" @input="handleRadiusChange" @blur="handleRadiusBlur('RightTop')">
					<template #prefix>
						<img src="@/assets/images/rRightTop.png" />
					</template>
				</el-input-number>
				<el-tooltip effect="dark" content="整体设置" placement="top">
					<div class="control-icon" @click="currentCptAttribute.radiusControl = !currentCptAttribute.radiusControl">
						<img v-if="currentCptAttribute.radiusControl" src="@/assets/images/lock.png" />
						<img v-else src="@/assets/images/lock_open.png" />
					</div>
				</el-tooltip>
				<el-input-number v-model="currentCptAttribute.attributeSetup.box.setup.radiusLeftBottom" :controls="false" :min="0" :precision="0" class="common-input" @input="handleRadiusChange" @blur="handleRadiusBlur('LeftBottom')">
					<template #prefix>
						<img src="@/assets/images/rLeftBottom.png" />
					</template>
				</el-input-number>
				<el-input-number v-model="currentCptAttribute.attributeSetup.box.setup.radiusRightBottom" :controls="false" :min="0" :precision="0" class="common-input" @input="handleRadiusChange" @blur="handleRadiusBlur('RightBottom')">
					<template #prefix>
						<img src="@/assets/images/rRightBottom.png" />
					</template>
				</el-input-number>
			</div>
			<div class="info-item-title-grey">透明度</div>
			<div class="info-item">
				<el-input-number v-model="currentCptAttribute.attributeSetup.box.setup.opacity" :controls="false" :min="0" :max="100" :precision="0"
					class="common-input" @blur="handleOpacityBlur">
					<template #prefix>
						<img src="@/assets/images/opacity_icon.png" />
					</template>
					<template #suffix>
						<span>%</span>
					</template>
				</el-input-number>
			</div>
			<div class="info-item-title-grey">缩放</div>
			<div class="info-item">
				<el-input-number v-model="currentCptAttribute.attributeSetup.box.setup.scale" :controls="false" :min="0" :max="1000" :precision="0"
					class="common-input" @blur="handleScaleBlur">
					<template #prefix>
						<img src="@/assets/images/opacity_icon.png" />
					</template>
					<template #suffix>
						<span>%</span>
					</template>
				</el-input-number>
			</div>
			<div class="info-item-title-grey">鼠标图标</div>
			<div class="info-item">
				<el-select v-model="currentCptAttribute.attributeSetup.box.setup.cursorType" placeholder="请选择"
					class="font-size-select select-cursor" :style="{width: '116px',margin: '0 0 8px 0',cursor: currentCptAttribute.attributeSetup.box.setup.cursorType + '!important',}">
					<el-option v-for="item in cursorTypeList" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
			</div>
		</div>
		<!-- 边框 -->
		<div class="content-block" v-if="currentCptAttribute.attributeSetup.box.show">
			<div class="info-item-title">边框</div>
			<div class="info-item">
				<div class="info-item-title-grey">使用主题样式：</div>
				<el-switch v-model="currentCptAttribute.attributeSetup.box.setup.borderTheme" size="small" />
			</div>
			<div class="info-item" v-if="currentCptAttribute.attributeSetup.box.setup.borderTheme">
				<el-select v-model="currentCptAttribute.attributeSetup.box.setup.borderThemeKey" class="font-family-select"
				    placeholder="请选择" style="width: calc(100% - 46px);min-width:100px;" >
					<el-option v-for="(item,index) in  ProjectRef.funcObj.getThemeList('border')" :key="item.keyName" :label="item.keyName" :value="item.keyName" />
				</el-select>
			</div>
			<div class="info-item" v-if="!currentCptAttribute.attributeSetup.box.setup.borderTheme">
				 <div class="info-item-title-grey">(若不需显示边框 即置线粗为 0px)</div>
			</div>
			<div class="info-item" v-if="!currentCptAttribute.attributeSetup.box.setup.borderTheme">
				<el-select v-model="currentCptAttribute.attributeSetup.box.setup.borderStyle" placeholder="请选择" class="font-size-select" style="margin-left: 0">
					<el-option v-for="item in borderStyleList" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-input-number v-model="currentCptAttribute.attributeSetup.box.setup.borderSize" :controls="false" :min="0" :precision="0" class="common-input" style="margin: 0 8px" @blur="handleBorderSizeBlur('box')">
					<template #suffix>
						<div style="height: 28px; line-height: 28px">px</div>
					</template>
				</el-input-number>
				<el-color-picker v-model="currentCptAttribute.attributeSetup.box.setup.borderColor" />
				<el-icon class="expression-icon" @click="expressionMgr.handleOpenExpression(currentCptAttribute.attributeSetup.box.setup,'borderExpression','','String')">
					<DocumentChecked color="#409eff" v-if="currentCptAttribute.attributeSetup.box.setup.borderExpression" />
					<DocumentDelete v-else />
				</el-icon>
			</div>
			<div class="info-item">
				<div class="info-item-title-grey">左边</div>
				<el-switch v-model="currentCptAttribute.attributeSetup.box.setup.borderLeft" size="small" />
				<div class="info-item-title-grey" style="margin-left:5px;">上边</div>
				<el-switch v-model="currentCptAttribute.attributeSetup.box.setup.borderTop" size="small" />
				<div class="info-item-title-grey" style="margin-left:5px;">右边</div>
				<el-switch v-model="currentCptAttribute.attributeSetup.box.setup.borderRight" size="small" />
				<div class="info-item-title-grey" style="margin-left:5px;">下边</div>
				<el-switch v-model="currentCptAttribute.attributeSetup.box.setup.borderBottom" size="small" />
			</div>
		</div>
		
		<!-- 阴影 -->
		<div class="content-block" v-if="currentCptAttribute.attributeSetup.box.show">
			<div class="info-item-title">阴影</div>
			<div class="info-item" style="justify-content: space-between">
				<div style="display: flex; align-items: center">
					<div class="custom-radio">
						<div v-for="item in shadowList" :key="item.value" :style="{backgroundColor: currentCptAttribute.attributeSetup.box.setup.shadowType === item.value ? '#FFFFFF' : 'transparent', }" 
				         class="shadow-item" @click="currentCptAttribute.attributeSetup.box.setup.shadowType = item.value">
							{{ item.label }}
						</div>
					</div>
					<el-color-picker v-if="currentCptAttribute.attributeSetup.box.setup.shadowType != 'none'"
					v-model="currentCptAttribute.attributeSetup.box.setup.shadowColor" show-alpha style="margin-left: 8px" />
				</div>
			</div>
			<div class="info-item" style="width: 240px; justify-content: space-between" v-if="currentCptAttribute.attributeSetup.box.setup.shadowType != 'none'">
				<div class="shadow-input">
					<div class="desc">X</div>
					<el-input-number v-model="currentCptAttribute.attributeSetup.box.setup.shadowOffsetX" :controls="false"  :precision="0" class="common-input" @blur="handleNumberBlur(currentCptAttribute.attributeSetup.box.setup, 'shadowOffsetX', 0)" />
				</div>
				<div class="shadow-input">
					<div class="desc">Y</div>
					<el-input-number v-model="currentCptAttribute.attributeSetup.box.setup.shadowOffsetY" :controls="false"  :precision="0" class="common-input" @blur="handleNumberBlur(currentCptAttribute.attributeSetup.box.setup, 'shadowOffsetY', 0)" />
				</div>
				<div class="shadow-input">
					<div class="desc">模糊</div>
					<el-input-number v-model="currentCptAttribute.attributeSetup.box.setup.shadowBlur" :controls="false" :min="0" :precision="0" class="common-input" @blur="handleNumberBlur(currentCptAttribute.attributeSetup.box.setup, 'shadowBlur', 0)" />
				</div>
				<div class="shadow-input">
					<div class="desc">扩展</div>
					<el-input-number v-model="currentCptAttribute.attributeSetup.box.setup.shadowSpread" :controls="false" :min="0" :precision="0" class="common-input" @blur="handleNumberBlur(currentCptAttribute.attributeSetup.box.setup, 'shadowSpread', 0)" />
				</div>
			</div>
		</div>
		<!-- List的数据设置 -->
		<div class="content-block" v-if="currentCptAttribute.attributeSetup.basic.setup.model=='TList'">
			<div class="info-item" style="margin-bottom:20px;">
				<div class="info-item-title" >列表数据绑定：</div>
				<el-icon class="expression-icon" 
				   @click="expressionMgr.handleOpenExpression(currentCptAttribute.attributeSetup.basic.setup,'value','variables','')">
					<DocumentChecked color="#409eff" v-if="currentCptAttribute.attributeSetup.basic.setup.value" />
					<DocumentDelete v-else />
				</el-icon>
			</div>
			<div class="info-item">
				<div>{{currentCptAttribute.attributeSetup.basic.setup.value}}</div>
			</div>
		</div>
		<!-- 文本 -->
		<div class="content-block"  v-if="currentCptAttribute.attributeSetup.font.show">
			<div class="info-item-title">文本</div>
			<div class="info-item" style="margin-bottom:20px;">
				<el-input v-model="currentCptAttribute.attributeSetup.basic.setup.value" style="width: calc(100% - 36px); height: 28px" />
				<el-icon class="expression-icon" 
				   @click="expressionMgr.handleOpenExpression(currentCptAttribute.attributeSetup.basic.setup,'valueExpression','valueVar','')">
					<DocumentChecked color="#409eff" v-if="currentCptAttribute.attributeSetup.basic.setup.valueExpression" />
					<DocumentDelete v-else />
				</el-icon>
			</div>
			<div class="info-item" style="margin-top: 15px;">
				<div class="info-item-title-grey">使用字体主题样式：</div>
				<el-switch v-model="currentCptAttribute.attributeSetup.font.setup.fontFamilyTheme" size="small" />
			</div>
			<div class="info-item" v-if="currentCptAttribute.attributeSetup.font.setup.fontFamilyTheme">
				<div class="info-item-title-grey">字体样式：</div>
				<el-select v-model="currentCptAttribute.attributeSetup.font.setup.fontFamilyThemeKey" placeholder="请选择" class="font-family-select"
					:style="{ width:'calc(100% - 100px)',minWidth:'150px'}">
					<el-option v-for="(item,index) in  ProjectRef.funcObj.getThemeList('fontFamily')" :key="item.keyName" :label="item.keyName" :value="item.keyName" />
					<!-- <el-option key="theme1" label="theme1" value="theme1" /> -->
				</el-select>
			</div>
			<div class="info-item" v-if="!currentCptAttribute.attributeSetup.font.setup.fontFamilyTheme">
				<div class="info-item-title-grey">设置字体：</div>
				<el-select v-model="currentCptAttribute.attributeSetup.font.setup.fontFamily" placeholder="请选择" class="font-family-select"
					:style="{ width: 'calc(100% - 100px)', fontFamily: currentCptAttribute.attributeSetup.font.setup.fontFamily,  }">
					<el-option v-for="item in systemFonts" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
			</div>
			
			<div class="info-item" style="margin-top: 15px;">
				<div class="info-item-title-grey">使用文字大小主题：</div>
				<el-switch v-model="currentCptAttribute.attributeSetup.font.setup.fontSizeTheme" size="small" />
			</div>
			<div class="info-item" v-if="currentCptAttribute.attributeSetup.font.setup.fontSizeTheme">
				<div class="info-item-title-grey">大小样式：</div>
				<el-select v-model="currentCptAttribute.attributeSetup.font.setup.fontSizeThemeKey" placeholder="请选择" class="font-family-select"
					:style="{ width: 'calc(100% - 100px)',minWidth:'150px'}">
					<el-option v-for="(item,index) in  ProjectRef.funcObj.getThemeList('fontSize')" :key="item.keyName" :label="item.keyName" :value="item.keyName" />
					<!-- <el-option key="theme1" label="theme1" value="theme1" /> -->
				</el-select>
			</div>
			<div class="info-item" v-if="!currentCptAttribute.attributeSetup.font.setup.fontSizeTheme">
				<div class="info-item-title-grey">字体大小：</div>
				<el-select v-model="currentCptAttribute.attributeSetup.font.setup.fontSize" placeholder="请选择" class="font-family-select"
				    :style="{ width: 'calc(100% - 100px)',minWidth:'150px'}" >
					<el-option v-for="item in fontSizeList" :key="item" :label="item" :value="item" />
				</el-select>
				<el-icon class="expression-icon" @click="expressionMgr.handleOpenExpression(currentCptAttribute.attributeSetup.font.setup,'fontSizeExpression','','Number')">
					<DocumentChecked color="#409eff" v-if="currentCptAttribute.attributeSetup.font.setup.fontSizeExpression" />
					<DocumentDelete v-else />
				</el-icon>
			</div>
			
			<div class="info-item" style="margin-top: 15px;">
				<div class="info-item-title-grey" >使用文字颜色主题：</div>
				<el-switch v-model="currentCptAttribute.attributeSetup.font.setup.fontColorTheme" size="small" />
			</div>
			<div class="info-item" v-if="currentCptAttribute.attributeSetup.font.setup.fontColorTheme" >
				<div class="info-item-title-grey">颜色样式：</div>
				<el-select v-model="currentCptAttribute.attributeSetup.font.setup.fontColorThemeKey" placeholder="请选择" class="font-family-select"
					:style="{ width: 'calc(100% - 100px)',minWidth:'150px'}">
					<el-option v-for="(item,index) in  ProjectRef.funcObj.getThemeList('color')" :key="item.keyName" :label="item.keyName" :value="item.keyName" />
					<!-- <el-option key="theme1" label="theme1" value="theme1" /> -->
				</el-select>
			</div>
			<div class="info-item" v-if="!currentCptAttribute.attributeSetup.font.setup.fontColorTheme">
				<div class="info-item-title-grey">字体颜色：</div>
				<el-color-picker v-model="currentCptAttribute.attributeSetup.font.setup.fontColor" sytle="width:100px;"/>
				<el-icon class="expression-icon" @click="expressionMgr.handleOpenExpression(currentCptAttribute.attributeSetup.font.setup,'fontColorExpression','','String')">
					<DocumentChecked color="#409eff" v-if="currentCptAttribute.attributeSetup.font.setup.fontColorExpression" />
					<DocumentDelete v-else />
				</el-icon>
			</div>
			<div class="info-item" style="margin-top: 20px;">
				<div class="info-item-title-grey">字体重量：</div>
				<el-select v-model="currentCptAttribute.attributeSetup.font.setup.fontWeight" placeholder="请选择" class="font-family-select" 
				    :style="{ width: 'calc(100% - 100px)'}">
					<el-option v-for="item in fontWeightList" :key="item" :label="item" :value="item" />
				</el-select>
			</div>

			<div class="info-item" style="margin-top: 10px;">
				<div class="info-item-title-grey">字体装饰：</div>
				<div class="custom-radio" style="width:180px;">
					<div v-for="item in textDecorationList" :key="item.value" :style="{ backgroundColor: currentCptAttribute.attributeSetup.font.setup.textDecoration === item.value ? '#FFFFFF' : 'transparent', }" 
				    @click="currentCptAttribute.attributeSetup.font.setup.textDecoration = item.value">
						<img :src="item.icon" />
					</div>
				</div>
			</div>
			<div class="info-item" style="margin-top: 10px;">
				<div class="info-item-title-grey">对齐方式：</div>
				<div class="custom-radio" style="width:180px;">
					<div v-for="item in textAlignList" :key="item.value" :style="{backgroundColor: currentCptAttribute.attributeSetup.font.setup.textAlign === item.value ? '#FFFFFF' : 'transparent', }" 
			        @click="currentCptAttribute.attributeSetup.font.setup.textAlign = item.value">
						<img :src="item.icon" />
					</div>
				</div>
			</div>
		</div>
		<!-- 图片资源 -->
		<div class="content-block" v-if="currentCptAttribute.attributeSetup.basic.setup.hasOwnProperty('key') && (currentCptAttribute.attributeSetup.basic.setup.key=='TImage' || currentCptAttribute.attributeSetup.basic.setup.key=='TImagePC')" >
			<div class="info-item-title">图片资源</div>
			<div class="info-item">
				<div class="filled-box" style="width:calc(100% - 30px);" @dragover.prevent="onDragover" @drop="onDrop">
					<el-input v-model="currentCptAttribute.attributeSetup.basic.setup.src" placeholder="请输入图片资源地址" style="width:calc(100%);"/>
				</div>
				<el-icon class="expression-icon" @click="expressionMgr.handleOpenExpression(currentCptAttribute.attributeSetup.basic.setup,'srcExpression','','String')">
					<DocumentChecked color="#409eff" v-if="currentCptAttribute.attributeSetup.basic.setup.srcExpression" />
					<DocumentDelete v-else />
				</el-icon>
			</div>
		</div>
		<div class="content-block" v-if="currentCptAttribute.attributeSetup.icon.show">
			<div class="info-item">
				<div class="info-item-title">所选图标:</div>
				<el-input v-model="currentCptAttribute.attributeSetup.icon.setup.iconPath" placeholder="[选择图标]" 
				     style="width:calc(100% - 100px);margin-left:4px;margin-right:4px;"/>
				<div style="font-size: 12px;padding:2px 4px;border:1px solid #efefef;cursor:pointer;border-radius: 5px;"
				    @click.stop="onSetupIcon()">设置</div>
				<el-icon class="expression-icon" @click="expressionMgr.handleOpenExpression(currentCptAttribute.attributeSetup.icon.setup,'iconPathExpression','','String')">
					<DocumentChecked color="#409eff" v-if="currentCptAttribute.attributeSetup.icon.setup.iconPathExpression" />
					<DocumentDelete v-else />
				</el-icon>
			</div>
			<div class="info-item">
				<div class="info-item-title-grey">设置图标大小:</div>
				<el-input-number v-model="currentCptAttribute.attributeSetup.icon.setup.iconSize" :controls="false" :min="0" :precision="0" class="common-input" style="margin: 0 8px" @blur="handleNumberBlur(currentCptAttribute.attributeSetup.icon.setup, 'iconSize', 0)">
					<template #suffix>
						<div style="height: 28px; line-height: 28px">px</div>
					</template>
				</el-input-number>
			</div>
			<div class="info-item">
				<div class="info-item-title-grey">设置图标颜色:</div>
				<el-color-picker v-model="currentCptAttribute.attributeSetup.icon.setup.iconColor" sytle="width:100px;"/>
			</div>
		</div>
		<!-- Line的专有属性 -->
		<div class="content-block" v-if="currentCptAttribute.attributeSetup.line.show">
			<div class="info-item-title">直线方向</div>
			<el-select v-model="currentCptAttribute.attributeSetup.line.setup.direct" class="font-size-select select-cursor"
			    placeholder="请选择" style="margin-top:10px;" @change="onLineChange">
				<el-option key="horizontal" label="横向" value="horizontal" />
				<el-option key="vertical" label="竖向" value="vertical" />
			</el-select>
		</div>
		<div class="content-block" v-if="currentCptAttribute.attributeSetup.line.show">
			<div class="info-item-title">线状</div>
			<div class="info-item">
				<el-select v-model="currentCptAttribute.attributeSetup.line.setup.borderStyle" placeholder="请选择" class="font-size-select" style="margin-left: 0">
					<el-option v-for="item in borderStyleList" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-input-number v-model="currentCptAttribute.attributeSetup.line.setup.borderSize" :controls="false" :min="0" :precision="0" class="common-input" style="margin: 0 8px" @blur="handleBorderSizeBlur('line')">
					<template #suffix>
						<div style="height: 28px; line-height: 28px">px</div>
					</template>
				</el-input-number>
				<el-color-picker v-model="currentCptAttribute.attributeSetup.line.setup.borderColor" />
				<el-icon class="expression-icon" @click="expressionMgr.handleOpenExpression(currentCptAttribute.attributeSetup.line.setup,'borderExpression','','String')">
					<DocumentChecked color="#409eff" v-if="currentCptAttribute.attributeSetup.line.setup.borderExpression" />
					<DocumentDelete v-else />
				</el-icon>
			</div>
		</div>
		
		<div class="content-block" v-if="currentCptAttribute.attributeSetup.choose.show">
			<div class="info-item" v-if="currentCptAttribute.attributeSetup.basic.setup.model=='TChoose'">
			   <div class="info-item-title" style="margin-right: 10px;">方向：</div>
			   <el-radio-group v-model="currentCptAttribute.attributeSetup.choose.setup.direct">
			         <el-radio value="horizontal" size="large">横向排列</el-radio>
			         <el-radio value="vertical" size="large">竖向排列</el-radio>
			       </el-radio-group>
			</div>
			<div class="info-item" style="margin-bottom:4px;">
			   <div class="info-item-title-grey" style="width:calc(100% - 26px);">选项列表</div>
			   <el-icon class="expression-icon" @click="expressionMgr.handleOpenExpression(currentCptAttribute.attributeSetup.choose.setup,'dataExpression','variablesEnum','')">
			   	<DocumentChecked color="#409eff" v-if="currentCptAttribute.attributeSetup.choose.setup.dataExpression" />
			   	<DocumentDelete v-else />
			   </el-icon>
			</div>
			<table class="setup-table">
				<thead>
					<tr>
						<th>显示列</th><th>值列</th><th style="width:30px;"><el-icon :size="15" @click.stop="onAddChooseItem()" style="cursor:pointer;"><Plus /></el-icon></th>
					</tr>
				</thead>
				<tbody>
					<tr v-for="(item,index) in currentCptAttribute.attributeSetup.choose.setup.dataList" :key="index">
						<td>
							<input type="text" v-model="item.label" />
						</td>
						<td>
							<input type="text" v-model="item.value" />
						</td>
						<td><el-icon :size="15" @click.stop="onDelChooseItem(index)" style="cursor:pointer;"><Close /></el-icon></td>
					</tr>
				</tbody>
			</table>
		</div>
		
		<!-- Switch选中颜色设置 -->
		<div class="content-block" v-if="currentCptAttribute.attributeSetup.proper.show">
			<div class="info-item">
				<div class="info-item-title-grey">选中颜色设置</div>
				<el-color-picker v-model="currentCptAttribute.attributeSetup.proper.setup.activeColor"/>
			</div>
			<div class="info-item">
				<div class="info-item-title-grey">未选颜色设置</div>
				<el-color-picker v-model="currentCptAttribute.attributeSetup.proper.setup.disActiveColor"/>
			</div>
		</div>
		
		
		<div class="content-block" v-if="currentCptAttribute.attributeSetup.basic.show && currentCptAttribute.attributeSetup.hover.show && ProjectRef.BasicData.clientType != 0">
			<div class="info-item">
				<div class="info-item-title" style="margin-right:20px;">开启鼠标悬停效果(hover)</div>
			    <el-switch v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.hover.used" size="small" @change="openHover"/>
			</div>
			<!-- 背景颜色 -->
			<div class="info-item" v-if="currentCptAttribute.attributeSetup.basic.setup.attribute.hover.used">
				<div class="info-item-title-grey">设背景色：</div>
				<el-switch v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.hover.backgroundColorOpen" size="small" />
				<div class="filled-box" style="width:150px;margin-left:20px;" v-if="currentCptAttribute.attributeSetup.basic.setup.attribute.hover.backgroundColorOpen">
					<el-color-picker v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.hover.backgroundColor" show-alpha />
					<el-input v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.hover.backgroundColor" placeholder="选择颜色" style="font-size:10px;"/>
				</div>
			</div>

			<!-- 字体 -->
			<div class="info-item" v-if="currentCptAttribute.attributeSetup.basic.setup.attribute.hover.used">
				<div class="info-item-title-grey">字体大小：</div>
				<el-switch v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.hover.fontSizeOpen" size="small" />
				<el-select v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.hover.fontSize" placeholder="请选择" class="font-family-select"
				    :style="{ width: '70px',marginLeft:'20px'}" v-if="currentCptAttribute.attributeSetup.basic.setup.attribute.hover.fontSizeOpen">
					<el-option v-for="item in fontSizeList" :key="item" :label="item" :value="item" />
				</el-select>
			</div>

			<div class="info-item" v-if="currentCptAttribute.attributeSetup.basic.setup.attribute.hover.used">
				<div class="info-item-title-grey">字体颜色：</div>
				<el-switch v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.hover.colorOpen" size="small" />
				<el-color-picker v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.hover.color"  style="margin-left:20px;"
				     v-if="currentCptAttribute.attributeSetup.basic.setup.attribute.hover.colorOpen"/>
			</div>

			<!-- 边框 -->
			<div class="info-item" v-if="currentCptAttribute.attributeSetup.basic.setup.attribute.hover.used">
				<div class="info-item-title-grey">设置边框：</div>
				<el-switch v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.hover.borderOpen" size="small" />
			</div>
			<div class="info-item" v-if="currentCptAttribute.attributeSetup.basic.setup.attribute.hover.used && currentCptAttribute.attributeSetup.basic.setup.attribute.hover.borderOpen">
				<el-select v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.hover.borderStyle" placeholder="请选择" class="font-size-select" style="margin-left: 0">
					<el-option v-for="item in borderStyleList" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-input-number v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.hover.borderSize" :controls="false" :min="0" :precision="0" class="common-input" style="margin: 0 8px" @blur="handleBorderSizeBlur('hover')">
					<template #suffix>
						<div style="height: 28px; line-height: 28px">px</div>
					</template>
				</el-input-number>
				<el-color-picker v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.hover.borderColor" />
			</div>
			<div class="info-item" v-if="currentCptAttribute.attributeSetup.basic.setup.attribute.hover.used">
				<div class="info-item-title-grey">设置阴影：</div>
				<el-switch v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.hover.shadowOpen" size="small" />
				<el-switch style="margin-left:20px;--el-switch-on-color: #13ce66; --el-switch-off-color: #00aaff"
				    v-if="currentCptAttribute.attributeSetup.basic.setup.attribute.hover.shadowOpen"
				    v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.hover.shadowType"
					inline-prompt
				    active-text="外阴影"
				    inactive-text="内阴影"
				  />
				  <el-color-picker v-if="currentCptAttribute.attributeSetup.basic.setup.attribute.hover.shadowOpen"
				  v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.hover.shadowColor" show-alpha style="margin-left: 8px" />
			</div>
			<div class="info-item" style="width: 240px; justify-content: space-between" 
			  v-if="currentCptAttribute.attributeSetup.basic.setup.attribute.hover.used && currentCptAttribute.attributeSetup.basic.setup.attribute.hover.shadowOpen">
				<div class="shadow-input">
					<div class="desc">X</div>
					<el-input-number v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.hover.shadowOffsetX" :controls="false"  :precision="0" class="common-input" @blur="handleNumberBlur(currentCptAttribute.attributeSetup.basic.setup.attribute.hover, 'shadowOffsetX', 0)" />
				</div>
				<div class="shadow-input">
					<div class="desc">Y</div>
					<el-input-number v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.hover.shadowOffsetY" :controls="false"  :precision="0" class="common-input" @blur="handleNumberBlur(currentCptAttribute.attributeSetup.basic.setup.attribute.hover, 'shadowOffsetY', 0)" />
				</div>
				<div class="shadow-input">
					<div class="desc">模糊</div>
					<el-input-number v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.hover.shadowBlur" :controls="false" :min="0" :precision="0" class="common-input" @blur="handleNumberBlur(currentCptAttribute.attributeSetup.basic.setup.attribute.hover, 'shadowBlur', 0)" />
				</div>
				<div class="shadow-input">
					<div class="desc">扩展</div>
					<el-input-number v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.hover.shadowSpread" :controls="false" :min="0" :precision="0" class="common-input" @blur="handleNumberBlur(currentCptAttribute.attributeSetup.basic.setup.attribute.hover, 'shadowSpread', 0)" />
				</div>
			</div>
			<div class="info-item" v-if="currentCptAttribute.attributeSetup.basic.setup.attribute.hover.used">
				<div class="info-item-title-grey">设置圆角：</div>
				<el-switch v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.hover.radiusOpen" size="small" />
				<el-input-number v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.hover.radius" :controls="false" :min="0" :precision="0"
					class="common-input"  v-if="currentCptAttribute.attributeSetup.basic.setup.attribute.hover.radiusOpen" style="width:70px;margin-left:20px;" @blur="handleNumberBlur(currentCptAttribute.attributeSetup.basic.setup.attribute.hover, 'radius', 0)">
					<template #prefix>
						<img src="@/assets/images/overall.png" />
					</template>
				</el-input-number>
			</div>

			<!-- transform -->
			<div class="info-item" v-if="currentCptAttribute.attributeSetup.basic.setup.attribute.hover.used">
				<div class="info-item-title-grey">设置缩放：</div>
				<el-switch v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.hover.scaleOpen" size="small" />
				<el-input-number v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.hover.scale" :controls="false" :min="0" :max="1000" :precision="0"
				    v-if="currentCptAttribute.attributeSetup.basic.setup.attribute.hover.scaleOpen"
					class="common-input" style="width:100px;margin-left:20px;" @blur="handleHoverScaleBlur">
					<template #prefix>
						<img src="@/assets/images/opacity_icon.png" />
					</template>
					<template #suffix>
						<span>%</span>
					</template>
				</el-input-number>
			</div>
			<div class="info-item" v-if="currentCptAttribute.attributeSetup.basic.setup.attribute.hover.used">
				<div class="info-item-title-grey">设置旋转：</div>
				<el-switch v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.hover.rotateOpen" size="small" />
				<el-input-number v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.hover.rotate" :controls="false" :min="0" :precision="0"
				v-if="currentCptAttribute.attributeSetup.basic.setup.attribute.hover.rotateOpen"
					class="common-input" style="width:70px;margin-left:20px;" @blur="handleNumberBlur(currentCptAttribute.attributeSetup.basic.setup.attribute.hover, 'rotate', 0)">
					<template #prefix>
						<img src="@/assets/images/rotation.png" />
					</template>
				</el-input-number>
			</div>
		</div>

		<!-- 开始点击效果  -->
		<div class="content-block" v-if="currentCptAttribute.attributeSetup.basic.show && currentCptAttribute.attributeSetup.active.show">
			<div class="info-item">
				<div class="info-item-title" style="margin-right:20px;">开启点击效果(active)</div>
			    <el-switch v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.active.used" size="small" @change="openActive"/>
			</div>
			<!-- 背景颜色 -->
			<div class="info-item" v-if="currentCptAttribute.attributeSetup.basic.setup.attribute.active.used">
				<div class="info-item-title-grey">设背景色：</div>
				<el-switch v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.active.backgroundColorOpen" size="small" />
				<div class="filled-box" style="width:150px;margin-left:20px;" v-if="currentCptAttribute.attributeSetup.basic.setup.attribute.active.backgroundColorOpen">
					<el-color-picker v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.active.backgroundColor" show-alpha />
					<el-input v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.active.backgroundColor" placeholder="选择颜色" style="font-size:10px;"/>
				</div>
			</div>
			
			<!-- 字体 -->
			<div class="info-item" v-if="currentCptAttribute.attributeSetup.basic.setup.attribute.active.used">
				<div class="info-item-title-grey">字体大小：</div>
				<el-switch v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.active.fontSizeOpen" size="small" />
				<el-select v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.active.fontSize" placeholder="请选择" class="font-family-select"
				    :style="{ width: '70px',marginLeft:'20px'}" v-if="currentCptAttribute.attributeSetup.basic.setup.attribute.active.fontSizeOpen">
					<el-option v-for="item in fontSizeList" :key="item" :label="item" :value="item" />
				</el-select>
			</div>
			
			<div class="info-item" v-if="currentCptAttribute.attributeSetup.basic.setup.attribute.active.used">
				<div class="info-item-title-grey">字体颜色：</div>
				<el-switch v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.active.colorOpen" size="small" />
				<el-color-picker v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.active.color"  style="margin-left:20px;"
				     v-if="currentCptAttribute.attributeSetup.basic.setup.attribute.active.colorOpen"/>
			</div>
			
			<!-- 边框 -->
			<div class="info-item" v-if="currentCptAttribute.attributeSetup.basic.setup.attribute.active.used">
				<div class="info-item-title-grey">设置边框：</div>
				<el-switch v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.active.borderOpen" size="small" />
			</div>
			<div class="info-item" v-if="currentCptAttribute.attributeSetup.basic.setup.attribute.active.used && currentCptAttribute.attributeSetup.basic.setup.attribute.active.borderOpen">
				<el-select v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.active.borderStyle" placeholder="请选择" class="font-size-select" style="margin-left: 0">
					<el-option v-for="item in borderStyleList" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-input-number v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.active.borderSize" :controls="false" :min="0" :precision="0" class="common-input" style="margin: 0 8px" @blur="handleBorderSizeBlur('active')">
					<template #suffix>
						<div style="height: 28px; line-height: 28px">px</div>
					</template>
				</el-input-number>
				<el-color-picker v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.active.borderColor" />
			</div>
			<div class="info-item" v-if="currentCptAttribute.attributeSetup.basic.setup.attribute.active.used">
				<div class="info-item-title-grey">设置阴影：</div>
				<el-switch v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.active.shadowOpen" size="small" />
				<el-switch style="margin-left:20px;--el-switch-on-color: #13ce66; --el-switch-off-color: #00aaff"
				    v-if="currentCptAttribute.attributeSetup.basic.setup.attribute.active.shadowOpen"
				    v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.active.shadowType"
					inline-prompt
				    active-text="外阴影"
				    inactive-text="内阴影"
				  />
				  <el-color-picker v-if="currentCptAttribute.attributeSetup.basic.setup.attribute.active.shadowOpen"
				  v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.active.shadowColor" show-alpha style="margin-left: 8px" />
			</div>
			<div class="info-item" style="width: 240px; justify-content: space-between" 
			  v-if="currentCptAttribute.attributeSetup.basic.setup.attribute.active.used && currentCptAttribute.attributeSetup.basic.setup.attribute.active.shadowOpen">
				<div class="shadow-input">
					<div class="desc">X</div>
					<el-input-number v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.active.shadowOffsetX" :controls="false"  :precision="0" class="common-input" @blur="handleNumberBlur(currentCptAttribute.attributeSetup.basic.setup.attribute.active, 'shadowOffsetX', 0)" />
				</div>
				<div class="shadow-input">
					<div class="desc">Y</div>
					<el-input-number v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.active.shadowOffsetY" :controls="false"  :precision="0" class="common-input" @blur="handleNumberBlur(currentCptAttribute.attributeSetup.basic.setup.attribute.active, 'shadowOffsetY', 0)" />
				</div>
				<div class="shadow-input">
					<div class="desc">模糊</div>
					<el-input-number v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.active.shadowBlur" :controls="false" :min="0" :precision="0" class="common-input" @blur="handleNumberBlur(currentCptAttribute.attributeSetup.basic.setup.attribute.active, 'shadowBlur', 0)" />
				</div>
				<div class="shadow-input">
					<div class="desc">扩展</div>
					<el-input-number v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.active.shadowSpread" :controls="false" :min="0" :precision="0" class="common-input" @blur="handleNumberBlur(currentCptAttribute.attributeSetup.basic.setup.attribute.active, 'shadowSpread', 0)" />
				</div>
			</div>
			<div class="info-item" v-if="currentCptAttribute.attributeSetup.basic.setup.attribute.active.used">
				<div class="info-item-title-grey">设置圆角：</div>
				<el-switch v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.active.radiusOpen" size="small" />
				<el-input-number v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.active.radius" :controls="false" :min="0" :precision="0"
					class="common-input"  v-if="currentCptAttribute.attributeSetup.basic.setup.attribute.active.radiusOpen" style="width:70px;margin-left:20px;" @blur="handleNumberBlur(currentCptAttribute.attributeSetup.basic.setup.attribute.active, 'radius', 0)">
					<template #prefix>
						<img src="@/assets/images/overall.png" />
					</template>
				</el-input-number>
			</div>
			
			<!-- transform -->
			<div class="info-item" v-if="currentCptAttribute.attributeSetup.basic.setup.attribute.active.used">
				<div class="info-item-title-grey">设置缩放：</div>
				<el-switch v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.active.scaleOpen" size="small" />
				<el-input-number v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.active.scale" :controls="false" :min="0" :max="1000" :precision="0"
				    v-if="currentCptAttribute.attributeSetup.basic.setup.attribute.active.scaleOpen"
					class="common-input" style="width:100px;margin-left:20px;" @blur="handleActiveScaleBlur">
					<template #prefix>
						<img src="@/assets/images/opacity_icon.png" />
					</template>
					<template #suffix>
						<span>%</span>
					</template>
				</el-input-number>
			</div>
			<div class="info-item" v-if="currentCptAttribute.attributeSetup.basic.setup.attribute.active.used">
				<div class="info-item-title-grey">设置旋转：</div>
				<el-switch v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.active.rotateOpen" size="small" />
				<el-input-number v-model="currentCptAttribute.attributeSetup.basic.setup.attribute.active.rotate" :controls="false" :min="0" :precision="0"
				v-if="currentCptAttribute.attributeSetup.basic.setup.attribute.active.rotateOpen"
					class="common-input" style="width:70px;margin-left:20px;" @blur="handleNumberBlur(currentCptAttribute.attributeSetup.basic.setup.attribute.active, 'rotate', 0)">
					<template #prefix>
						<img src="@/assets/images/rotation.png" />
					</template>
				</el-input-number>
			</div>
		</div>
		<div class="content-block" v-if="currentCptAttribute.attributeSetup.others.show">
			<div class="info-item-title" style="margin-top: 10px;">专有属性</div>
			<OthersAttribute :others = "currentCptAttribute.attributeSetup.others.setup" :expressionMgr = "expressionMgr"
			v-if="currentCptAttribute.attributeSetup.others.setup" />
		</div>
		<div class="content-block" v-if="currentCptAttribute.attributeSetup.componentParams.show">
			<div class="info-item-title" style="margin-top: 10px;">组件参数</div>
			<div  v-for="(item,index) in paramObjToList(currentCptAttribute.attributeSetup.componentParams.setup)" :key="index" class="component-param-list">
				 <div class="component-param-name flex-item">名称：{{item.name}}</div>
				 <div class="component-param-type flex-item">类型：{{item.type}}</div>
				 <div class="component-param-value flex-item">值：<el-input  class="component-param-input" v-model="currentCptAttribute.attributeSetup.componentParams.setup[item.name].value" /> </div>
				 <div class="component-param-expression flex-item">{{item.expression}}</div>
				<el-icon class="expression-icon" @click="expressionMgr.handleOpenExpression(currentCptAttribute.attributeSetup.componentParams.setup[item.name],'expression','','String')">
					<DocumentChecked color="#409eff" v-if="item.expression" />
					<DocumentDelete v-else />
				 </el-icon>
			</div>
		</div>
		
		<div class="content-block" v-if="currentCptAttribute.attributeSetup.tabs.show">
			<div class="info-item-title" style="margin-top: 10px;">Tab属性</div>
			<div class="info-item">
				<div class="info-item-title-grey">开启动画：</div>
				<el-switch v-model="currentCptAttribute.attributeSetup.tabs.setup.openAnim" size="small" />
			</div>
			<div class="info-item">
				<div class="info-item-title-grey">底部导航条背景颜色：</div>
				<!-- <div class="filled-box" style="width: calc(100% - 400px);"> -->
					<el-color-picker v-model="currentCptAttribute.attributeSetup.tabs.setup.navigateBackground"/>
					<!-- <el-input v-model="currentCptAttribute.attributeSetup.tabs.setup.navigateBackground" placeholder="请左边选择颜色" />
				</div> -->
			</div>
			<div class="info-item" >
				<div class="info-item-title-grey">当前页：</div>
				<el-input-number v-model="currentCptAttribute.attributeSetup.tabs.setup.currentPage" :controls="false" :min="0" :precision="0" 
				class="common-input" style="margin: 0 8px;width:calc(100% - 100px)">
				</el-input-number>
				<el-icon class="expression-icon" @click="expressionMgr.handleOpenExpression(currentCptAttribute.attributeSetup.tabs.setup,'currentExpression','','String')">
					<DocumentChecked color="#409eff" v-if="currentCptAttribute.attributeSetup.tabs.setup.currentExpression" />
					<DocumentDelete v-else />
				 </el-icon>
			</div>
		</div>
		<div  v-for="(item,index) in currentCptAttribute.attributeSetup.tabs.setup.pages.items" :key="index" class="content-block"
			  v-if="currentCptAttribute.attributeSetup.tabs.show">
			  <div style="display: flex;justify-content: space-between;font-size: 14px;">
				  <div>第{{index+1}}个</div>
				  <div @click="deletePageItems(index)">删除</div>
			  </div>
			<div class="info-item" style="margin-bottom:20px;">
				<div class="info-item-title-grey" style="width: 100px; flex-shrink: 0;">文字：</div>
				<el-input v-model="item.title" style="width: 100%; height: 28px" />
				<el-icon class="expression-icon"
				   @click="expressionMgr.handleOpenExpression(item,'titleExpression','valueVar','')">
					<DocumentChecked color="#409eff" v-if="item.titleExpression" />
					<DocumentDelete v-else />
				</el-icon>
			</div>

			<div class="info-item">
				 <div class="info-item-title-grey" style="width: 100px; flex-shrink: 0;">选中图标：</div>

				<div class="filled-box" style="width:100%;" @dragover.prevent="onDragover" @drop="onTabDrop($event,item,'activePath')">
					<el-input v-model="item.activePath" placeholder="请输入图片资源地址" style="width:calc(100%);"/>
				</div>
				<el-icon class="expression-icon" @click="expressionMgr.handleOpenExpression(item,'activePathExpression','variables','String')">
					<DocumentChecked color="#409eff" v-if="item.activePathExpression" />
					<DocumentDelete v-else />
				</el-icon>
			</div>
			<div class="info-item">
				 <div class="info-item-title-grey" style="width: 100px; flex-shrink: 0;">未选中图标：</div>

				<div class="filled-box" style="width:100%;" @dragover.prevent="onDragover" @drop="onTabDrop($event,item,'deactivePath')">
					<el-input v-model="item.deactivePath" placeholder="请输入图片资源地址" style="width:calc(100%);"/>
				</div>
				<el-icon class="expression-icon" @click="expressionMgr.handleOpenExpression(item,'deactivePathExpression','variables','String')">
					<DocumentChecked color="#409eff" v-if="item.deactivePathExpression" />
					<DocumentDelete v-else />
				</el-icon>
			</div>
			<div class="info-item">
				<div class="info-item-title-grey" style="width: 200px; flex-shrink: 0;">选中文字颜色：使用主题样式</div>
				<el-switch v-model="item.activeColorThemeOpen" size="small" />
			</div>
			<div class="info-item" v-if="item.activeColorThemeOpen">
				<el-select v-model="item.activeColorTheme" class="font-family-select"
				    placeholder="请选择" style="width: 100%;min-width:100px;" >
					<el-option v-for="themeItem in  ProjectRef.funcObj.getThemeList('color')" :key="themeItem.keyName" :label="themeItem.keyName" :value="themeItem.keyName" />
				</el-select>
			</div>
			<div class="info-item" v-if="!item.activeColorThemeOpen">
				<div class="filled-box" style="width: 100%;">
					<el-color-picker v-model="item.activeColor" />
					<el-input v-model="item.activeColor" placeholder="请输入选中文字颜色" />
				</div>
				<el-icon class="expression-icon" @click="expressionMgr.handleOpenExpression(item,'activeColorExpression','','String')">
					<DocumentChecked color="#409eff" v-if="item.activeColorExpression" />
					<DocumentDelete v-else />
				</el-icon>
			</div>
			<div class="info-item">
				<div class="info-item-title-grey" style="width: 200px; flex-shrink: 0;">未选中文字颜色：使用主题样式</div>
				<el-switch v-model="item.deactiveColorThemeOpen" size="small" />
			</div>
			<div class="info-item" v-if="item.deactiveColorThemeOpen">
				<el-select v-model="item.deactiveColorTheme" class="font-family-select"
				    placeholder="请选择" style="width: 100%;min-width:100px;" >
					<el-option v-for="themeItem in  ProjectRef.funcObj.getThemeList('color')" :key="themeItem.keyName" :label="themeItem.keyName" :value="themeItem.keyName" />
				</el-select>
			</div>
			<div class="info-item" v-if="!item.deactiveColorThemeOpen">
				<div class="filled-box" style="width: 100%;">
					<el-color-picker v-model="item.deactiveColor"/>
					<el-input v-model="item.deactiveColor" placeholder="请输入未选中文字颜色" />
				</div>
				<el-icon class="expression-icon" @click="expressionMgr.handleOpenExpression(item,'deactiveColorExpression','','String')">
					<DocumentChecked color="#409eff" v-if="item.deactiveColorExpression" />
					<DocumentDelete v-else />
				</el-icon>
			</div>
			<div class="info-item">
				<div class="info-item-title-grey" style="width: 100px; flex-shrink: 0;">选中文字大小：</div>
				<el-select v-model="item.activeFontSize" placeholder="请选择" class="font-family-select"
				    :style="{ width: '100%',minWidth:'150px'}" >
					<el-option v-for="item in fontSizeList" :key="item" :label="item" :value="item" />
				</el-select>
				<el-icon class="expression-icon" @click="expressionMgr.handleOpenExpression(item,'activeFontSizeExpression','','Number')">
					<DocumentChecked color="#409eff" v-if="item.activeFontSizeExpression" />
					<DocumentDelete v-else />
				</el-icon>
			</div>
			<div class="info-item">
				<div class="info-item-title-grey" style="width: 100px; flex-shrink: 0;">未选中文字大小：</div>
				<el-select v-model="item.deactiveFontSize" placeholder="请选择" class="font-family-select"
				    :style="{ width: '100%',minWidth:'150px'}" >
					<el-option v-for="item in fontSizeList" :key="item" :label="item" :value="item" />
				</el-select>
				<el-icon class="expression-icon" @click="expressionMgr.handleOpenExpression(item,'deactiveFontSizeExpression','','Number')">
					<DocumentChecked color="#409eff" v-if="item.deactiveFontSizeExpression" />
					<DocumentDelete v-else />
				</el-icon>
			</div>
			<div class="info-item" style="margin-bottom:20px;">
				<div class="info-item-title-grey" style="width: 100px; flex-shrink: 0;">角标：</div>
				<el-input v-model="item.badgeNum" style="width: 100%; height: 28px" />
				<el-icon class="expression-icon"
				   @click="expressionMgr.handleOpenExpression(item,'badgeNumExpression','valueVar','')">
					<DocumentChecked color="#409eff" v-if="item.badgeNumExpression" />
					<DocumentDelete v-else />
				</el-icon>
			</div>
			
		</div>
		<div @click.stop="addPageItems()"  v-if="currentCptAttribute.attributeSetup.tabs.show">添加tab + </div>
		
		<div class="content-block" v-if="currentCptAttribute.attributeSetup.css.show">
			<div class="info-item-title" style="margin-top: 10px;">直接设置CSS</div>
			<div class="info-item" style="margin-bottom: 6px;">
				<div class="setup-btn" @click.stop="openCssSetup()">
					{{currentCptAttribute.showCssSetup ? '收起' : '展开'}}
				</div>
				<span v-if="currentCptAttribute.showCssSetup">
					<el-tooltip content="刷新" placement="top" effect="light" :show-after="500">
						<el-icon :size="25" color="#c3c3c3" class="refresh-btn"  @click="onRefreshCssSetup()">
							<Refresh />
						</el-icon>
					</el-tooltip>
				</span>
				<span v-if="currentCptAttribute.showCssSetup">
					<el-tooltip content="同步属性" placement="top" effect="light" :show-after="500">
						<el-icon :size="25" color="#c3c3c3" class="refresh-btn"  @click="onSyncSetup()">
							<Finished />
						</el-icon>
					</el-tooltip>
				</span>
			</div>
			<div class="info-item" style="margin-bottom: 30px;" v-if="currentCptAttribute.showCssSetup">
				<textarea  v-model="currentCptAttribute.attributeSetup.css.setup.str" class="css-text"></textarea>
			</div>
		</div>
	</div>
	<ExpressionDialog v-model="expressionMgr.show" 
		:pageData="expressionMgr.pageData" 
		:globalData="expressionMgr.globalData" 
		:pageParams="expressionMgr.pageParams" 
		:textareaDefaultValue="expressionMgr.initTxt" 
		:openType = "expressionMgr.openType"
		:returnType="expressionMgr.returnType"
		@success="expressionMgr.handleExpressionSuccess" />
		
	<SelectIconDialog v-model:dialogVisible="setupIconDialogMgr.show"
	    @change="setupIconDialogMgr.handleSelectIcon" />
</template>

<script setup>
	import {reactive,ref,onMounted,inject,toRaw} from "vue";
	import {DocumentDelete,DocumentChecked} from "@element-plus/icons-vue";
	import {ElMessage} from "element-plus";
	import ExpressionDialog from "./ExpressionDialog.vue";
	import {useAttributeSelect} from '../../../common/useAttributeData.js'
	import {useExplainVar} from '../../../common/useExplainVar.js'
	import { useAttributeCore } from "../../../common/useAttributeCore";
	
	const {parse,reParams} = useExplainVar()
	const {systemFonts,fontSizeList,fontWeightList,borderStyleList,textAlignList,textDecorationList,shadowList
	        ,layoutDirectionList,layoutJustifyList,cursorTypeList} = useAttributeSelect()

	const ProjectRef = inject("ProjectRef");
	const FileScript = inject("FileScript");
	const ChooseComponentList = inject("ChooseComponentList");
	const cmptNameMap = inject("cmptNameMap")
	
	//当前组件属性设置
	const currentCptAttribute = reactive({
		currentId:0,
		currentName:"",
		currentType:"",
		showCssSetup:false,
		layoutDisabled:false,//是否固定宽高比例
		paddingControl:false,//内边距是否整体设置
		marginControl:false,//外边距是否整体设置
		radiusControl:false,//圆角是否整体设置
		expChooseIcon:false,
		attributeSetup:{
			basic:{show:false,setup:{}},
			background:{show:false,setup:{}},
			font:{show:false,setup:{}},
			box:{show:false,setup:{}},
			position:{show:false,setup:{}},
			value:{show:false,setup:{}},
			placeHolder:{show:false,setup:{}},
			layout:{show:false,setup:{}},
			line:{show:false,setup:{}},
			css:{show:false,setup:{}},
			choose:{show:false,setup:{}},
			icon:{show:false,setup:{}},
			proper:{show:false,setup:{}},
			hover:{show:false,setup:{}},
			active:{show:false,setup:{}},
			others:{show:false,setup:{}},
			componentParams:{show:false,setup:{}},
			tabs:{show:false,setup:{}}
		},
		func:{}
	})
	function offSetOn(){
			if (currentCptAttribute.attributeSetup.position.setup.hUnit=='%' ||
			 currentCptAttribute.attributeSetup.position.setup.wUnit=='%' || 
			 currentCptAttribute.attributeSetup.position.setup.hUnit=='vh' || 
			 currentCptAttribute.attributeSetup.position.setup.wUnit=='vh' || 
			 currentCptAttribute.attributeSetup.position.setup.hUnit=='vw' || 
			 currentCptAttribute.attributeSetup.position.setup.wUnit=='vw'){
				 return true;
			 } else {
				 return false;
			 }
	}
	
			 
	const {chooseSingleCmpt,chooseAppBody,initAttributes,onLineChange,openCssSetup,onRefreshCssSetup,
	       openActive,openHover,onDelChooseItem,onAddChooseItem ,handleChangePadding,handleChangeMargin,
			handleRadiusChange,handleDimensionChange,checkParentFreeLayout,addPageItems,deletePageItems} = useAttributeCore(currentCptAttribute)
	
	const expressionMgr = reactive({
		show:false,
		initTxt:"",
		activeKey:"",
		openType:"expression",
		returnType:"String",
		parent:null,
		pageData:[],
		globalData:[],
		pageParams:[]
	})
	expressionMgr.handleOpenExpression = (parent,key,openType,returnType) => {
	    let varType = FileScript.filePath.length > 0 && FileScript.filePath.slice(-4)=='.cpt' ? 'component' : 'PageData'
		console.log("varType="+varType)
		expressionMgr.globalData = parse(ProjectRef.BasicData.globalData.varText,"global")
		expressionMgr.pageData = parse(FileScript.instance.varText,varType)
		expressionMgr.pageParams = reParams(FileScript.params)
		expressionMgr.activeKey = key; // 设置当前激活的键名
        expressionMgr.parent = parent
		if(openType=='valueVar'){
			if(currentCptAttribute.attributeSetup.basic.setup.model=='TChoose' || 
				 currentCptAttribute.attributeSetup.basic.setup.model=='TInput'){
				expressionMgr.openType = 'variables'
			}else{
				expressionMgr.openType = 'expression'
			}
		}else{
			expressionMgr.openType = openType && openType.length > 0 ? openType : "expression"
		}
		expressionMgr.returnType = returnType && returnType.length > 0 ? returnType : "String"
		expressionMgr.initTxt = expressionMgr.parent ? expressionMgr.parent[key] : ""
		expressionMgr.show = true; // 显示对话框
	};

	expressionMgr.handleExpressionSuccess = (expression) => {
		// 处理表达式成功后的逻辑
		if(expressionMgr.parent){
			expressionMgr.parent[expressionMgr.activeKey] = expression
		}
		expressionMgr.show = false; // 关闭对话框
	};

	// 处理输入框失去焦点时的空值重置
	const handleBlurReset = (field, defaultValue) => {
		if (field === null || field === undefined || field === '') {
			return defaultValue;
		}
		return field;
	};

	// 内边距失去焦点处理
	const handlePaddingBlur = (position) => {
		const setup = currentCptAttribute.attributeSetup.box.setup;
		const currentValue = setup[`padding${position}`];
		const resetValue = handleBlurReset(currentValue, 0);

		if (currentCptAttribute.paddingControl && resetValue === 0 && currentValue !== resetValue) {
			// 整体设置模式下，如果当前值被重置为0，则所有边都设为0
			setup.paddingTop = 0;
			setup.paddingBottom = 0;
			setup.paddingRight = 0;
			setup.paddingLeft = 0;
		} else {
			setup[`padding${position}`] = resetValue;
		}
	};

	// 外边距失去焦点处理
	const handleMarginBlur = (position) => {
		const setup = currentCptAttribute.attributeSetup.box.setup;
		const currentValue = setup[`margin${position}`];
		const resetValue = handleBlurReset(currentValue, 0);

		if (currentCptAttribute.marginControl && resetValue === 0 && currentValue !== resetValue) {
			// 整体设置模式下，如果当前值被重置为0，则所有边都设为0
			setup.marginTop = 0;
			setup.marginBottom = 0;
			setup.marginRight = 0;
			setup.marginLeft = 0;
		} else {
			setup[`margin${position}`] = resetValue;
		}
	};

	// 四角弧度失去焦点处理
	const handleRadiusBlur = (position) => {
		const setup = currentCptAttribute.attributeSetup.box.setup;
		const currentValue = setup[`radius${position}`];
		const resetValue = handleBlurReset(currentValue, 0);

		if (currentCptAttribute.radiusControl && resetValue === 0 && currentValue !== resetValue) {
			// 整体设置模式下，如果当前值被重置为0，则所有角都设为0
			setup.radiusLeftTop = 0;
			setup.radiusRightTop = 0;
			setup.radiusLeftBottom = 0;
			setup.radiusRightBottom = 0;
		} else {
			setup[`radius${position}`] = resetValue;
		}
	};

	// 透明度失去焦点处理
	const handleOpacityBlur = () => {
		const setup = currentCptAttribute.attributeSetup.box.setup;
		setup.opacity = handleBlurReset(setup.opacity, 100);
	};

	// 缩放失去焦点处理
	const handleScaleBlur = () => {
		const setup = currentCptAttribute.attributeSetup.box.setup;
		setup.scale = handleBlurReset(setup.scale, 100);
	};

	// 边框宽度失去焦点处理
	const handleBorderSizeBlur = (type = 'box') => {
		let setup;
		if (type === 'line') {
			setup = currentCptAttribute.attributeSetup.line.setup;
		} else if (type === 'hover') {
			setup = currentCptAttribute.attributeSetup.basic.setup.attribute.hover;
		} else if (type === 'active') {
			setup = currentCptAttribute.attributeSetup.basic.setup.attribute.active;
		} else {
			setup = currentCptAttribute.attributeSetup.box.setup;
		}
		setup.borderSize = handleBlurReset(setup.borderSize, 0);
	};

	// hover状态缩放失去焦点处理
	const handleHoverScaleBlur = () => {
		const setup = currentCptAttribute.attributeSetup.basic.setup.attribute.hover;
		setup.scale = handleBlurReset(setup.scale, 100);
	};

	// active状态缩放失去焦点处理
	const handleActiveScaleBlur = () => {
		const setup = currentCptAttribute.attributeSetup.basic.setup.attribute.active;
		setup.scale = handleBlurReset(setup.scale, 100);
	};

	// 通用数值输入框失去焦点处理
	const handleNumberBlur = (obj, key, defaultValue = 0) => {
		obj[key] = handleBlurReset(obj[key], defaultValue);
	};
	
	ChooseComponentList.doOtherFunc.noticeAttr =()=>{
		if(ChooseComponentList.chooseMap.size == 1){
			for(let item of ChooseComponentList.chooseMap.values()){
				console.log("yyyyyy",item)
				if(item.id == currentCptAttribute.currentId) return 
				initAttributes()
				currentCptAttribute.currentId = item.id
				currentCptAttribute.currentName = item.name
				currentCptAttribute.currentType = item.key
		        let attributeArray = ProjectRef.funcObj.getComponentAttributes(item.key).split(",")
				if(attributeArray.length > 0){
					if(!item.attribute){
						item.attribute = {hover:{used:false},active:{used:false}}
					}else{
						if(!item.attribute.hasOwnProperty("hover")) item.attribute.hover = {used:false}
						if(!item.attribute.hasOwnProperty("active")) item.attribute.active = {used:false}
					}
					for(let key of attributeArray){
						chooseSingleCmpt(key,item)
					}
					if(item.key=='TTab'){
						if(!item.attribute.hasOwnProperty("tabs") || !item.attribute.tabs){
						     item.attribute.tabs = currentCptAttribute.func.initTabs()	
						}
						currentCptAttribute.attributeSetup.tabs.setup = item.attribute.tabs
						currentCptAttribute.attributeSetup.tabs.show = true
					}
				}
				
				if(item.hasOwnProperty('componentParams')){
					console.log("9999",item.componentParams)
					currentCptAttribute.attributeSetup.componentParams.setup = item.componentParams
					currentCptAttribute.attributeSetup.componentParams.show = true
				}
				
				let othersArray = ProjectRef.funcObj.getComponentOthers(item.key)
				if(othersArray && othersArray.length > 0){
					if(!item.hasOwnProperty("others") || !item.others){
						item.others = {}
						othersArray.forEach(otherItem=>{
							if(otherItem.name && otherItem.name.length > 0){
								let tItem = {
									name:otherItem.name,
									type:otherItem.type ? otherItem.type : 'String',
									label:otherItem.label ? otherItem.label : '未定义',
									value:otherItem.defaultValue ? otherItem.defaultValue : (otherItem.type && otherItem.type=='Number' ? 0 : '')
								}
								if(otherItem.hasOwnProperty("expression")){
									tItem.expression = ""
								}
								if(tItem.type=='List'){
									if(otherItem.hasOwnProperty("listData")){
										tItem.listData = otherItem.listData
									}else{
										tItem.listData = []
									}
								}
								if(tItem.type=='Array'){
									tItem.format = otherItem.format
									tItem.items = []
									tItem.addBtn = otherItem.addBtn
									tItem.delBtn = otherItem.delBtn
								}
								// item.others.push(tItem)
								item.others[tItem.name] = tItem
							}
						})
						// console.log("others",othersArray)
					}
					currentCptAttribute.attributeSetup.others.setup = item.others
					currentCptAttribute.attributeSetup.others.show = true
				// }else{
				// 	console.log(item.key,othersArray)
				}
			}
		}else if(ChooseComponentList.chooseMap.size > 1 ){
			initAttributes()
		}else{
			chooseAppBody()
		}
	}
	
	function onchangeName(){
		if(currentCptAttribute.currentId == currentCptAttribute.attributeSetup.basic.setup.id &&
		    currentCptAttribute.currentName != currentCptAttribute.attributeSetup.basic.setup.name){
				if(cmptNameMap.checkName(currentCptAttribute.attributeSetup.basic.setup.name)){
					currentCptAttribute.attributeSetup.basic.setup.name = currentCptAttribute.currentName
					ElMessage({
						message:"对不起，此名称已经存在，不能出现重名组件",
						type:"error"
					})
				}else{
					if(cmptNameMap.checkName(currentCptAttribute.currentName)){
						cmptNameMap.names.delete(currentCptAttribute.currentName)
					}
					currentCptAttribute.currentName = currentCptAttribute.attributeSetup.basic.setup.name
				}
		}
	}
	
	
	
	function onSyncSetup(){
		if(currentCptAttribute.attributeSetup.css.show && currentCptAttribute.attributeSetup.css.setup.str.length > 0){
			currentCptAttribute.attributeSetup.basic.setup.cssSyncFunc(currentCptAttribute.attributeSetup.css.setup.str)
		}
	}

	function onMoveItem(direct){
		if(currentCptAttribute.attributeSetup.position.show){
			let xChildren = []
			if(currentCptAttribute.attributeSetup.position.setup.parent.id=='10001'){
				xChildren = FileScript.componentList
			}else{
				xChildren = currentCptAttribute.attributeSetup.position.setup.parent.children
			}
			if(xChildren && xChildren.length > 0){
				let index = currentCptAttribute.attributeSetup.position.setup.TIndex
				if(direct=='up' && index > 0){
					let tmpItem = xChildren.splice(index,1)[0]
					xChildren.splice(index - 1, 0, tmpItem);
				}else if(direct=='down' && index >= 0){
					let tmpItem = xChildren.splice(index,1)[0]
					console.log(xChildren.length,index)
					xChildren.splice(index + 1, 0, tmpItem);
				}
			}
		}
	}
	
	const setupIconDialogMgr = reactive({
		show:false,
	})
	
	setupIconDialogMgr.handleSelectIcon = (iconName)=>{
		setupIconDialogMgr.show = false
        currentCptAttribute.attributeSetup.icon.setup.iconPath = iconName		
	}
	
	function onSetupIcon(){
		setupIconDialogMgr.show = true
	}
	
	function onDragover(e){
		console.log('123',currentCptAttribute.attributeSetup.tabs.setup.pages.items)
		e.preventDefault()
	}
	
	function onDrop(e){
		let res = e.dataTransfer.getData("treeItem")
		if(res && res.length > 0){
			currentCptAttribute.attributeSetup.basic.setup.src = res
		}
	}
	function onTabDrop(e,obj,key){
		let res = e.dataTransfer.getData("treeItem")
		if(res && res.length > 0){
			obj[key] = res
		}
	}
	function onBackDrop(e){
		let res = e.dataTransfer.getData("treeItem")
		if(res && res.length > 0){
			currentCptAttribute.attributeSetup.background.setup.backgroundImageUrl = res
		}
	}
	
	function paramObjToList(paramObj){
		let retList = []
		Object.keys(paramObj).forEach(key=>{
			retList.push(paramObj[key])
		})

		return retList
	}
</script>

<style lang="scss" scoped>
	.content {
		padding-bottom: 10px;

		.content-block {
			border-bottom: 1px solid #e6e6e6;
			padding: 10px 5px;
			white-space: nowrap;
			
			.icon-choose-box{
				display: flex;
				justify-content: flex-start;
				align-items: flex-start;
				flex-wrap: wrap;
				width:calc(100% - 5px);
				border:1px solid #dddddd;
				margin-top:8px;
				
				.icon-choose {
					margin:8px;
					border:1px solid #e8e8e8;
					border-radius: 4px;
					padding:4px;
					cursor:pointer;
				}
				.icon-choose:hover{
					border:1px solid #55aaff;
				}
			}
			

			.info-item-title {
				font-weight: 600;
				font-size: 14px;
				color: #212121;
				line-height: 16px;
				text-align: left;
				// width: 52px;
			}

			.info-item-title-grey {
				font-size: 14px;
				color: #808080;
				line-height: 16px;
				text-align: left;
			}

			.info-item {
				display: flex;
				align-items: center;
				justify-content: flex-start;
				// flex-wrap: wrap;
				margin-top: 6px;
				white-space: nowrap;
				
				.move-btn{
					border:1px solid #efefef;
					padding:4px 30px;
					border-radius: 6px;
					font-size:12px;
					cursor:pointer;
				}
				.move-btn:hover{
					border:1px solid #d9d9d9;
				}
				.move-btn:active{
					border:1px solid #d9d9d9;
					transform: scale(0.97);
				}
				// 名称、组件ID输入框
				.name-input {
					width: calc(100% - 100px);
					height: 28px;
                    min-width: 160px;
					:deep(.el-input__wrapper) {
						background: #f5f5f5;
						border-radius: 6px;
						box-shadow: none;
						color: #000;
					}
				}

				// 数字输入框
				.common-input {
					width: 116px;
					height: 28px;
					margin: 0 8px 8px 0;

					:deep(.el-input__wrapper) {
						background: #f5f5f5;
						border-radius: 6px;
						box-shadow: none;
						color: #000;
						padding: 0 10px;

						.el-input__prefix-inner {
							line-height: 28px;

							div {
								margin: 0;
								width: 15px;
								height: 28px;
								text-align: center;
								color: #808080;
								transform: translateY(-1px);
							}

							img {
								display: block;
								width: 15px;
								height: 15px;
								margin: 0;
							}
						}

						.el-input__inner {
							height: 28px;
							line-height: 28px;
							text-align: left;
							padding-left: 6px;
						}
					}
				}

				.input-select {
					:deep(.el-input__wrapper) {
						padding-right: 0;
					}

					:deep(.el-select__wrapper) {
						width: 50px;
						box-shadow: none;
						background: #f5f5f5;
						height: 28px;
						line-height: 28px;
						min-height: 28px;
						padding: 0 5px;

						.el-select__placeholder {
							span {
								display: block;
								height: 28px;
								line-height: 28px;
								transform: translateY(-2px);
							}
						}
					}
				}

				.control-icon {
					display: flex;
					justify-content: center;
					align-items: center;
					width: 28px;
					height: 28px;
					background: #f5f5f5;
					border-radius: 6px 6px 6px 6px;
					margin-bottom: 8px;
					cursor: pointer;

					img {
						width: 20px;
						height: 20px;
					}
				}

				.line-height-input {
					width: 82px;
					margin-left: 8px;
					margin-bottom: 0;
				}

				.font-family-select {
					:deep(.el-select__wrapper) {
						min-height: 28px;
						height: 28px;
					}
				}

				.font-size-select {
					width: 82px;
					margin-left: 8px;

					:deep(.el-select__wrapper) {
						min-height: 28px;
						height: 28px;
						background: #f5f5f5;
						border-radius: 6px 6px 6px 6px;
						box-shadow: none;
					}
				}

				.select-cursor {
					:deep(.el-select__wrapper) {
						cursor: inherit;
					}
				}

				.custom-radio {
					box-sizing: border-box;
					padding: 2px;
					display: flex;
					justify-content: flex-start;
					align-items: center;
					width: 250px;
					height: 28px;
					background: #f5f5f5;
					border-radius: 6px 6px 6px 6px;

					div {
						width: 45px;
						height: 24px;
						border-radius: 4px 4px 4px 4px;
						display: flex;
						justify-content: center;
						align-items: center;
						cursor: pointer;

						img {
							width: 14px;
							height: 14px;
						}
					}

					.shadow-item {
						width: 93px;
						height: 24px;
						background: #ffffff;
						border-radius: 4px 4px 4px 4px;
						text-align: center;
						font-size: 14px;
						color: #000000;
						line-height: 16px;
					}
				}

				:deep(.el-color-picker__trigger) {
					width: 28px;
					height: 28px;
					border-radius: 6px 6px 6px 6px;
				}

				.filled-box {
					display: flex;
					width: 240px;
					height: 28px;
					background: #f5f5f5;
					border-radius: 6px 6px 6px 6px;

					:deep(.el-color-picker__trigger) {
						border: none;
					}

					:deep(.el-input) {
						.el-input__wrapper {
							background: transparent;
							box-shadow: none;
						}
					}
				}

				.shadow-input {
					.desc {
						font-size: 12px;
						color: #808080;
						line-height: 16px;
						text-align: center;
						margin: 5px 0;
					}

					.common-input {
						width: 55px;
						margin-right: 0;
						text-align: center;

						:deep(.el-input__inner) {
							text-align: center;
							padding-left: 0;
						}
					}
				}

				.expression-icon {
					margin-left: 8px;
					color: #808080;
					cursor: pointer;
				}
				
				.setup-btn{
					border:1px solid #dadada;
					border-radius: 5px;
					width:calc(100% - 20px);
					padding:4px 10px;
					cursor: pointer;
				}
				.setup-btn:hover{
					box-shadow: inset 0px 0px 3px 2px #c2e9f1;
				}
				.refresh-btn{
					transition:all 0.3s;
					cursor:pointer;
					margin-left:10px;
					margin-right:10px;
				}
				.refresh-btn:hover{
					transform: rotate(50deg);
				}
				.refresh-btn:active{
					transform: rotate(-60deg);
				}
				.css-text{
					width:calc(100% - 8px);
					height:300px;
					font-size:14px;
					border:1px solid #dcdcdc;
					padding: 5px 10px;
					line-height: 20px;
				}
				.css-text:focus {
				  outline: none;
				}
			}
			
			.setup-table{
				border:1px solid #e7e7e7;
				width:calc(100% - 1px);
				font-size:12px;
				tr{
					margin:0px;
					padding:0px;
					height:30px;
				}
				
				thead{
					background-color: #dfdfdf;
				}
				
			    tbody {
					max-height:300px;
					overflow-y:auto;
					td{
						border:1px solid #e7e7e7;
					}
				}
				
				input{
					width:calc(100% - 2px);
					height:calc(100% - 2px);
					border:0px;
				}
				input:focus{
					outline:none;
				}
			}
			
		}
	}
	.component-param-list{
		display: flex;
		align-items: center;
	}
	.component-param-name{
		flex: 2;
		text-align: left;
		flex-shrink: 0;
	}
	.component-param-type{
		flex: 1.5;
		text-align: left;
		flex-shrink: 0;
	}
	.component-param-value{
		flex: 2.5;
		text-align: left;
		flex-shrink: 0;
	}
	.component-param-expression{
		margin-right: 5px;
		flex: 1.5;
		// text-align: right;
	}
	.component-param-input{
		width: calc(100% - 40px);
	}
	.flex-item{
		margin-right: 5px;
		text-overflow: ellipsis;
		white-space: nowrap;
		overflow: hidden; 
  		min-width: 0;           /* 关键：覆盖 flex 默认最小宽度 */
	}

</style>