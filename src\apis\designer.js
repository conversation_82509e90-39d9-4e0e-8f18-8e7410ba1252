import http from "@/axios/netRequest";

// export const apiTest = (data) => {
//   return http.post(`/yhyApi/api/designer`, data);
// };

export const apiLoadProject = (data) => {
  return http.post(`/api/file/loadProject/` + data.projectId, {});
};

export const apiLoadProjectFiles = (data) => {
  // return http.post('/yhyApi/api/loadProjectFiles',data)
  return http.post("/api/file/queryByPath", data);
};

export const apiAddFile = (data) => {
  return http.post("/api/file/addFile", data);
};

export const apiCloneFile = (data) => {
  return http.post("/api/file/cloneFile", data);
};

export const apiLockFile = (data) => {
  return http.post("/api/file/lockFileByPath", data);
};

export const apiUpdateFile = (data) => {
  return http.post("/api/file/updateFile", data);
};

export const apiBatchUpdateFile = (data) => {
  return http.post("/api/file/batchUpdateFile", data);
};

export const apiDeleteFile = (data) => {
  return http.post("/api/file/deleteByPath", data);
};

export const apiLoadSetup = (data) => {
  return http.post("/api/file/loadSetup/" + data.projectId, data);
};

export const apiSendPublish = (data)=>{
	return http.post("/app/publish",data)
}

export const apiReadAllGrammar = () => {
  return http.get("/api/grammarLibrary/list");
};

export const apiReadDbConnectList = (data) => {
  return http.post("/api/authDbProject/listByProjectId/" + data.projectId,data);
};

export const apiTestDbConnect = (data) => {
  return http.post("/api/dbstore/testDbConnect/" + data.id, data);
};

export const apiCheckFile = (data) => {
  return http.post("/api/file/checkFile", data);
};

export const apiReadImageResources = (projectId)=>{
	return http.post("/api/project/getProjectResource/"+projectId,{projectId:projectId})
}

export const apiReadCurrentDbSchema = (projectId)=>{
	return http.post("/api/grammarLibrary/queryDatabaseResource/"+projectId)
}

export const readSystemComponents = ()=>{
	return http.post("/api/component/list")
}

export const apiLoadFileJson = (data) => {
  return http.post("/api/file/queryJsonByPath", data);
};

export const fetchThemeList = (projectId) => {
  return http.post(`/api/themeStyle/listByPage/${projectId}`, {
    pageNo: 1,
    pageSize: 100,
  });
};

export const saveTheme = (data) => {
  return http.post(`/api/themeStyle/saveOrUpdate`, data);
};

export const deleteTheme = (id) => {
  return http.delete(`/api/themeStyle/deleteById/${id}`);
};

export const cloneTheme = (data) => {
  return http.post(`/api/themeStyle/copy`, data);
};

export const setDefaultTheme = (id) => {
  return http.post(`/api/themeStyle/setDefault/${id}`);
};

export const imageUpload = (data) => {
  return http.post(`/file/imageUpload`, data, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
};

export const videoUpload = (data) => {
  return http.post(`/file/videoUpload`, data, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
};

// 方法编译执行
export const apiMethodCompileExecute = (projectId, filePath) => {
  return http.post(`/compile/methodCompileExecute/${projectId}/${filePath}`, {});
};
