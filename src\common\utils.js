import { ElMessageBox, ElMessage } from "element-plus";
import { nextTick } from "vue";

export const customMessageBox = (msgText = "您有未保存的更改，是否保存?") => {
  return new Promise(async (resolve, reject) => {
    // 先创建消息框
    const msgBox = ElMessageBox({
      title: "提示",
      message: msgText,
      showCancelButton: true,
      confirmButtonText: "保存",
      cancelButtonText: "不保存",
      distinguishCancelAndClose: true,
      showClose: false,
      closeOnClickModal: false,
      closeOnPressEscape: false,
      // beforeClose: (action, instance, done) => {
      //   if (action === "close") {
      //     ElMessageBox.close();
      //   }
      // },
    });

    // 等待DOM更新后操作按钮
    await nextTick();

    // 获取消息框DOM元素
    const messageBox = document.querySelector(".el-message-box");
    if (messageBox) {
      const footer = messageBox.querySelector(".el-message-box__btns");
      if (footer) {
        // 1. 先移除所有按钮
        while (footer.firstChild) {
          footer.removeChild(footer.firstChild);
        }

        // 2. 创建保存按钮（原确认按钮）
        const saveBtn = document.createElement("button");
        saveBtn.className = "el-button el-button--primary";
        saveBtn.innerHTML = "<span>保存</span>";
        saveBtn.onclick = () => {
          // 保存逻辑
          resolve();
          ElMessageBox.close();
        };
        footer.appendChild(saveBtn);

        // 3. 创建不保存按钮
        const dontSaveBtn = document.createElement("button");
        dontSaveBtn.className = "el-button el-button--default";
        dontSaveBtn.innerHTML = "<span>不保存</span>";
        dontSaveBtn.onclick = () => {
          reject();
          ElMessageBox.close();
        };
        footer.appendChild(dontSaveBtn);

        // 4. 创建取消按钮（原取消按钮）
        const cancelBtn = document.createElement("button");
        cancelBtn.className = "el-button el-button--default";
        cancelBtn.innerHTML = "<span>取消</span>";
        cancelBtn.onclick = () => {
          ElMessageBox.close();
        };
        footer.appendChild(cancelBtn);
      }
    }
  });
};

export const exportTxtFile = (jsonString, fileName = "json.txt") => {
  try {
    // 解析JSON字符串
    const jsonObj = JSON.parse(jsonString);

    // 美化JSON（4个空格缩进）
    const prettyJson = JSON.stringify(jsonObj, null, 4);

    // 创建Blob对象
    const blob = new Blob([prettyJson], { type: "text/plain" });

    // 创建下载链接
    const link = document.createElement("a");
    link.href = URL.createObjectURL(blob);
    link.download = fileName;

    // 触发下载
    document.body.appendChild(link);
    link.click();

    // 清理
    document.body.removeChild(link);
    URL.revokeObjectURL(link.href);
    ElMessage({
      type: "success",
      message: "导出成功",
    });
  } catch (error) {
    console.error("Invalid JSON string:", error);
    alert("导出失败：JSON格式不正确");
  }
};

export const validateArrayItems = (arr) => {
  // 检查数组是否为空
  if (!Array.isArray(arr)) {
    return false;
  }

  // 如果数组为空，直接返回true（根据需求可能需要返回false）
  if (arr.length === 0) {
    return false;
  }

  // 获取第一个项作为基准
  const firstItem = arr[0];

  // 定义需要比较相等的字段
  const compareFields = ["name", "fullname", "remark", "mainColor"];

  // 检查基准项的所有值是否非空
  for (const key in firstItem) {
    if (
      firstItem[key] === null ||
      firstItem[key] === undefined ||
      firstItem[key] === ""
    ) {
      if (key !== "remark" && key !== "desc") {
        // remark,desc字段可以为空
        return false;
      }
    }
  }

  // 检查其他项
  for (let i = 1; i < arr.length; i++) {
    const currentItem = arr[i];

    // 检查当前项的所有值是否非空
    for (const key in currentItem) {
      if (
        currentItem[key] === null ||
        currentItem[key] === undefined ||
        currentItem[key] === ""
      ) {
        if (key !== "remark" && key !== "desc") {
          return false;
        }
      }
    }

    // 检查需要比较的字段是否与基准项一致
    for (const field of compareFields) {
      if (currentItem[field] !== firstItem[field]) {
        return false;
      }
    }
  }

  return true;
};
