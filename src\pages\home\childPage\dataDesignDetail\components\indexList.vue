<template>
  <div class="current_table">
    <div class="title">
      当前表：{{ dataTableValue.name }}--[{{ dataTableValue.fullname }}]
    </div>
    <div>
      <el-button :icon="Plus" @click="handleAdd">
        新增
      </el-button>
      <el-button :icon="Refresh" @click="fetchIndexList(true)">刷新</el-button>
      <el-button
        :icon="DocumentAdd"
        type="primary"
        color="#2B6BFF"
        :disabled="!hasNewItems"
        v-debounce="handleSunmit"
      >
        保存
      </el-button>
    </div>
  </div>
  <el-form :model="list" ref="formRef">
    <el-table
      ref="tableRef"
      :data="list"
      border
      table-layout="fixed"
      :header-cell-style="props.headerCellStyleData"
      :cell-style="props.cellStyleData"
      :height="tableHeight"
    >
      <el-table-column label="索引名称">
        <template #default="{ row, $index }">
          <el-form-item
            v-if="!row.id || editingIndexes.includes($index)"
            :prop="`${$index}.name`"
            :rules="rules.name"
          >
            <el-input
              v-model="row.name"
              :ref="el => { if (el) nameInputRefs[$index] = el }"
            />
          </el-form-item>
          <div v-else>{{ row.name }}</div>
        </template>
      </el-table-column>
      <el-table-column label="索引字段">
        <template #default="{ row, $index }">
          <el-form-item
            v-if="!row.id || editingIndexes.includes($index)"
            :prop="`${$index}.fields`"
            :rules="rules.fields"
          >
            <div class="flex_box" style="width: 100%">
              <span>{{ row.fields }}</span>
              <el-button link type="primary" @click="handleOpenDialog($index)">
                选择
              </el-button>
            </div>
          </el-form-item>
          <div v-else>{{ row.fields }}</div>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template #default="{ $index, row }">
          <div class="flex_box" style="justify-content: flex-start">
            <el-button
              type="primary"
              link
              v-if="row.id && !editingIndexes.includes($index)"
              @click="editingIndexes.push($index)"
            >
              编辑
            </el-button>
            <el-form-item v-if="row.id && editingIndexes.includes($index)">
              <el-button type="danger" link @click="handleCancel($index)">
                取消
              </el-button>
            </el-form-item>
            <el-popconfirm title="确认删除?" @confirm="handleDel($index)">
              <template #reference>
                <el-form-item v-if="!row.id || editingIndexes.includes($index)">
                  <img
                    src="@/assets/images/form_icon_del.png"
                    class="results_btn"
                  />
                </el-form-item>
                <img
                  v-else
                  src="@/assets/images/form_icon_del.png"
                  class="results_btn"
                />
              </template>
            </el-popconfirm>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </el-form>
  <!-- 弹窗选择索引字段 -->
  <el-dialog v-model="dialogVisible" destroy-on-close width="800">
    <template #header>
      <div style="text-align: center; font-weight: 600; font-size: 18px">
        选择字段
      </div>
    </template>
    <el-table
      ref="multipleTableRef"
      :data="fieldList"
      row-key="colname"
      border
      table-layout="fixed"
      :header-cell-style="headerCellStyleData"
      :cell-style="cellStyleData"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column label="列名" prop="colname"></el-table-column>
      <el-table-column label="中文名" prop="fullname"></el-table-column>
    </el-table>
    <template #footer>
      <div style="display: flex; justify-content: flex-end">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" v-debounce="handleSubmit">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { toRefs, ref, onMounted, reactive, nextTick, computed} from "vue";
import { Refresh, DocumentAdd, Plus } from "@element-plus/icons-vue";
import {
  indexList,
  saveIndexList,
  deleteIndex,
  columnList,
} from "@/apis/homeApi";
import { ElMessage } from "element-plus";

const props = defineProps({
  dataTableValue: {
    type: Object,
    default: {},
  },
  tableId: {
    type: String,
    default: "",
  },
  headerCellStyleData: {
    type: Object,
    default: {},
  },
  cellStyleData: {
    type: Object,
    default: {},
  },
});

const { dataTableValue } = toRefs(props);
const formRef = ref(null);
const tableRef = ref(null);
const nameInputRefs = ref({});
const rules = reactive({
  name: [
    { required: true, message: "请输入", trigger: "change" },
    {
      pattern: /^[a-z_,]+$/,
      message: "只能包含小写字母和下划线",
      trigger: "change",
    },
  ],
  fields: [{ required: true, message: "请选择", trigger: "change" }],
});
const list = ref([]);
const multipleTableRef = ref(null);
const dialogVisible = ref(false);
const fieldList = ref([]);

const selectList = ref([]);
const currentIndex = ref(-1);
const editingIndexes = ref([]); // 正在编辑的行索引数组

// 计算属性：是否有需要保存的项目（新增项目或正在编辑的项目）
const hasNewItems = computed(() => {
  return list.value.some(item => !item.id) || editingIndexes.value.length > 0;
});

// 计算表格的响应式高度（适用于电脑屏幕）
const tableHeight = computed(() => {
  const currentHeight = window.innerHeight;
  const reservedHeight = 380; // 预留空间
  const availableHeight = currentHeight - reservedHeight;

  if (currentHeight <= 900) {
    return Math.max(350, Math.round(availableHeight * 1.2));
  } else if (currentHeight <= 1200) {
    return Math.max(450, Math.round(availableHeight * 1.2));
  } else {
    return Math.max(500, Math.min(800, Math.round(availableHeight * 1.2)));
  }
});

const handleCancel = (index) => {
  if (typeof index === 'number') {
    // 取消单个编辑
    const item = list.value[index];
    if (!item.id) {
      // 如果是新增项，直接删除
      list.value.splice(index, 1);
    } else {
      // 如果是编辑项，从编辑数组中移除
      const editIndex = editingIndexes.value.indexOf(index);
      if (editIndex > -1) {
        editingIndexes.value.splice(editIndex, 1);
      }
    }
  } else {
    // 取消所有编辑
    fetchIndexList();
    editingIndexes.value = [];
  }
  currentIndex.value = -1;
};

const handleOpenDialog = (index) => {
  currentIndex.value = index;
  selectList.value = list.value[index].fields.split(",");

  dialogVisible.value = true;
  nextTick(() => {
    selectList.value.forEach((val) => {
      const row = fieldList.value.find((item) => item.colname === val);
      if (row) {
        multipleTableRef.value.toggleRowSelection(row, true);
      }
    });
  });
};

const handleSelectionChange = (val) => {
  selectList.value = val.map((item) => {
    return item.colname;
  });
};

const handleSubmit = () => {
  if (selectList.value.length === 0) {
    ElMessage({
      type: "error",
      message: "请选择字段",
    });
  } else {
    list.value.splice(currentIndex.value, 1, {
      ...list.value[currentIndex.value],
      fields: selectList.value.join(","),
    });
    dialogVisible.value = false;
  }
};

const handleAdd = () => {
  list.value.push({
    id: "",
    name: "",
    fields: "",
  });


  
  nextTick(() => {
    // 滚动到表格底部
    if (tableRef.value) {
      const tableBody = tableRef.value.$el.querySelector('.el-table__body-wrapper');
      if (tableBody) {
        tableBody.scrollTop = tableBody.scrollHeight;
      }
    }

    // 聚焦到新增行的索引名称输入框
    const newRowIndex = list.value.length - 1;
    const inputRef = nameInputRefs.value[newRowIndex];
    if (inputRef) {
      inputRef.focus();
    }
  });
};

const handleDel = async (index) => {
  if (!list.value[index].id) {
    // 新增未保存的数据，直接删除
    list.value.splice(index, 1);
    currentIndex.value = -1;

    // 更新编辑索引数组，因为删除后索引会发生变化
    editingIndexes.value = editingIndexes.value
      .map(editIndex => editIndex > index ? editIndex - 1 : editIndex)
      .filter(editIndex => editIndex !== index);

    ElMessage({
      type: "success",
      message: "删除成功",
    });
  } else {
    try {
      await deleteIndex(list.value[index].id);
      ElMessage({
        type: "success",
        message: "删除成功",
      });
      fetchIndexList();
    } catch {}
  }
};

const fetchIndexList = async (bool) => {
  try {
    const res = await indexList(props.tableId);
    list.value = res.data || [];
    if (bool) {
      ElMessage({
        type: "success",
        message: "刷新成功",
      });
      currentIndex.value = -1;
    }
  } catch {}
};

const handleSunmit = async () => {
  try {
    await formRef.value.validate();

    // 获取所有需要保存的数据（新增的和正在编辑的）
    const itemsToSave = list.value.filter((item, index) =>
      !item.id || editingIndexes.value.includes(index)
    );

    await saveIndexList(props.tableId, {
      createOrUpdateIndexReqVos: JSON.stringify(itemsToSave),
    });
    fetchIndexList();
    ElMessage({
      type: "success",
      message: "保存成功",
    });
    currentIndex.value = -1;
    editingIndexes.value = [];
  } catch {}
};

const fetchFieldList = async (bool) => {
  try {
    const res = await columnList(props.tableId);
    fieldList.value = res.data.items;
  } catch {}
};

onMounted(() => {
  fetchIndexList();
  fetchFieldList();
});
</script>

<style lang="scss">
.current_table {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 0 16px 0;
  .title {
    font-weight: 600;
    font-size: 16px;
    color: #212121;
    line-height: 19px;
  }
}
.results_btn {
  width: 20px;
  height: 20px;
  margin-left: 13px;
  cursor: pointer;
}
</style>
