<template>
	<div :class="props.TItem.model !='TChoose' ?  'content basicStyle' : 'content singleStyle'" @mousedown.stop="onMouseDown" @contextmenu.prevent.stop="handleContextMenu"
		:style="tComptBase.getBoxStyle()" v-show="props.TItem.hasOwnProperty('visible') && ( props.TItem.visible==true || props.TItem.visible=='true')"
		@click.stop="onBoxClicked()">
		<div :style="tComptBase.getInnerBoxStyle()" >
		<div class="basic-choose-box left-top-box"     @mousedown.stop="onMouseDown2(11,$event)"
			v-if="ChooseComponentList.chooseMap.has(props.TItem.id) && props.TItem.model !='TLine'" />
		<div class="basic-choose-box top-box"          @mousedown.stop="onMouseDown2(12,$event)"
			v-if="ChooseComponentList.chooseMap.has(props.TItem.id)  && tComptBase.getLineDirect('vertical')" />
		<div class="basic-choose-box right-top-box"    @mousedown.stop="onMouseDown2(13,$event)"
			v-if="ChooseComponentList.chooseMap.has(props.TItem.id)  && props.TItem.model !='TLine'" />
		<div class="basic-choose-box right-box"        @mousedown.stop="onMouseDown2(14,$event)"
			v-if="ChooseComponentList.chooseMap.has(props.TItem.id) && tComptBase.getLineDirect('horizontal')"/>
		<div class="basic-choose-box right-bottom-box" @mousedown.stop="onMouseDown2(15,$event)"
			v-if="ChooseComponentList.chooseMap.has(props.TItem.id)  && props.TItem.model !='TLine'" />
		<div class="basic-choose-box bottom-box"       @mousedown.stop="onMouseDown2(16,$event)"
			v-if="ChooseComponentList.chooseMap.has(props.TItem.id)  && tComptBase.getLineDirect('vertical')" />
		<div class="basic-choose-box left-bottom-box"  @mousedown.stop="onMouseDown2(17,$event)"
			v-if="ChooseComponentList.chooseMap.has(props.TItem.id)  && props.TItem.model !='TLine'" />
		<div class="basic-choose-box left-box"         @mousedown.stop="onMouseDown2(18,$event)"
			v-if="ChooseComponentList.chooseMap.has(props.TItem.id) && tComptBase.getLineDirect('horizontal')" />
		<div class="base-content">
			<TBasic           v-if="props.TItem.model=='TBasic'"      :TItem="props.TItem" :TParent="props.TParent" :TBase="tComptBase" />
			<TIcon       v-else-if="props.TItem.model=='TIcon'"       :TItem="props.TItem" :TParent="props.TParent" :TBase="tComptBase" />
			<TImage      v-else-if="props.TItem.model=='TImage'"      :TItem="props.TItem" :TParent="props.TParent" :TBase="tComptBase" />
			<TChoose     v-else-if="props.TItem.model=='TChoose'"     :TItem="props.TItem" :TParent="props.TParent" :TBase="tComptBase" />
			<TInput      v-else-if="props.TItem.model=='TInput'"      :TItem="props.TItem" :TParent="props.TParent" :TBase="tComptBase" />
			<TBox        v-else-if="props.TItem.model=='TBox'"        :TItem="props.TItem" :TParent="props.TParent" :TBase="tComptBase" />
			<TList       v-else-if="props.TItem.model=='TList'"       :TItem="props.TItem" :TParent="props.TParent" :TBase="tComptBase" />
			<TLine       v-else-if="props.TItem.model=='TLine'"       :TItem="props.TItem" :TParent="props.TParent" :TBase="tComptBase" />
			<TSwitch     v-else-if="props.TItem.model=='TSwitch'"     :TItem="props.TItem" :TParent="props.TParent" :TBase="tComptBase" />
			<TSelect     v-else-if="props.TItem.model=='TSelect'"     :TItem="props.TItem" :TParent="props.TParent" :TBase="tComptBase" />
			<TCustomCmpt v-else-if="props.TItem.model=='TCustomCmpt'" :TItem="props.TItem" :TParent="props.TParent" :TBase="tComptBase" />
			<TTab        v-else-if="props.TItem.model=='TTab'"        :TItem="props.TItem" :TParent="props.TParent" :TBase="tComptBase" />
			<TSwiper     v-else-if="props.TItem.model=='TSwiper'"     :TItem="props.TItem" :TParent="props.TParent" :TBase="tComptBase" />
		</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
	import { reactive, ref, onMounted ,inject,watch,computed} from 'vue'
	import { useTComponentBase } from "../../../common/tcomponentBase.js"

	const props = defineProps({
		TItem: {
			type: Object,
			default: {}
		},
		TParent:{
			type:Object,
			default:{id:'10001'}
		},
		TIndex:{
			type:Number,
			default:-1
		}
	})
	
	props.TItem.TIndex = props.TIndex

	const appScreen = inject("appScreen")
	const FileScript = inject("FileScript")
	const ChooseComponentList = inject("ChooseComponentList")
	const pressControlKey = inject('pressControlKey')
    const chooseBox = inject("chooseBox")
	const tComptBase  = useTComponentBase(props.TItem)
	const rightMenu = inject("rightMenu")
	
	watch(()=>props.TItem,(value)=>{
		props.TItem.TIndex = props.TIndex
		tComptBase.setXItem(props.TItem)
	})
	
	function onBoxClicked(){
		if(tComptBase.checkEnabled()){
			props.TItem.checked = !props.TItem.checked
		}
		
	}
	
	function getParentPos(item){
		if(item.hasOwnProperty("parent") && item.parent.id != '10001'){
			let v = getParentPos(item.parent)
			return {x:item.x + v.x,y:item.y + v.y}
		}else{
			return {x:item.x,y:item.y}
		}
	}
	
	function onMouseDown(e:MouseEvent){
		if(pressControlKey.value==1 && props.TItem.hasOwnProperty("key") && props.TItem.key == 'TBox'){
			let xm = getParentPos(props.TItem)
			chooseBox.offsetX = xm.x
			chooseBox.offsetY = xm.y
			chooseBox.parent = props.TItem
			FileScript.onMouseDown(20,e)
			return 
		}
		if(props.TItem.parent && props.TItem.parent.hasOwnProperty("key") && props.TItem.parent.key == 'TCustomCmpt'){
			if(props.TItem.parent.onMouseDown){
				props.TItem.parent.onMouseDown(e)
			}
			return
		}
		ChooseComponentList.setKey(props.TItem)
		FileScript.onMouseDown(1,e)
		readyCopy(e)
	}
	
	function onMouseDown2(category:number,e:MouseEvent){
		FileScript.target = props.TItem
		FileScript.onMouseDown(category,e)
	}

	onMounted(() => {
		  if(props.TItem.hasOwnProperty("key") && props.TItem.key == 'TCustomCmpt'){
			  props.TItem.onMouseDown = onMouseDown
		  }
	})
	
	const handleContextMenu = (e) => {
	  e.preventDefault()
	  readyCopy(e)
	  rightMenu.show=true
	}
	
	function readyCopy(e){
		rightMenu.x = e.clientX
		rightMenu.y = e.clientY
		rightMenu.offsetX = e.offsetX 
		rightMenu.offsetY = e.offsetY
		rightMenu.first = true
		rightMenu.third = true
		if(props.TItem.hasOwnProperty("key") && props.TItem.key == 'TBox'){
				  rightMenu.second = true
		}else{
				  rightMenu.second = false
		}
		if(props.TItem.hasOwnProperty("key") && props.TItem.key == 'TCustomCmpt'){
				  rightMenu.fourth = true
		}else{
				  rightMenu.fourth = false
		}
		rightMenu.parent = props.TItem
	}
</script>

<style lang="scss" scoped>
	@import "../../../common/effect.css";
	
	.content{
		position: absolute;
		.basic-choose-box{
			position: absolute;
			width:8px;
			height:8px;
			background-color: #00aaff;
			z-index: 997;
		}
		.left-top-box {
			top:-4px;
			left:-4px;
			cursor:se-resize;
		}
		.top-box {
			top:-4px;
			left:calc(50% - 4px);
			cursor:s-resize;
		}
		.right-top-box{
			right:-4px;
			top:-4px;
			cursor:sw-resize;
		}
		.right-box{
			right:-4px;
			top:calc(50% - 4px);
			cursor:w-resize;
		}
		.right-bottom-box{
			right:-4px;
			bottom:-4px;
			cursor:nw-resize;
		}
		.bottom-box{
			bottom:-4px;
			left:calc(50% - 4px);
			cursor:n-resize;
		}
		.left-bottom-box{
			left:-4px;
			bottom:-4px;
			cursor:ne-resize;
		}
		.left-box{
			top:calc(50% - 4px);
			left:-4px;
			cursor:e-resize;
		}
		.base-content{
			position: absolute;
			top:1px;
			left:1px;
			width:100%;
			height:100%;
			display: flex;
			justify-content: space-between;
			align-items: stretch;
			overflow: hidden;
		}
		
	}
	
	// .basicStyle{
	// 	background-color: var(--basic-backgroundColor);
	// 	color:var(--basic-color);
	// 	font-size:var(--basic-fontSize);
	// 	box-shadow: var(--basic-boxShadow);
	// 	border:var(--basic-border);
	// 	border-radius: var(--basic-borderRadius);
	// 	transform: var(--basic-transform);
	// }
	// .basicStyle:hover{
	// 	background-color: var(--hover-backgroundColor);
	// 	color:var(--hover-color);
	// 	font-size:var(--hover-fontSize);
	// 	box-shadow: var(--hover-boxShadow);
	// 	border:var(--hover-border);
	// 	border-radius: var(--hover-borderRadius);
	// 	transform: var(--hover-transform);
	// }
	// .basicStyle:active{
	// 	background-color: var(--active-backgroundColor);
	// 	color:var(--active-color);
	// 	font-size:var(--active-fontSize);
	// 	box-shadow: var(--active-boxShadow);
	// 	border:var(--active-border);
	// 	border-radius: var(--active-borderRadius);
	// 	transform: var(--active-transform);
	// }
</style>