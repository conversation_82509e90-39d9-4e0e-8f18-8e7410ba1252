<template>
	<div class="tree-box">
		<div style="display: flex;justify-content: space-between;align-items: center;">
			<el-input v-model="keyword" placeholder="输入查询关键字 或 lock" clearable style="flex:1;margin:2px 5px;"/>
			<div class="refresh-btn" @click.stop="initTree()">
				<el-tooltip class="box-item" effect="light" content="刷新树数据" placement="top-start" :show-after="500">
				<el-icon :size="26" color="#a7a7a7"><Refresh /> </el-icon>
				</el-tooltip>
			</div>
		</div>
		
		<el-tree
		    ref="treeRef"
		    class="filter-tree"
		    :data="treeMgr.treeData"
		    :filter-node-method="filterNode"
			default-expand-all
			empty-text="没有查到数据"
			:style="{height:'calc(100vh - 230px - '+layoutParam.bottomTabHeight+'px)'}"
		  >
		  <!-- 自定义节点内容 -->
		      <template #default="scope">
				<div class="tree-item"   @dblclick="onOpenFile(scope.node)">
				  <el-icon :size="18" :color="scope.node.data.lock == 1 ? '#00aaff' : '#7a7a7a'">
					  <Setting v-if="scope.node.data.type=='setup'"/>
					  <Memo  v-else-if="scope.node.data.type=='page'" />
					  <ScaleToOriginal v-else-if="scope.node.data.type=='component'" />
					  <SetUp v-else-if="scope.node.data.type=='event'" />
					  <Menu v-else-if="scope.node.data.type=='methrod'" />
					  <Connection v-else-if="scope.node.data.type=='service'" />
					  <MessageBox v-else-if="scope.node.data.type=='resource'" />
					  <FolderOpened v-else-if="scope.node.data.type=='directory'"/>
					  <Document v-else-if="scope.node.data.type=='file'"/>
					  <CopyDocument v-else/>
				  </el-icon>
		          <div class="label-text" :style="{color: scope.node.data.lock == 1 ? '#00aaff' : '#7a7a7a'}" 
				       :draggable="scope.node.data.type=='file' && '.cpt.evt.res.vpg.mhd'.indexOf('.'+checkTealword(scope.node.data.label)) >= 0"
					   @dragstart="handleDragstart(toRaw(scope.node.data),$event)">
				      {{ scope.node.data.label}}
				  </div>
				  <div class="show-oper-box">
					  <el-tooltip class="box-item" effect="light" content="刷新数据" placement="top-start" :show-after="500"
					     v-if="scope.node.data.type !='file' && scope.node.data.type !='setup' && scope.node.data.type !='directory'">
					  	 <el-icon :size="18" @click.stop="onRefreshNodeFromDb(scope.node.data)" style="margin-left:6px;"><Refresh /></el-icon>
					  </el-tooltip>
					  <el-tooltip class="box-item" effect="light" content="新建文件夹" placement="top-start" :show-after="500" 
					     v-if="scope.node.data.type !='file' && scope.node.data.type !='setup'">
					  	 <el-icon :size="18" @click.stop="onAddFolder(scope.node.data)" style="margin-left:6px;"><FolderAdd /></el-icon>
					  </el-tooltip>
					  <el-tooltip class="box-item" effect="light" content="新建文件" placement="top-start" :show-after="500" 
					      v-if="scope.node.data.type !='file' && scope.node.data.type !='setup'">
					  	 <el-icon :size="18" @click.stop="onAddFile(scope.node.data)" style="margin-left:6px;"><Plus /></el-icon>
					  </el-tooltip>
					  <el-tooltip class="box-item" effect="light" :content="scope.node.data.lockUser ? ('被 ' +scope.node.data.lockUser + ' 锁住') : '上锁'" placement="top-start" :show-after="500" 
					      v-if="(scope.node.data.type=='file' || scope.node.data.type=='setup') && scope.node.data.lock == 0 ">
						  <el-icon :size="18" @click.stop="onLockClick(scope.node.data,1)" style="margin-left:6px;"><Lock /></el-icon>
					  </el-tooltip>
					  <el-tooltip class="box-item" effect="light" content="释放" placement="top-start" :show-after="500"
					      v-if="(scope.node.data.type=='file' || scope.node.data.type=='setup') && scope.node.data.lock == 1 ">
					  	  <el-icon :size="18" @click.stop="onLockClick(scope.node.data,0)" style="margin-left:6px;" color="#00aaff"><Unlock /></el-icon>
					  </el-tooltip>
					  <el-tooltip class="box-item" effect="light" content="克隆" placement="top-start" :show-after="500"
					      v-if="scope.node.data.type=='file'">
					      <el-icon :size="18" @click.stop="onCloneFile(scope.node)" style="margin-left:6px;transform: rotate(90deg);"><DocumentCopy /></el-icon>
					  </el-tooltip>
					  <el-tooltip class="box-item" effect="light" content="移动" placement="top-start" :show-after="500"
					      v-if="scope.node.data.type=='file' || scope.node.data.type=='directory'">
					      <el-icon :size="18" @click.stop="onChangePath(scope.node)" style="margin-left:6px;transform: rotate(90deg);"><Sort /></el-icon>
					  </el-tooltip>
					  <el-tooltip class="box-item" effect="light" content="编辑" placement="top-start" :show-after="500" 
					      v-if="scope.node.data.type=='file' || scope.node.data.type=='directory'">
						  <el-icon :size="18" @click.stop="onEditFile(scope.node.data)" style="margin-left:6px;"><Edit /></el-icon>
					  </el-tooltip>
					  <el-tooltip class="box-item" effect="light" content="删除" placement="top-start" :show-after="500" 
					      v-if="scope.node.data.type=='file' || scope.node.data.type=='directory'">
					      <el-icon :size="18" @click.stop="onDeleteFile(scope.node)" style="margin-left:6px;"><Delete /></el-icon>
					  </el-tooltip>
				  </div>
				</div>
		      </template>
		  </el-tree>
	</div>
</template>

<script lang="ts" setup>
	import {reactive ,inject,onMounted,ref,watch,computed,onUnmounted, nextTick,toRaw} from 'vue'
	import { ElTree,ElMessage, ElMessageBox } from 'element-plus'
	interface TreeNode {
	  id: number,
	  label: string,
	  type: string,
	  fileId?: string,
	  filePath?: string,
	  lock?:number,
	  lockUser?:string,
	  children?:TreeNode[],
	}
	const treeRef = ref<InstanceType<typeof ElTree>>()
	const ProjectRef = inject("ProjectRef")
	const layoutParam = inject("layoutParam")
	const keyword = ref('')
	const treeMgr = reactive({
		treeData:[],
		setupTop:null,
		pageTop:null,
		componentTop:null,
		eventTop:null,
		methrodTop:null,
		serviceTop:null,
		resourceTop:null
	})
	
	function initTree(){
		treeMgr.treeData = []
		treeMgr.setupTop = createTreeNode(1,'配置','setup','setup.top.setup',0,'')
		treeMgr.treeData.push(treeMgr.setupTop)
		
		treeMgr.pageTop  = createTreeNode(2,'页面','page','page.top',0,'')
		treeMgr.treeData.push(treeMgr.pageTop)
		
		treeMgr.componentTop = createTreeNode(3,'组件','component','component.top',0,'')
		treeMgr.treeData.push(treeMgr.componentTop)
		
		treeMgr.eventTop = createTreeNode(4,'事件','event','event.top',0,'')
		treeMgr.treeData.push(treeMgr.eventTop)
		
		treeMgr.methrodTop = createTreeNode(5,'函数','methrod','methrod.top',0,'')
		treeMgr.treeData.push(treeMgr.methrodTop)
		
		treeMgr.serviceTop = createTreeNode(6,'服务','service','service.top',0,'')
		treeMgr.treeData.push(treeMgr.serviceTop)
		
		treeMgr.resourceTop = createTreeNode(7,'资源','resource','resource.top',0,'')
		treeMgr.treeData.push(treeMgr.resourceTop)
		
		refreshTreeNodes()
	}
	
	function refreshTreeNodes(){
		// console.log(treeMgr.treeData)
		treeMgr.setupTop.fileId = ProjectRef.BasicData.fileData.setupFile.projectId
		if(ProjectRef.BasicData.fileData.pageFiles.length > 0){
			treeMgr.pageTop.children = []
			for(let i=0;i< ProjectRef.BasicData.fileData.pageFiles.length;i++){
				if(ProjectRef.BasicData.fileData.pageFiles[i].filePath.substring(0,8) == 'page.top'){
				  addTreeNode(treeMgr.pageTop,ProjectRef.BasicData.fileData.pageFiles[i],2)
				}
			}
		}
		
		if(ProjectRef.BasicData.fileData.componentFiles.length > 0){
			treeMgr.componentTop.children = []
			for(let i=0;i< ProjectRef.BasicData.fileData.componentFiles.length;i++){
				if(ProjectRef.BasicData.fileData.componentFiles[i].filePath.substring(0,13) == 'component.top'){
				  addTreeNode(treeMgr.componentTop,ProjectRef.BasicData.fileData.componentFiles[i],2)
				}
			}
		}
		
		if(ProjectRef.BasicData.fileData.eventFiles.length > 0){
			treeMgr.eventTop.children = []
			for(let i=0;i< ProjectRef.BasicData.fileData.eventFiles.length;i++){
				if(ProjectRef.BasicData.fileData.eventFiles[i].filePath.substring(0,9) == 'event.top'){
				   addTreeNode(treeMgr.eventTop,ProjectRef.BasicData.fileData.eventFiles[i],2)
				}
			}
		}
		
		if(ProjectRef.BasicData.fileData.methrodFiles.length > 0){
			treeMgr.methrodTop.children = []
			for(let i=0;i< ProjectRef.BasicData.fileData.methrodFiles.length;i++){
				if(ProjectRef.BasicData.fileData.methrodFiles[i].filePath.substring(0,11) == 'methrod.top'){
				addTreeNode(treeMgr.methrodTop,ProjectRef.BasicData.fileData.methrodFiles[i],2)
				}
			}
		}
		
		if(ProjectRef.BasicData.fileData.serviceFiles.length > 0){
			treeMgr.serviceTop.children = []
			for(let i=0;i< ProjectRef.BasicData.fileData.serviceFiles.length;i++){
				if(ProjectRef.BasicData.fileData.serviceFiles[i].filePath.substring(0,11) == 'service.top'){
				addTreeNode(treeMgr.serviceTop,ProjectRef.BasicData.fileData.serviceFiles[i],2)
				}
			}
		}
		
		if(ProjectRef.BasicData.fileData.sourceFiles.length > 0){
			treeMgr.resourceTop.children = []
			for(let i=0;i< ProjectRef.BasicData.fileData.sourceFiles.length;i++){
				if(ProjectRef.BasicData.fileData.sourceFiles[i].filePath.substring(0,12) == 'resource.top'){
				addTreeNode(treeMgr.resourceTop,ProjectRef.BasicData.fileData.sourceFiles[i],2)
				}
			}
		}
	}
	
	function addTreeNode(parent:TreeNode,xItem:object,index:number){
		let pathArray = xItem.filePath.split(".")
		if(pathArray.length > 0 ){
			let name = pathArray[index]
			let lastName = pathArray[pathArray.length - 1]
			if(index + 1 == pathArray.length - 1 && ".vpg.cpt.evt.mhd.sve.res".indexOf("."+lastName) >= 0){
				if(!parent.hasOwnProperty('children')) parent.children = []
				let nodeLabel = name+"."+lastName
				let nodePath = pathArray.join(".")
				for(let n=0;n<parent.children.length;n++){
					if(parent.children[n].filePath == nodePath){
						return 
					}
				}
				parent.children.push(createTreeNode(0,nodeLabel,'file',nodePath,xItem.lock,xItem.lockUser))
				return
			}
		   if(parent.hasOwnProperty('children') && parent.children.length > 0){
				for(let n=0;n<parent.children.length;n++){
					let tmpNode:TreeNode = parent.children[n]
					if(tmpNode.label == name){
						if(index < pathArray.length - 1){
							let nextIndex = index + 1
							addTreeNode(tmpNode,xItem,nextIndex)
						}
						return
					}
				}
			}
			let item:TreeNode = createTreeNode(0,name,'directory',pathArray.slice(0,index).join(".")+"."+name,0,'')
			if(index < pathArray.length - 1){
				let nextIndex = index + 1
				addTreeNode(item,xItem,nextIndex)
			}
			parent.children.push(item)
		}
	}

	function createTreeNode(id:number,label:string,type:string,path:string,lock:number,lockUser:string){
		return {
			id: id,
			label: label,
			fileId:'0',
			type:type,
			filePath:path,
			lock:lock,
			lockUser:lockUser,
			children:[]
		}
	}
	
	function getTreeNode(path:string){
		if(path.length > 0){
			let pathArray = path.split(".")
			if(pathArray[0]=='page'){
				return getInnerNode(treeMgr.pageTop,pathArray,2)
			}else if(pathArray[0]=='component'){
				return getInnerNode(treeMgr.componentTop,pathArray,2)
			}else if(pathArray[0]=='event'){
				return getInnerNode(treeMgr.eventTop,pathArray,2)
			}else if(pathArray[0]=='methrod'){
				return getInnerNode(treeMgr.methrodTop,pathArray,2)
			}else if(pathArray[0]=='service'){
				return getInnerNode(treeMgr.serviceTop,pathArray,2)
			}else if(pathArray[0]=='resource'){
				return getInnerNode(treeMgr.resourceTop,pathArray,2)
			}
		}
		return null
	}
	
	function getInnerNode(parent:object,pathArray:string[],index:number){
		let firstName = pathArray.slice(0,index).join(".")+"."+pathArray[index]
		if(pathArray.length - 1 == index + 1 && ".vpg.cpt.evt.mhd.sve.res".indexOf("."+pathArray[pathArray.length - 1]) >= 0){
			firstName += "."+pathArray[pathArray.length - 1]
		}
		if(parent.hasOwnProperty('children') && parent.children.length > 0){
			for(let i=0;i<parent.children.length;i++){
				if(parent.children[i].filePath == firstName){
					if(".vpg.cpt.evt.mhd.sve.res".indexOf("."+pathArray[pathArray.length - 1]) >= 0){
						if(index < pathArray.length - 2){
							let nextIndex = index + 1
							return getInnerNode(parent.children[i],pathArray,nextIndex)
						}
					}else{
						if(index < pathArray.length - 1){
							let nextIndex = index + 1
							return getInnerNode(parent.children[i],pathArray,nextIndex)
						}
					}
					return parent.children[i]
				}
			}
		}
		return null
	}
	
	function onRefreshNodeFromDb(data){
		ProjectRef.funcObj.loadProjectFiles({path:data.type+".top"},()=>{
			refreshTreeNodes()
		})
		
	}
	
		
	function checkTealword(path:string){
		return path.split(".").slice(-1)[0]
	}
	
	function handleDragstart(data,e){
		e.dataTransfer.setData('treeItem',data.filePath)
	}
	
	watch(keyword, (val) => {
	  treeRef.value!.filter(val)
	})
	
	const filterNode = (value: string, data: TreeNode) => {
	  if(value == 'lock'){
		  return data.type=='file' && data.lock == 1
	  }else{
		  if (!value) return true
		  return data.label.includes(value)
	  }
	}
	
	// 通用的文件检查和打开函数
	function checkAndOpenFileWithRefresh(filePath, fileType, onNotFound) {
		const fileList = fileType === 'methrod' ? ProjectRef.BasicData.fileData.methrodFiles : ProjectRef.BasicData.fileData.eventFiles

		// 检查文件是否存在
		for(let f = 0; f < fileList.length; f++){
			if(fileList[f].filePath === filePath){
				ProjectRef.funcObj.openFile(filePath)
				return
			}
		}

		// 文件不存在，如果文件列表为空则刷新一次
		if(fileList.length === 0){
			onRefreshNodeFromDb({type: fileType})
			setTimeout(() => {
				const refreshedList = fileType === 'methrod' ? ProjectRef.BasicData.fileData.methrodFiles : ProjectRef.BasicData.fileData.eventFiles
				for(let f = 0; f < refreshedList.length; f++){
					if(refreshedList[f].filePath === filePath){
						ProjectRef.funcObj.openFile(filePath)
						return
					}
				}
				// 刷新后仍然没找到，执行回调
				onNotFound()
			}, 500)
		} else {
			// 文件列表不为空但文件不存在，直接执行回调
			onNotFound()
		}
	}

	onMounted(()=>{
		ProjectRef.funcObj.otherFunc.initTree = initTree
		ProjectRef.funcObj.otherFunc.refreshTree = refreshTreeNodes
		ProjectRef.funcObj.otherFunc.onRefreshNodeFromDb = onRefreshNodeFromDb
		ProjectRef.funcObj.otherFunc.checkAndOpenFileWithRefresh = checkAndOpenFileWithRefresh
		initTree()
	})
	
	onUnmounted(()=>{
		
	})
	
	function onOpenFile(targetNode:object){
		const itemData = targetNode.data
		const pathArray = itemData.filePath.split(".")
		if(".vpg.cpt.evt.mhd.sve.res".indexOf("."+pathArray[pathArray.length - 1]) >= 0 || pathArray[0]=='setup'){
			ProjectRef.funcObj.openFile(itemData.filePath)
		}else{
			if(itemData.type=='page'){
				if(ProjectRef.BasicData.fileData.pageFiles.length == 0){
					onRefreshNodeFromDb({type:itemData.type})
				}
			}else if(itemData.type=="component"){
				if(ProjectRef.BasicData.fileData.componentFiles.length == 0){
					onRefreshNodeFromDb({type:itemData.type})
				}
			}else if(itemData.type=="event"){
				if(ProjectRef.BasicData.fileData.eventFiles.length == 0){
					onRefreshNodeFromDb({type:itemData.type})
				}
			}else if(itemData.type=="methrod"){
				if(ProjectRef.BasicData.fileData.methrodFiles.length == 0){
					onRefreshNodeFromDb({type:itemData.type})
				}
			}else if(itemData.type=="service"){
				if(ProjectRef.BasicData.fileData.serviceFiles.length == 0){
					onRefreshNodeFromDb({type:itemData.type})
				}
			}else if(itemData.type=="resource"){
				if(ProjectRef.BasicData.fileData.sourceFiles.length == 0){
					onRefreshNodeFromDb({type:itemData.type})
				}
			}
		}
	}
	
	function onLockClick(item:TreeNode,lock:number){
		let toLock = item.lock==0 ? 1 : 0
		
		ProjectRef.funcObj.lockFile(item.filePath,toLock,(data)=>{
			item.lockUser = data.lockName
			if(data.success){
				item.lock = toLock
				if(toLock==0){
					item.lockUser = ''
				}
				let targetArray = ProjectRef.funcObj.getSourceArray(item.filePath)
				if(targetArray && targetArray.length > 0){
					for(let i=0;i<targetArray.length;i++){
						if(targetArray[i].filePath == item.filePath){
							targetArray[i].lock = item.lock 
							targetArray[i].lockUser = item.lockUser
							let logMsg = item.lock ==1 ? '锁定文件 ：' : '解锁文件：'
							ProjectRef.funcObj.sendLog(logMsg + '[ '+item.label+' ]')
							return
						}
					}
				}
			}else{
				ElMessage({
					message:'本文件已经被['+data.lockName+']锁住！',
					type:'warning'
				})
			}
		})

		
	}
	function onAddFolder(item:TreeNode){
	    ElMessageBox.prompt('输入文件夹名称', '添加文件夹', {
	       confirmButtonText: '创建',
	       cancelButtonText: '取消',
	       inputPattern:
	         /[\w]?[\w]*?/,
	       inputErrorMessage: '不是有效的文件夹名称',
	     })
	       .then(({ value }) => {
			  let labelArray = item.label.split('.')
			  if(!(labelArray.length > 1 && ".vpg.cpt.evt.mhd.sve.res".indexOf("."+labelArray[labelArray.length - 1]) >= 0)){
                  if(item.hasOwnProperty('children') && item.children.length > 0){
					  for(let k=0;k<item.children.length;k++){
						  if(item.children[k].label == value){
							  return
						  }
					  }
				  }
				  item.children.push(createTreeNode(item.id*10,value,'directory',item.filePath+"."+value,0,''))
				  ProjectRef.funcObj.sendLog("创建文件夹：" + value)
			  }
			  
	       })
	       .catch(() => {
	         
	       })
	}
	function onAddFile(item:TreeNode){
		const type = item.filePath.split('.')[0]
		let fileName = ""
		let title = ""
		if(type=='page'){
			title = "页面"
			fileName = ".vpg"
		}else if(type=='component'){
			title = "组件"
			fileName = ".cpt"
		}else if(type=='event'){
			title = "事件"
			fileName = ".evt"
		}else if(type=='methrod'){
			title = "方法"
			fileName = ".mhd"
		}else if(type=='service'){
			title = "服务"
			fileName = ".sve"
		}else if(type=='resource'){
			title = "资源"
			fileName = ".res"
		}
		ElMessageBox.prompt('输入'+title+'名称', '添加'+title, {
		   confirmButtonText: '添加',
		   cancelButtonText: '取消',
		   inputPattern:
		     /[\w]?[\w]*?/,
		   inputErrorMessage: '不是有效的文件名称',
		 })
		   .then(({ value }) => {
			   fileName = value+fileName
			   let filePath = item.filePath+"."+fileName
			   ProjectRef.funcObj.createFile(filePath,(data)=>{
				   if(data.code==200){
					   ProjectRef.funcObj.openFile(filePath)
					   ProjectRef.funcObj.sendLog("创建文件：" + fileName)
				   }
				   
			   })
		   })
		   .catch(() => {
		     
		   })
	}
	
	function onCloneFile(node:TreeNode){
		const item:TreeNode = node.data
		let pathArray = item.filePath.split(".")
		if(".vpg.cpt.evt.mhd.sve.res".indexOf("."+ pathArray[pathArray.length - 1]) >= 0){
			pathArray[pathArray.length - 2] = pathArray[pathArray.length - 2]+"_Copy"
		    ElMessageBox.prompt('新路径为:', "克隆文件", {
		       inputValue: pathArray.join("."),
		       confirmButtonText: '克隆',
		       cancelButtonText: '取消',
		     })
		       .then(({ value }) => {
				   ProjectRef.funcObj.cloneFile(item.filePath,value)
		       })
		       .catch(() => {
		         
		       })
		}
		
	}
	
	function onChangePath(node:TreeNode){
		const item:TreeNode = node.data
		// const parent:TreeNode = node.parent.data
		let pathArray = item.filePath.split(".")
		let currentPath = pathArray.slice(0,-1).join(".")
		let sourceFolderName = ""
		if(".vpg.cpt.evt.mhd.sve.res".indexOf("."+ pathArray[pathArray.length - 1]) >= 0){
			currentPath = pathArray.slice(0,-2).join(".")
		}else{
			sourceFolderName = pathArray.slice(-1)[0]
		}
		ElMessageBox.prompt('[ '+item.label+' ] 新路径为:', "更改路径", {
		   inputValue: currentPath,
		   confirmButtonText: '更改',
		   cancelButtonText: '取消',
		 })
		   .then(({ value }) => {
			   let path = item.filePath
			   let newPath = value+(sourceFolderName != '' ? ("." + sourceFolderName) : "")
			   ProjectRef.funcObj.batchUpdateFile({
				   path:path,
				   newPath:newPath
			   },()=>{
				   refreshTreeNodes()
				   ProjectRef.funcObj.sendLog("原路径：" + path +"，更改成:" + newPath)
			   })
			   
		   })
		   .catch(() => {
		     
		   })
	}
	
	function onEditFile(item:TreeNode){
		let pmpt = item.type=='file' ? '原文件名称' : (item.type=='directory' ? '原文件夹名称' : '原来名称')
		let title = item.type=='file' ? '更改文件名' : (item.type=='directory' ? '更改文件夹名称:' : '更改名称:')
		let realName = item.type == 'file' ? item.label.split('.')[0] : item.label
		ElMessageBox.prompt(pmpt+'： [ '+item.label+' ]', title, {
		   inputValue: realName,
		   confirmButtonText: '更改',
		   cancelButtonText: '取消',
		   inputPattern:
		     /[\w]?[\w]*?/,
		   inputErrorMessage: '不是有效的文件夹名称',
		 })
		   .then(({ value }) => {
			   let oldPath = item.filePath
			   let itemObj = getTreeNode(oldPath)
			   if(itemObj && itemObj != null){
				   let pathArray = item.filePath.split(".")
				   if(".vpg.cpt.evt.mhd.sve.res".indexOf("."+ pathArray[pathArray.length - 1]) >= 0){
					   pathArray[pathArray.length - 2] = value
					   let newPath = pathArray.join(".")
					   ProjectRef.funcObj.updateFile({
						   path:oldPath,
						   newPath:newPath
					   },(data)=>{
						   itemObj.filePath = newPath
						   let oldFileName = itemObj.label
						   itemObj.label = value + "." + pathArray[pathArray.length - 1]
						   ProjectRef.funcObj.sendLog("原文件：" + oldFileName +"，更改成:" + itemObj.label)
						   let srcObj = ProjectRef.funcObj.getSourceItem(oldPath)
						   if(srcObj != null){
							   srcObj.filePath = pathArray.join(".")
							   srcObj.title = value + "." + pathArray[pathArray.length - 1]
						   }
					   })
				   }else{
					   pathArray[pathArray.length - 1] = value
					   let newPath = pathArray.join(".")
					   ProjectRef.funcObj.batchUpdateFile({
						   path:oldPath,
						   newPath:newPath
					   },()=>{
						   refreshTreeNodes()
						   ProjectRef.funcObj.sendLog("原文件夹：" + oldPath +"，更改成:" + item.filePath)
					   })
					   
					   
				   }
			   }
		   })
		   .catch(() => {
		     
		   })
	}
	function onDeleteFile(node){
		const item:TreeNode = node.data
		const parent:TreeNode = node.parent.data
		ElMessageBox.confirm(
		    '确定删除 [ '+item.label+' ]吗？',
		    '删除',
		    {
		      confirmButtonText: '删除',
		      cancelButtonText: '取消',
		      type: 'warning',
		    }
		  )
		    .then(() => {
				let path = item.filePath
				ProjectRef.funcObj.deleteFile(path,()=>{
					for(let i=0;i<parent.children.length;i++){
						if(path == parent.children[i].filePath){
							parent.children.splice(i,1)
							ProjectRef.funcObj.removeSourceItem(path)
							refreshTreeNodes()
							ProjectRef.funcObj.sendLog("已经删除：" + path)
							return
						}
					}
				})
		    })
		    .catch(() => {
		      
		    })
	}
</script>

<style lang="scss" scoped>
	 .tree-box {
		 height:calc(100%);
		 width:calc(100%);
		 overflow: hidden;
		 .tree-item {
			 display: flex;
			 justify-content: space-between;
			 align-items: center;
			 height:40px;
			 font-size: 15px;
		 }
		 
		 .refresh-btn {
			 width:26px;
			 height:26px;
			 border-radius: 20px;
			 // border:1px solid #00aaff;
			 display: flex;
			 justify-content: center;
			 align-items: center;
			 margin:2px 6px;
			 cursor:pointer;
			 transition:all 0.4s;
		 }
		 .refresh-btn:hover {
			 // border:1px solid #52defd;
			 transform: rotate(90deg);
		 }
		 .refresh-btn:active{
			 transform: rotate(-90deg);
		 }
	 }
	 .tree-item{
		 padding-right:10px;
		 display: flex;
		 justify-content: space-between;
		 align-items: center;
		 height:40px;
		 width:100%;
		 
	 }
	 .show-oper-box {
		 display:none;
	 }
	 .tree-item:hover .show-oper-box{
		 display:inline;
	 }
	 .label-text {
		 margin-left:6px;
		 flex:1;
	 }
	 :deep(.el-tree){
		 overflow: auto;
	 }
</style>