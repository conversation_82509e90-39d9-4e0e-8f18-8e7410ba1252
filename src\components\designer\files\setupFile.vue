<template>
	<div class="content file-box" style="height:calc(100% - 10px);">
		<div class="title-bar">Project Setup -- 项目全局参数配置页</div>
		<el-tabs type="border-card" style="margin-left:4px;">
		    <el-tab-pane label="首页配置">
			   <div class="pane-box" :style="{height: 'calc(100vh - 230px - ' + layoutParam.bottomTabHeight+'px)'}">
				   <div class="setup-line-box" @dragover.prevent="onDragOver" @drop="onDrop(1,$event)" >
					   <span>首页启动页路径:</span>
					   <el-input v-model="setupData.firstPage.loadPath" style="flex:1;margin-left:20px;" placeholder="请输入启动页路径..." 
					        clearable @input="onchange" />
				   </div>
				   <div class="setup-line-box" @dragover.prevent="onDragOver" @drop="onDrop(2,$event)">
					   <span>默认登录页路径:</span>
					   <el-input v-model="setupData.firstPage.defaultLoginPath" style="flex:1;margin-left:20px;" placeholder="请输入登录页路径..." clearable
						    @input="onchange"/>
				   </div>
				   <div class="setup-line-box" @dragover.prevent="onDragOver" @drop="onDrop(3,$event)">
					   <span>设置欢迎页路径:</span>
					   <el-input v-model="setupData.firstPage.welcomePath" style="flex:1;margin-left:20px;" placeholder="请输入欢迎页路径..." 
					       clearable  @input="onchange" />
				   </div>
				   <div class="setup-line-box" style="justify-content: flex-start;">
				   		<span>　当前应用类型:</span>
						<div style="margin-left:20px;display: flex;justify-content: center;align-items: center;">
							<el-icon :size="17" v-if="ProjectRef.BasicData.clientType==0" ><Iphone /></el-icon>
							<el-icon :size="17" v-else><Monitor /></el-icon>
							<span style="margin-left:5px;">{{ProjectRef.BasicData.clientType==0 ? '[手机应用]' : '[PC应用]'}}</span>	
						</div>
				   </div>
			   </div>
			</el-tab-pane>
		    <el-tab-pane label="数据库连接">
				<div class="pane-box" :style="{height: 'calc(100vh - 230px - ' + layoutParam.bottomTabHeight+'px)'}">
					<div class="setup-line-box" style="justify-content: flex-start;">
						<span style="width:160px;">选择数据源:</span>
						<el-select
						  v-model="ProjectRef.DataBase.databaseName"
						  placeholder="选择一个数据源.."
						  size="large"
						  style="width:500px;"
						  @change="onDbChange"
						>
						  <el-option
							v-for="item in ProjectRef.DataBase.validDB"
							:key="item.id"
							:label="item.name"
							:value="item"
						  />
						</el-select>				   
					</div>			
					<div class="setup-line-box" style="justify-content: flex-start;">
						<span style="width:160px;"></span>
						<div class="setup-btn" @click.stop="onConnectTest()">测试连接</div>
						<div class="setup-btn" @click.stop="onRefreshDbList()">刷新数据源列表</div>
						<div class="setup-btn" @click.stop="onNavToDbMgr()">去创建数据源</div>
					</div>
				</div>
			</el-tab-pane>
		    <el-tab-pane label="全局自定义函数">
				<div class="pane-box" :style="{height: 'calc(100vh - 230px - ' + layoutParam.bottomTabHeight+'px)'}">
					<div class="methd-panel">
						<div class="label-title">管理自定义函数，在此定的函数可以用于表达式、脚本中.</div>
						<div class="file-btn" @click.stop="addFuncSetupItem()">
							<el-icon :size="15">
								<DocumentAdd />
							</el-icon>
							<div class="file-label" >新加</div>
						</div>
					</div>	
					<div class="methd-list" >
						<el-table :data="setupData.globalFunc" style="width: 100%;" ref="funcTableRef"
						   :style="{height: 'calc(100vh - 290px - ' + layoutParam.bottomTabHeight+'px)'}">
						    <el-table-column prop="name" label="方法名称" width="300" >
								<template #default="scope">
									<el-input v-model="scope.row.name" style="width: 100%;" placeholder="请自定义方法名..." clearable @input="onchange" />
								</template>
							</el-table-column>
						    <el-table-column label="事件文件路径 (Event File)" >
								<template #default="scope">
									<div style="width: 100%;" @dragover.prevent="onDragOver" @drop="onFunDrop(scope.$index,$event)">
									<el-input v-model="scope.row.path" style="width: 100%;" placeholder="请选择文件路径..." clearable  @input="onchange"/>
									</div>
								</template>
							</el-table-column>
							<el-table-column label="操作" width="60">
								<template #default="scope">
									<el-icon :size="15" style="cursor: pointer;" @click.stop="delFuncSetupItem(scope.row.name,scope.$index)"><Close /></el-icon>
								</template>
							</el-table-column>
						  </el-table>
					</div>
				</div>
			</el-tab-pane>
		    <el-tab-pane label="支付配置">
				<div class="pane-box" :style="{height: 'calc(100vh - 230px - ' + layoutParam.bottomTabHeight+'px)'}">
								   
				</div>
			</el-tab-pane>
		    <el-tab-pane label="短信配置">
				<div class="pane-box" :style="{height: 'calc(100vh - 230px - ' + layoutParam.bottomTabHeight+'px)'}">
								   
				</div>
			</el-tab-pane>
		    <el-tab-pane label="推送配置">
				<div class="pane-box" :style="{height: 'calc(100vh - 230px - ' + layoutParam.bottomTabHeight+'px)'}">
								   
				</div>
			</el-tab-pane>
			<el-tab-pane label="环境参数">
				<div class="pane-box" :style="{height: 'calc(100vh - 230px - ' + layoutParam.bottomTabHeight+'px)'}">
						<div class="methd-panel">
							<div class="label-title">管理项目中的配置常数参数，如：地图中的KEY，等等...</div>
							<div class="file-btn" @click.stop="addConstParam()">
								<el-icon :size="15">
									<DocumentAdd />
								</el-icon>
								<div class="file-label" >新加</div>
							</div>
						</div>	
						<div class="methd-list" >
							<el-table :data="setupData.envirConfig" style="width: 100%;" ref="funcTableRef2"
							   :style="{height: 'calc(100vh - 290px - ' + layoutParam.bottomTabHeight+'px)'}">
							    <el-table-column prop="name" label="参数名称" width="230" >
									<template #default="scope">
										<el-input v-model="scope.row.name" style="width: 100%;" placeholder="请参数名称..." clearable @input="onchange" />
									</template>
								</el-table-column>
							    <el-table-column label="配置值" >
									<template #default="scope">
										<el-input v-model="scope.row.value" style="width: 100%;" placeholder="输入配置值..." clearable  @input="onchange"/>
									</template>
								</el-table-column>
								<el-table-column label="说明" >
									<template #default="scope">
										<el-input v-model="scope.row.remark" style="width: 100%;" placeholder="输入说明内容..." clearable @input="onchange"/>
									</template>
								</el-table-column>
								<el-table-column label="操作" width="60">
									<template #default="scope">
										<el-icon :size="15" style="cursor: pointer;" @click.stop="delConstParam(scope.row.name,scope.$index)"><Close /></el-icon>
									</template>
								</el-table-column>
							  </el-table>
						</div>		   
				</div>
			</el-tab-pane>
		  </el-tabs>
	</div>
</template>

<script lang="ts" setup>
	import {reactive,ref,onMounted,inject,watch,toRaw, nextTick} from 'vue'
	import { useFileBase } from '../../../common/fileBasic';
	import {apiReadDbConnectList,apiTestDbConnect,apiCheckFile ,apiLoadSetup,apiUpdateFile} from '../../../apis/designer.js'
	import {  ElMessage, ElMessageBox } from "element-plus";
	import { useRouter } from "vue-router";

	const rounter = useRouter()
	const fileBase = useFileBase()
	const props = defineProps({
		fileItem:{
			type:Object,
			default:{}
		}
	})
	const ProjectRef = inject("ProjectRef")
	const layoutParam = inject("layoutParam")
	const funcTableRef = ref(null)
	const funcTableRef2 = ref(null)
	const setupVersion = ref(0)
	
	const setupData = reactive({
		firstPage:{
			welcomePath: "page.top.welcomepage.vpg",
			loadPath: "page.top.homepage.vpg",
			defaultLoginPath: "page.top.loginpage.vpg"
		},
		dbAccess: {},
		globalFunc: [
			{
				path: "func.top.test.aa.func",
				name: "aaa"
			}
		],
		payConfig: [],
		messageConfig: [],
		pushConfig: [],
		envirConfig: [
			{
				name: "aaa",
				remark: "testaaa",
				value: "bbb"
			},
			{
				name: "bbb",
				remark: "testbbbb",
				value: "ddd"
			}
		]
	})
	
	onMounted(()=>{
         props.fileItem.func.saveData = saveData
         loadSetupFile()
		 onRefreshDbList()
	})
	
	function saveData(success){
        //保存前做检查
		let pathArray = []
		if(setupData.firstPage.defaultLoginPath.trim().length > 0){
		   pathArray.push(setupData.firstPage.defaultLoginPath)
		}
		if(setupData.firstPage.loadPath.trim().length > 0){
		   pathArray.push(setupData.firstPage.loadPath)
		}
		if(setupData.firstPage.welcomePath.trim().length > 0){
		   pathArray.push(setupData.firstPage.welcomePath)
		}
		
		for(let rowItem of setupData.globalFunc){
			if(rowItem.path && rowItem.path.trim().length > 0){
				pathArray.push(rowItem.path)
			}
		}
		if(pathArray && pathArray.length > 0){
			checkFile({
				path:pathArray,
			},(data)=>{
				// console.log("result",data)
				if(data.failure && data.failure.length > 0){
					ElMessage({
						message:"请检文件路径，有不存文件："+JSON.stringify(data.failure),
						type:"warning"
					})
				}else if(data.success && data.success.length == pathArray.length){
					saveSetupData(success)
				}
			})
		}else{
			saveSetupData(success)
		}
		
	}

	function onDragOver(e){
		e.preventDefault();
	}
	function onDrop(index,e){
		const res = e.dataTransfer.getData("treeItem")
		if(res.length > 0){
			let pathArray = res.split(".")
			if(pathArray[0]=='page'){
				if(index == 1){
					setupData.firstPage.loadPath = res
				}else if(index==2){
					setupData.firstPage.defaultLoginPath = res
				}else if(index ==3){
					setupData.firstPage.welcomePath = res
				}
				props.fileItem.isModify = true
			}else if(pathArray[0]== 'methrod'){
				if(index == 1){
					setupData.firstPage.loadPath = res
				}
			}
		}
	}
	
	function onFunDrop(index,e){
		const res = e.dataTransfer.getData("treeItem")
		if(res.length > 0){
			let pathArray = res.split(".")
			if(pathArray[0]=='event'){
				setupData.globalFunc[index].path = res
				props.fileItem.isModify = true
			}
		}
	}
	
	function onDbChange(item){
		if(item){
		  ProjectRef.DataBase.databaseName = item.name
		  setupData.dbAccess.name = item.name
		  ProjectRef.DataBase.databaseId = item.id
		  setupData.dbAccess.id = item.id
		  props.fileItem.isModify = true
		}
	}
	
	function onchange(item){
		if(item){
		  props.fileItem.isModify = true
		}
	}
	
	function onRefreshDbList(){
		readDbConnectList({
			projectId:ProjectRef.BasicData.ProjectId,
		})
	}
	
	function onNavToDbMgr(){
		rounter.push({
			name:"DataDesignList"
		})
	}
	
	function onConnectTest(){
		if(ProjectRef.DataBase.databaseId){
			testDbConnect({
				projectId:ProjectRef.BasicData.ProjectId,
				id:ProjectRef.DataBase.databaseId,
			})
		}else{
			ElMessage({
			  message: "数据源不能为空",
			  type: "warning",
			})
		}
	}
	
	//----- 自定义函数
	
	function addFuncSetupItem(){
		setupData.globalFunc.push({
			name:"",
			path:"",
		})
		props.fileItem.isModify = true
		nextTick(()=>{
			if (funcTableRef.value) {
				let pos = (setupData.globalFunc.length * 100)
			    funcTableRef.value.scrollTo({
			      top: pos,
			      behavior: 'smooth', // 平滑滚动
			    });
			}
		})
	}
	
	function delFuncSetupItem(name:string,index:number){
		if(name && name.length > 0){
			let content = '确定删除此方法 ['+name+'] 吗？'
		
			ElMessageBox.confirm(content,'删除',
				{
				  confirmButtonText: '删除',
				  cancelButtonText: '取消',
				  type: 'warning',
				}
			  )
				.then(() => {
				  setupData.globalFunc.splice(index,1)
				  props.fileItem.isModify = true
				})
				.catch(() => {
				  
				})
		}else{
			setupData.globalFunc.splice(index,1)
			props.fileItem.isModify = true
		}
	}
	
	//----- 常量参数（环境参数）
	
	function addConstParam(){
		setupData.envirConfig.push({
			name:"",
			value:"",
			remark:""
		})
		props.fileItem.isModify = true
		nextTick(()=>{
			if (funcTableRef2.value) {
				let pos = (setupData.envirConfig.length * 100)
			    funcTableRef2.value.scrollTo({
			      top: pos,
			      behavior: 'smooth', // 平滑滚动
			    });
			}
		})
	}
	
	function delConstParam(name:string,index:number){
		if(name && name.length > 0){
			let content = '确定删除此参数 ['+name+'] 吗？'
		
			ElMessageBox.confirm(content,'删除',
				{
				  confirmButtonText: '删除',
				  cancelButtonText: '取消',
				  type: 'warning',
				}
			  )
				.then(() => {
				  setupData.envirConfig.splice(index,1)
				  props.fileItem.isModify = true
				})
				.catch(() => {
				  
				})
		}else{
			setupData.envirConfig.splice(index,1)
			props.fileItem.isModify = true
		}
	}
	
	
	async function readDbConnectList(data){
		let res = await apiReadDbConnectList(data)
		if(res.code==200){
			ProjectRef.DataBase.validDB = res.data
		}
	}
	
	async function testDbConnect(data){
		let res = await apiTestDbConnect(data)
		if(res.code==200){
			ElMessage({
			  message: "测试成功",
			  type: "success",
			});
		}else{
			ElMessage({
			  message: "测试失败",
			  type: "warning",
			});
		}
	}

	async function loadSetupFile(){
		let res = await apiLoadSetup({
			projectId:ProjectRef.BasicData.ProjectId
		})
		if(res.code==200){
			setupVersion.value = res.data.version
			setupData.firstPage.loadPath = res.data.firstPage.loadPath
			setupData.firstPage.defaultLoginPath = res.data.firstPage.defaultLoginPath
			setupData.firstPage.welcomePath = res.data.firstPage.welcomePath
			setupData.dbAccess.name  = res.data.dbAccess.name
			setupData.dbAccess.id = res.data.dbAccess.id
			ProjectRef.DataBase.databaseName = setupData.dbAccess.name
			ProjectRef.DataBase.databaseId = setupData.dbAccess.id
			setupData.globalFunc= res.data.globalFunc
			setupData.payConfig = res.data.payConfig
			setupData.messageConfig = res.data.messageConfig
			setupData.pushConfig = res.data.pushConfig
			setupData.envirConfig = res.data.envirConfig
			console.log(" read version:"+ setupVersion.value,res)
		}else{
			console.log("read error",res)
		}
	}
	
	async function saveSetupData(success){
		let setupSaveData = toRaw(setupData)
		console.log(" save setup:",setupSaveData)
		let res = await apiUpdateFile({
			projectId:ProjectRef.BasicData.ProjectId,
			version:setupVersion.value,
			path:'setup.top.setup',
			_json:setupSaveData
		})
        if(res.code==200){
			if(res.data.hasOwnProperty("success") && res.data.success==false){
				ElMessage({
					message:res.message,
					type:"error"
				})
			}else{
			    setupVersion.value++
				props.fileItem.isModify = false
				if(success) success()
				ElMessage({
					message:"保存成功",
					type:"success"
				})
			}
		}
	}
	
	async function checkFile(data,success){
		data.projectId = ProjectRef.BasicData.ProjectId
		let res = await apiCheckFile(data)
		if(res.code==200){
			if(success) success(res.data)
		}
	}
</script>

<style lang="scss" scoped>
	.content{
		user-select: none;
		.title-bar{
			text-align: left;
			margin-top: 10px;
			margin-left:20px;
			margin-bottom:20px;
		}
		
		.pane-box {
			overflow: auto;
			
			.setup-line-box{
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin:30px 50px 10px 30px;
				
				.setup-btn{
					background-color: #00aaff;
					border-radius: 40px;
					width:100px;
					color:white;
					padding:8px 20px;
					margin:5px 10px;
					cursor:pointer;
					font-size: 14px;
				}
				.setup-btn:hover{
					background-color: #008bd0;
				}
				.setup-btn:active{
					transform: scale(0.98);
					
				}
			}
			
			.methd-panel {
				 display: flex;
				 justify-content: space-between;
				 align-items: center;
				 padding:5px 20px 20px 20px;
				 border-bottom: 1px dotted #ececec;
				 
				 .label-title {
					 font-size: 15px;
				 }
				 
				.file-btn {
					display: flex;
					justify-content: space-around;
					align-items: center;
					margin-left: 30px;
					font-size: 13px;
					font-weight: 300;
					cursor: pointer;
					padding:5px 10px;
				
					.file-label {
						margin-left: 3px;
					}
				}
				.file-btn:hover{
					color: #00aaff;
					transform: scale(1.1);
				}
				.file-btn:active{
					transform: scale(0.96);
				}
			}
			.methd-list{
				overflow-y: auto;
			}
		}
	}
</style>