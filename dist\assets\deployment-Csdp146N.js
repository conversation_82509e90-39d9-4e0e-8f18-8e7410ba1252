import{E as r}from"./el-button-B8TCQS4u.js";import{e,c as I,a as g,b as s,f as E,v as i,a5 as w,F as n,C as a,h as K,o,k as l,t as V}from"./index-Dgb0NZ1J.js";import{_ as W}from"./plugin-vue_export-helper-DlAUqK2U.js";const y="/assets/xmfb_icon_weixinxcx-C61cFUX2.png",M="/assets/xmfb_icon_android-C1Mn4Y8m.png",m="/assets/xmfb_icon_ios-CFWNCSNo.png",J="data:image/png;base64,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",Y={class:"deployment_container"},x={class:"flex_box head"},q={class:"back"},d={class:"platform_list"},H=["src"],R={class:"name"},S={__name:"deployment",setup(Z){const Q=K(),t=e([{label:"微信小程序",icon:y},{label:"Android/HMS",icon:M},{label:"IOS应用",icon:m},{label:"后台服务部署（含H5应用）",icon:J}]),c=()=>{Q.go(-1)};return(X,A)=>{const B=r;return o(),I("div",Y,[g("div",x,[g("div",q,[s(B,{style:{"font-size":"16px",color:"#212121"},icon:i(w),link:"",onClick:c},{default:E(()=>A[0]||(A[0]=[l(" 退出 ")])),_:1,__:[0]},8,["icon"]),A[1]||(A[1]=g("span",{class:"title"},"布署",-1))])]),g("div",d,[(o(!0),I(n,null,a(t.value,C=>(o(),I("div",{key:C.label,class:"platform_item"},[g("img",{src:C.icon},null,8,H),g("div",R,V(C.label),1),s(B,{color:"#000"},{default:E(()=>A[2]||(A[2]=[l("打包小程序")])),_:1,__:[2]}),s(B,{color:"#000"},{default:E(()=>A[3]||(A[3]=[l("参数配置")])),_:1,__:[3]})]))),128))])])}}},u=W(S,[["__scopeId","data-v-05386040"]]);export{u as default};
