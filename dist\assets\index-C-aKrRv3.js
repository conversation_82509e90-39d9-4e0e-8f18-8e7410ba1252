import{aB as Pe,O as ge,af as Q,H as Ue,I as We,J as G,ab as be,Y as Ye,M as p,aR as Xe,au as _e,_ as Qe,K as Te,aQ as Ge,T as Je,N as xe,aC as J,e as Z,c7 as Ze,ct as qe,cu as et,X as tt,bz as ot,ag as q,B as at,a2 as R,bh as nt,c as b,o as c,D as f,F as ee,a as T,n as m,v as t,Q as D,g as S,f as B,R as K,y as O,aE as we,b as st,bn as lt,a8 as rt,bs as it,t as j,j as Se,P as Ce,S as ut}from"./index-Dgb0NZ1J.js";import{U as te,u as dt,a as ct,e as pt,I as Ee,C as Ie,d as ze}from"./index-CINbulG0.js";import{u as ft,g as vt,e as mt,b as ht}from"./el-button-B8TCQS4u.js";const yt=()=>Pe&&/firefox/i.test(window.navigator.userAgent);let g;const gt={height:"0",visibility:"hidden",overflow:yt()?"":"hidden",position:"absolute","z-index":"-1000",top:"0",right:"0"},bt=["letter-spacing","line-height","padding-top","padding-bottom","font-family","font-weight","font-size","text-rendering","text-transform","width","text-indent","padding-left","padding-right","border-width","box-sizing"];function xt(a){const v=window.getComputedStyle(a),i=v.getPropertyValue("box-sizing"),o=Number.parseFloat(v.getPropertyValue("padding-bottom"))+Number.parseFloat(v.getPropertyValue("padding-top")),r=Number.parseFloat(v.getPropertyValue("border-bottom-width"))+Number.parseFloat(v.getPropertyValue("border-top-width"));return{contextStyle:bt.map(l=>[l,v.getPropertyValue(l)]),paddingSize:o,borderSize:r,boxSizing:i}}function Ne(a,v=1,i){var o;g||(g=document.createElement("textarea"),document.body.appendChild(g));const{paddingSize:r,borderSize:u,boxSizing:l,contextStyle:x}=xt(a);x.forEach(([y,F])=>g==null?void 0:g.style.setProperty(y,F)),Object.entries(gt).forEach(([y,F])=>g==null?void 0:g.style.setProperty(y,F,"important")),g.value=a.value||a.placeholder||"";let d=g.scrollHeight;const C={};l==="border-box"?d=d+u:l==="content-box"&&(d=d-r),g.value="";const h=g.scrollHeight-r;if(ge(v)){let y=h*v;l==="border-box"&&(y=y+r+u),d=Math.max(y,d),C.minHeight=`${y}px`}if(ge(i)){let y=h*i;l==="border-box"&&(y=y+r+u),d=Math.min(y,d)}return C.height=`${d}px`,(o=g.parentNode)==null||o.removeChild(g),g=void 0,C}const wt=Ue({id:{type:String,default:void 0},size:Ye,disabled:Boolean,modelValue:{type:G([String,Number,Object]),default:""},maxlength:{type:[String,Number]},minlength:{type:[String,Number]},type:{type:String,default:"text"},resize:{type:String,values:["none","both","horizontal","vertical"]},autosize:{type:G([Boolean,Object]),default:!1},autocomplete:{type:String,default:"off"},formatter:{type:Function},parser:{type:Function},placeholder:{type:String},form:{type:String},readonly:Boolean,clearable:Boolean,showPassword:Boolean,showWordLimit:Boolean,suffixIcon:{type:be},prefixIcon:{type:be},containerRole:{type:String,default:void 0},tabindex:{type:[String,Number],default:0},validateEvent:{type:Boolean,default:!0},inputStyle:{type:G([Object,Array,String]),default:()=>We({})},autofocus:Boolean,rows:{type:Number,default:2},...dt(["ariaLabel"])}),St={[te]:a=>Q(a),input:a=>Q(a),change:a=>Q(a),focus:a=>a instanceof FocusEvent,blur:a=>a instanceof FocusEvent,clear:()=>!0,mouseleave:a=>a instanceof MouseEvent,mouseenter:a=>a instanceof MouseEvent,keydown:a=>a instanceof Event,compositionstart:a=>a instanceof CompositionEvent,compositionupdate:a=>a instanceof CompositionEvent,compositionend:a=>a instanceof CompositionEvent},Ct=["class","style"],Et=/^on[A-Z]/,It=(a={})=>{const{excludeListeners:v=!1,excludeKeys:i}=a,o=p(()=>((i==null?void 0:i.value)||[]).concat(Ct)),r=_e();return r?p(()=>{var u;return Xe(Object.entries((u=r.proxy)==null?void 0:u.$attrs).filter(([l])=>!o.value.includes(l)&&!(v&&Et.test(l))))}):p(()=>({}))};function zt(a){let v;function i(){if(a.value==null)return;const{selectionStart:r,selectionEnd:u,value:l}=a.value;if(r==null||u==null)return;const x=l.slice(0,Math.max(0,r)),d=l.slice(Math.max(0,u));v={selectionStart:r,selectionEnd:u,value:l,beforeTxt:x,afterTxt:d}}function o(){if(a.value==null||v==null)return;const{value:r}=a.value,{beforeTxt:u,afterTxt:l,selectionStart:x}=v;if(u==null||l==null||x==null)return;let d=r.length;if(r.endsWith(l))d=r.length-l.length;else if(r.startsWith(u))d=u.length;else{const C=u[x-1],h=r.indexOf(C,x-1);h!==-1&&(d=h+1)}a.value.setSelectionRange(d,d)}return[i,o]}const Nt="ElInput",Pt=Te({name:Nt,inheritAttrs:!1}),Tt=Te({...Pt,props:wt,emits:St,setup(a,{expose:v,emit:i}){const o=a,r=Ge(),u=It(),l=Je(),x=p(()=>[o.type==="textarea"?oe.b():n.b(),n.m(F.value),n.is("disabled",E.value),n.is("exceed",Be.value),{[n.b("group")]:l.prepend||l.append,[n.m("prefix")]:l.prefix||o.prefixIcon,[n.m("suffix")]:l.suffix||o.suffixIcon||o.clearable||o.showPassword,[n.bm("suffix","password-clear")]:A.value&&Y.value,[n.b("hidden")]:o.type==="hidden"},r.class]),d=p(()=>[n.e("wrapper"),n.is("focus",W.value)]),{form:C,formItem:h}=mt(),{inputId:y}=ht(o,{formItemContext:h}),F=ft(),E=vt(),n=xe("input"),oe=xe("textarea"),L=J(),w=J(),U=Z(!1),H=Z(!1),ae=Z(),$=J(o.inputStyle),z=p(()=>L.value||w.value),{wrapperRef:Fe,isFocused:W,handleFocus:Ve,handleBlur:ke}=ct(z,{beforeFocus(){return E.value},afterBlur(){var e;o.validateEvent&&((e=h==null?void 0:h.validate)==null||e.call(h,"blur").catch(s=>ze()))}}),ne=p(()=>{var e;return(e=C==null?void 0:C.statusIcon)!=null?e:!1}),V=p(()=>(h==null?void 0:h.validateState)||""),se=p(()=>V.value&&Ze[V.value]),Me=p(()=>H.value?qe:et),Re=p(()=>[r.style]),le=p(()=>[o.inputStyle,$.value,{resize:o.resize}]),I=p(()=>tt(o.modelValue)?"":String(o.modelValue)),A=p(()=>o.clearable&&!E.value&&!o.readonly&&!!I.value&&(W.value||U.value)),Y=p(()=>o.showPassword&&!E.value&&!!I.value),N=p(()=>o.showWordLimit&&!!o.maxlength&&(o.type==="text"||o.type==="textarea")&&!E.value&&!o.readonly&&!o.showPassword),X=p(()=>I.value.length),Be=p(()=>!!N.value&&X.value>Number(o.maxlength)),Oe=p(()=>!!l.suffix||!!o.suffixIcon||A.value||o.showPassword||N.value||!!V.value&&ne.value),[re,ie]=zt(L);ot(w,e=>{if(Le(),!N.value||o.resize!=="both")return;const s=e[0],{width:P}=s.contentRect;ae.value={right:`calc(100% - ${P+15+6}px)`}});const k=()=>{const{type:e,autosize:s}=o;if(!(!Pe||e!=="textarea"||!w.value))if(s){const P=Ce(s)?s.minRows:void 0,he=Ce(s)?s.maxRows:void 0,ye=Ne(w.value,P,he);$.value={overflowY:"hidden",...ye},R(()=>{w.value.offsetHeight,$.value=ye})}else $.value={minHeight:Ne(w.value).minHeight}},Le=(e=>{let s=!1;return()=>{var P;if(s||!o.autosize)return;((P=w.value)==null?void 0:P.offsetParent)===null||(e(),s=!0)}})(k),M=()=>{const e=z.value,s=o.formatter?o.formatter(I.value):I.value;!e||e.value===s||(e.value=s)},_=async e=>{re();let{value:s}=e.target;if(o.formatter&&o.parser&&(s=o.parser(s)),!de.value){if(s===I.value){M();return}i(te,s),i(Ee,s),await R(),M(),ie()}},ue=e=>{let{value:s}=e.target;o.formatter&&o.parser&&(s=o.parser(s)),i(Ie,s)},{isComposing:de,handleCompositionStart:ce,handleCompositionUpdate:pe,handleCompositionEnd:fe}=pt({emit:i,afterComposition:_}),He=()=>{re(),H.value=!H.value,setTimeout(ie)},$e=()=>{var e;return(e=z.value)==null?void 0:e.focus()},Ae=()=>{var e;return(e=z.value)==null?void 0:e.blur()},De=e=>{U.value=!1,i("mouseleave",e)},Ke=e=>{U.value=!0,i("mouseenter",e)},ve=e=>{i("keydown",e)},je=()=>{var e;(e=z.value)==null||e.select()},me=()=>{i(te,""),i(Ie,""),i("clear"),i(Ee,"")};return q(()=>o.modelValue,()=>{var e;R(()=>k()),o.validateEvent&&((e=h==null?void 0:h.validate)==null||e.call(h,"change").catch(s=>ze()))}),q(I,()=>M()),q(()=>o.type,async()=>{await R(),M(),k()}),at(()=>{!o.formatter&&o.parser,M(),R(k)}),v({input:L,textarea:w,ref:z,textareaStyle:le,autosize:nt(o,"autosize"),isComposing:de,focus:$e,blur:Ae,select:je,clear:me,resizeTextarea:k}),(e,s)=>(c(),b("div",{class:m([t(x),{[t(n).bm("group","append")]:e.$slots.append,[t(n).bm("group","prepend")]:e.$slots.prepend}]),style:Se(t(Re)),onMouseenter:Ke,onMouseleave:De},[f(" input "),e.type!=="textarea"?(c(),b(ee,{key:0},[f(" prepend slot "),e.$slots.prepend?(c(),b("div",{key:0,class:m(t(n).be("group","prepend"))},[D(e.$slots,"prepend")],2)):f("v-if",!0),T("div",{ref_key:"wrapperRef",ref:Fe,class:m(t(d))},[f(" prefix slot "),e.$slots.prefix||e.prefixIcon?(c(),b("span",{key:0,class:m(t(n).e("prefix"))},[T("span",{class:m(t(n).e("prefix-inner"))},[D(e.$slots,"prefix"),e.prefixIcon?(c(),S(t(O),{key:0,class:m(t(n).e("icon"))},{default:B(()=>[(c(),S(K(e.prefixIcon)))]),_:1},8,["class"])):f("v-if",!0)],2)],2)):f("v-if",!0),T("input",we({id:t(y),ref_key:"input",ref:L,class:t(n).e("inner")},t(u),{minlength:e.minlength,maxlength:e.maxlength,type:e.showPassword?H.value?"text":"password":e.type,disabled:t(E),readonly:e.readonly,autocomplete:e.autocomplete,tabindex:e.tabindex,"aria-label":e.ariaLabel,placeholder:e.placeholder,style:e.inputStyle,form:e.form,autofocus:e.autofocus,role:e.containerRole,onCompositionstart:t(ce),onCompositionupdate:t(pe),onCompositionend:t(fe),onInput:_,onChange:ue,onKeydown:ve}),null,16,["id","minlength","maxlength","type","disabled","readonly","autocomplete","tabindex","aria-label","placeholder","form","autofocus","role","onCompositionstart","onCompositionupdate","onCompositionend"]),f(" suffix slot "),t(Oe)?(c(),b("span",{key:1,class:m(t(n).e("suffix"))},[T("span",{class:m(t(n).e("suffix-inner"))},[!t(A)||!t(Y)||!t(N)?(c(),b(ee,{key:0},[D(e.$slots,"suffix"),e.suffixIcon?(c(),S(t(O),{key:0,class:m(t(n).e("icon"))},{default:B(()=>[(c(),S(K(e.suffixIcon)))]),_:1},8,["class"])):f("v-if",!0)],64)):f("v-if",!0),t(A)?(c(),S(t(O),{key:1,class:m([t(n).e("icon"),t(n).e("clear")]),onMousedown:rt(t(it),["prevent"]),onClick:me},{default:B(()=>[st(t(lt))]),_:1},8,["class","onMousedown"])):f("v-if",!0),t(Y)?(c(),S(t(O),{key:2,class:m([t(n).e("icon"),t(n).e("password")]),onClick:He},{default:B(()=>[(c(),S(K(t(Me))))]),_:1},8,["class"])):f("v-if",!0),t(N)?(c(),b("span",{key:3,class:m(t(n).e("count"))},[T("span",{class:m(t(n).e("count-inner"))},j(t(X))+" / "+j(e.maxlength),3)],2)):f("v-if",!0),t(V)&&t(se)&&t(ne)?(c(),S(t(O),{key:4,class:m([t(n).e("icon"),t(n).e("validateIcon"),t(n).is("loading",t(V)==="validating")])},{default:B(()=>[(c(),S(K(t(se))))]),_:1},8,["class"])):f("v-if",!0)],2)],2)):f("v-if",!0)],2),f(" append slot "),e.$slots.append?(c(),b("div",{key:1,class:m(t(n).be("group","append"))},[D(e.$slots,"append")],2)):f("v-if",!0)],64)):(c(),b(ee,{key:1},[f(" textarea "),T("textarea",we({id:t(y),ref_key:"textarea",ref:w,class:[t(oe).e("inner"),t(n).is("focus",t(W))]},t(u),{minlength:e.minlength,maxlength:e.maxlength,tabindex:e.tabindex,disabled:t(E),readonly:e.readonly,autocomplete:e.autocomplete,style:t(le),"aria-label":e.ariaLabel,placeholder:e.placeholder,form:e.form,autofocus:e.autofocus,rows:e.rows,role:e.containerRole,onCompositionstart:t(ce),onCompositionupdate:t(pe),onCompositionend:t(fe),onInput:_,onFocus:t(Ve),onBlur:t(ke),onChange:ue,onKeydown:ve}),null,16,["id","minlength","maxlength","tabindex","disabled","readonly","autocomplete","aria-label","placeholder","form","autofocus","rows","role","onCompositionstart","onCompositionupdate","onCompositionend","onFocus","onBlur"]),t(N)?(c(),b("span",{key:0,style:Se(ae.value),class:m(t(n).e("count"))},j(t(X))+" / "+j(e.maxlength),7)):f("v-if",!0)],64))],38))}});var Ft=Qe(Tt,[["__file","input.vue"]]);const Bt=ut(Ft);export{Bt as E,It as u};
