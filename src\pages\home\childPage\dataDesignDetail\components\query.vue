<template>
  <div class="current_table">
    <div class="title">
      当前表：{{ dataTableValue.name }}--[{{ dataTableValue.fullname }}]
    </div>
    <div class="query_type">
      <div
        v-for="(item, index) in queryTypes"
        :key="index"
        :class="selectType === index ? 'query_type_active' : ''"
        @click="selectType = index"
      >
        {{ item }}
      </div>
    </div>
  </div>
  <div class="btn_control">
    <el-button v-if="selectType === 0" class="custom_btn" @click="handleAdd">
      <template #icon>
        <img class="btn_icon" src="@/assets/images/sjsj_icon_add.png" />
      </template>
      增加
    </el-button>
    <el-button v-if="selectType !== 2" class="custom_btn" @click="handleClear">
      <template #icon>
        <img class="btn_icon" src="@/assets/images/sjsj_icon_qingkong.png" />
      </template>
      清空
    </el-button>
    <el-button
      v-if="selectType === 0"
      class="custom_btn"
      @click="handleCreateSql(1)"
    >
      <template #icon>
        <img class="btn_icon" src="@/assets/images/sjsj_icon_daima.png" />
      </template>
      生成SQL
    </el-button>
    <el-button
      v-if="selectType !== 2"
      type="primary"
      @click="handleCreateSql(2)"
    >
      <template #icon>
        <img class="btn_icon" src="@/assets/images/sjsj_icon_chaxun.png" />
      </template>
      查询
    </el-button>
    <el-button
      v-if="selectType === 2"
      class="custom_btn"
      :disabled="filterFieldList.length === 0"
      @click="dialogVisible = true"
    >
      <template #icon>
        <img class="btn_icon" src="@/assets/images/sjsj_icon_add.png" />
      </template>
      新增
    </el-button>
    <el-button
      v-if="selectType === 2"
      class="custom_btn"
      @click="handleFetchData(true)"
    >
      <template #icon>
        <el-icon>
          <Refresh />
        </el-icon>
      </template>
      刷新
    </el-button>
  </div>
  <el-form
    :model="queryDataList"
    ref="formRef"
    :rules="tableRules"
    v-if="selectType === 0"
  >
    <div class="table">
      <el-row :gutter="12">
        <el-col :span="4">
          <div class="title">字段</div>
        </el-col>
        <el-col :span="4">
          <div class="title">符号</div>
        </el-col>
        <el-col :span="8">
          <div class="title">条件值</div>
        </el-col>
        <el-col :span="8">
          <div class="title">与或</div>
        </el-col>
      </el-row>
      <el-row
        :gutter="12"
        v-for="(item, index) in queryDataList"
        :key="item.id"
        style="margin-top: 20px"
      >
        <el-col :span="4">
          <el-form-item :prop="`${index}.colName`" :rules="tableRules.colName">
            <el-select v-model="item.colName" placeholder="请选择字段">
              <el-option
                v-for="item in fieldList"
                :key="item.id"
                :label="item.colname"
                :value="item.colname"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item :prop="`${index}.symbol`" :rules="tableRules.symbol">
            <el-select v-model="item.symbol" placeholder="请选择符号">
              <el-option label="> (大于)" value=">"></el-option>
              <el-option label="< (小于)" value="<"></el-option>
              <el-option label=">= (大于等于)" value=">="></el-option>
              <el-option label="<= (小于等于)" value="<="></el-option>
              <el-option label="= (等于)" value="="></el-option>
              <el-option label="in (存在..中)" value="in"></el-option>
              <el-option label="between (在..之间)" value="between"></el-option>
              <el-option label="like (像..)" value="like"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :prop="`${index}.value`" :rules="tableRules.value">
            <el-input v-model="item.value" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            v-if="index < queryDataList.length - 1"
            :prop="`${index}.logic`"
            :rules="tableRules.logic"
            style="flex-grow: 1"
          >
            <el-select v-model="item.logic" placeholder="请选择逻辑">
              <el-option label="and (与)" value="and"></el-option>
              <el-option label="or (或)" value="or"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="2">
          <el-popconfirm title="确认删除?" @confirm="handleDel(index)">
            <template #reference>
              <el-icon
                style="color: #666666; cursor: pointer; margin-top: 10px"
              >
                <CloseBold />
              </el-icon>
            </template>
          </el-popconfirm>
        </el-col>
      </el-row>
    </div>
  </el-form>
  <textarea
    v-else-if="selectType === 1"
    v-model="textareaValue"
    placeholder="请输入SQL语句"
  />
  <el-table
    v-else
    :data="queryList"
    border
    table-layout="auto"
    :header-cell-style="props.headerCellStyleData"
    :cell-style="props.cellStyleData"
    style="margin-top: 16px"
    :height="tableHeight"
  >
    <el-table-column
      v-for="item in reverseFieldList"
      :key="item.id"
      :prop="item.colname"
      :label="item.fullname"
    />
    <!-- <el-table-column prop="columns" label="名称" /> -->
    <el-table-column>
      <template #default="{ row }">
        <img
          v-if="filterFieldList.length > 0"
          src="@/assets/images/form_icon_bianji.png"
          class="results_btn"
          @click="handleUpdate(row)"
        />
        <el-popconfirm title="确认删除?" @confirm="handleDelete(row)">
          <template #reference>
            <img src="@/assets/images/form_icon_del.png" class="results_btn" />
          </template>
        </el-popconfirm>
      </template>
    </el-table-column>
  </el-table>
  <el-dialog
    v-model="dialogVisible"
    destroy-on-close
    width="1000"
    @close="handleResetForm"
  >
    <template #header>
      <div style="text-align: center; font-weight: 600; font-size: 18px">
        {{ formData._id ? "编辑数据" : "新增数据" }}
      </div>
    </template>
    <el-form
      ref="dialogFormRef"
      :model="formData"
      :rules="rules"
      label-width="160px"
      status-icon
      label-position="left"
      class="custom-form"
    >
      <el-form-item
        v-for="item in filterFieldList"
        :key="item.id"
        :prop="item.colname"
      >
        <template #label>
          <span :title="item.fullname" class="form-label-text">
            {{ item.fullname }}
          </span>
        </template>
        <!-- date 类型-->
        <el-date-picker
          v-if="dataTableValue.typeDb == 1 && item.type === 3"
          v-model="formData[item.colname]"
          type="datetime"
          placeholder="请选择"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 100%"
        />
        <!-- datetime 类型 -->
        <el-date-picker
          v-else-if="dataTableValue.typeDb == 1 && item.type === 4"
          v-model="formData[item.colname]"
          type="datetime"
          placeholder="系统自动创建"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 100%"
          disabled
        />
        <el-input v-else v-model="formData[item.colname]" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div style="display: flex; justify-content: center">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" v-debounce="handleSubmit">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { toRefs, ref, reactive, onMounted, computed } from "vue";
import { CloseBold, Refresh } from "@element-plus/icons-vue";
import { columnList, generateSql } from "@/apis/homeApi";
import { ElMessage } from "element-plus";

const props = defineProps({
  dataTableValue: {
    type: Object,
    default: {},
  },
  tableId: {
    type: String,
    default: "",
  },
  headerCellStyleData: {
    type: Object,
    default: {},
  },
  cellStyleData: {
    type: Object,
    default: {},
  },
});
const { dataTableValue } = toRefs(props);

const rules = ref({});
const tableRules = reactive({
  colName: [{ required: true, message: "请选择", trigger: "change" }],
  symbol: [{ required: true, message: "请选择", trigger: "change" }],
  value: [{ required: true, message: "请输入", trigger: "change" }],
  logic: [{ required: true, message: "请选择", trigger: "change" }],
});
const dialogFormRef = ref();
const queryTypes = ["条件查询", "SQL查询", "数据结果"];
const selectType = ref(0);
const textareaValue = ref("");
const queryDataList = ref([
  
]);
const dialogVisible = ref(false);
const formData = ref({});
const fieldList = ref([]);
const queryList = ref([]);
const formRef = ref(null);
const filterFieldList = computed(() => {
  // orderNo 小于等于3的是系统字段且 MongoDB datetime（系统自动创建）不可新增编辑
  return fieldList.value.filter((e) => e.orderNo > 3);
});
const reverseFieldList = computed(() => {
  return [...fieldList.value].reverse();
});
const options = computed(() => {
  return dataTableValue.value.typeDb === 0
    ? ["varchar", "char", "int", "bigint", "decimal", "timestamp", "text"]
    : ["string", "double", "long", "date", "datetime", "boolean"];
});

// 计算表格的响应式高度（适用于电脑屏幕）
const tableHeight = computed(() => {
  // 获取当前视窗高度
  const currentHeight = window.innerHeight;

  // 预留空间：页面头部、标题、按钮、查询条件等区域
  const reservedHeight = 380;

  // 计算可用高度
  const availableHeight = currentHeight - reservedHeight;

  // 根据常见电脑屏幕尺寸设置高度
  if (currentHeight <= 900) {
    // 较小的电脑屏幕 (如1366x768)
    return Math.max(350, Math.round(availableHeight * 1.1));
  } else if (currentHeight <= 1200) {
    // 常见电脑屏幕 (如1920x1080)
    return Math.max(450, Math.round(availableHeight * 1.1));
  } else {
    // 大尺寸电脑屏幕 (如2560x1440, 4K等)
    return Math.max(500, Math.min(800, Math.round(availableHeight * 1.1)));
  }
});
const handleUpdate = (e) => {
  formData.value = JSON.parse(JSON.stringify(e));
  dialogVisible.value = true;
};

const handleDelete = async (e) => {
  try {
    await generateSql({
      tableId: props.tableId,
      id: e._id,
      type: 3,
    });
    ElMessage({
      type: "success",
      message: "删除成功",
    });
    handleFetchData();
  } catch {}
};

const handleResetForm = () => {
  formData.value = {};
};

const handleFetchData = async (bool) => {
  try {
    const res = await generateSql({
      tableId: props.tableId,
      sql: textareaValue.value || `select * from ${props.dataTableValue.name}`,
      type: 4,
    });
    queryList.value = res.data.results;
    selectType.value = 2;
    if (bool) {
      ElMessage({
        type: "success",
        message: "刷新成功",
      });
    }
  } catch {}
};

const handleCreateSql = async (type) => {
  if (selectType.value === 0) {
    // 条件查询tab
    // if (queryDataList.value.length === 0) {
    //   return ElMessage({
    //     type: "error",
    //     message: "请设置查询条件",
    //   });
    // }
    try {
      await formRef.value.validate();
      const queryList = queryDataList.value.map((item, index) => {
        // 数值类型不需要加引号
        const numberTypes = ["int", "bigint", "decimal", "long", "double"];
        const fieldDetail = fieldList.value.find(
          (val) => val.colname === item.colName
        ); //当前字段
        const bool = numberTypes.includes(options.value[fieldDetail.type]);

        return ` (a.${item.colName} ${item.symbol} ${
          bool ? item.value : "'" + item.value + "'"
        }) ${index < queryDataList.value.length - 1 ? " " + item.logic : ""} `;
      });

      textareaValue.value =
        `select * from ${props.dataTableValue.name}` +
        (queryList.length > 0 ? " a where" + queryList.join("") : "");
      if (type === 2) {
        handleFetchData();
      }
      selectType.value = type;
    } catch {}
  } else if (selectType.value === 1) {
    //SQL查询tab
    if (!textareaValue.value) {
      return ElMessage({
        type: "error",
        message: "请输入查询SQL语句",
      });
    }
    handleFetchData();
  }
};

const handleSubmit = () => {
  dialogFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        console.log(formData.value);
        if (formData.value._id) {
          await generateSql({
            tableId: props.tableId,
            json: JSON.stringify(formData.value),
            type: 2,
            id: formData.value._id,
          });
        } else {
          if (dataTableValue.value.typeDb === 1) {
            // mogondb 类型 新增数据时，自动生成 datetime 字段
            const filed = filterFieldList.value.find((e) => e.type === 4);
            if (filed) {
              formData.value[filed.colname] = "";
            }
          }
          await generateSql({
            tableId: props.tableId,
            json: JSON.stringify(formData.value),
            type: 1,
          });
        }
        handleFetchData();
        ElMessage({
          type: "success",
          message: formData.value._id ? "编辑成功" : "创建成功",
        });
        dialogVisible.value = false;
      } catch (error) {
        console.log(error);
      }
    }
  });
};

const handleDel = (index) => {
  queryDataList.value.splice(index, 1);
};

const handleAdd = () => {
  queryDataList.value.push({
    id: Math.ceil(Math.random() * 10000),
    colName: "",
    symbol: "",
    value: "",
    logic: "",
  });
};

const handleClear = () => {
  if (selectType.value === 0) {
    queryDataList.value = [];
  } else {
    textareaValue.value = "";
  }
};

const fetchFieldList = async () => {
  try {
    const res = await columnList(props.tableId);
    fieldList.value = res.data.items;
    res.data.items.forEach((e) => {
      rules.value[e.colname] = [
        {
          required:
            dataTableValue.value.typeDb == 1 && e.type === 4 ? false : true, //datatime 类型自动生成
          message: `请输入${e.fullname}`,
          trigger: "change",
        },
      ];
    });
  } catch {}
};

onMounted(() => {
  fetchFieldList();
});
</script>

<style lang="scss" scoped>
// :deep(.el-dialog) {
//   padding: 0 !important;
// }
.custom_btn:hover {
  background-color: initial !important;
  border-color: #dcdfe6 !important;
  color: #606266 !important;
}

.current_table {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 0 16px 0;

  .title {
    font-weight: 600;
    font-size: 16px;
    color: #212121;
    line-height: 19px;
  }

  .query_type {
    box-sizing: border-box;
    padding: 0 2px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 229px;
    height: 32px;
    background: #f5f5f5;
    border-radius: 8px;

    div {
      width: 72px;
      height: 28px;
      border-radius: 6px;
      text-align: center;
      line-height: 28px;
      font-weight: 600;
      font-size: 14px;
      color: #999999;
      cursor: pointer;
    }

    .query_type_active {
      font-weight: 500;
      font-size: 14px;
      color: #212121;
      background: #ffffff;
    }
  }
}

.btn_control {
  .btn_icon {
    width: 20px;
    height: 20px;
  }
}

.table {
  margin-top: 16px;
  height: 528px;
  background: #ffffff;
  box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.06);
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  padding: 24px;

  .title {
    font-size: 14px;
    color: #999999;
    line-height: 16px;
  }
}

textarea {
  width: 100%;
  box-sizing: border-box;
  margin-top: 16px;
  height: calc(100vh - 340px);
  background: #fafafa;
  border-radius: 8px;
  padding: 24px;
  border: none;
  font-size: 16px;
  resize: none;
}

textarea:focus {
  outline: none;
}

.results_btn {
  width: 20px;
  height: 20px;
  margin-left: 13px;
  cursor: pointer;
}

/* 自定义表单label样式 - 支持省略号和悬停提示 */
.custom-form :deep(.el-form-item__label) {
  width: 170px !important;
  max-width: 170px !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}

/* 表单label文本样式 */
.form-label-text {
  width: 100%;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
