import { createRouter, createWebHashHistory } from "vue-router";

const routes = [
  {
    path: "/",
    redirect: "/home",
  },
  {
    path: "/login",
    name: "Login",
    redirect: "/login/index",
    component: () => import("@/pages/login/index.vue"),
    meta: { title: "登录" },
    children: [
      {
        path: "index",
        name: "Index",
        component: () => import("@/pages/login/childPage/login.vue"),
        meta: { title: "登录" },
      },
      {
        path: "resetPassword",
        name: "RestPassword",
        component: () => import("@/pages/login/childPage/resetPassword.vue"),
        meta: { title: "重置密码" },
      },
      {
        path: "register",
        name: "Register",
        component: () => import("@/pages/login/childPage/register.vue"),
        meta: { title: "注册" },
      },
    ],
  },
  {
    path: "/home",
    name: "Home",
    redirect: "/home/<USER>",
    component: () => import("@/pages/home/<USER>"),
    meta: { title: "首页" },
    children: [
      {
        path: "projectDesignList",
        name: "ProjectDesignList",
        component: () => import("@/pages/home/<USER>/projectDesignList.vue"),
        meta: { title: "项目设计" },
      },
      {
        path: "dataDesignList",
        name: "DataDesignList",
        component: () => import("@/pages/home/<USER>/dataDesignList.vue"),
        meta: { title: "数据设计" },
      },
      {
        path: "projectReleaseList",
        name: "ProjectReleaseList",
        component: () =>
          import("@/pages/home/<USER>/projectReleaseList.vue"),
        meta: { title: "项目发布" },
      },
      {
        path: "dataDesignDetail",
        name: "DataDesignDetail",
        component: () =>
          import("@/pages/home/<USER>/dataDesignDetail/index.vue"),
        meta: { title: "数据表管理" },
      },
      {
        path: "componentMarket",
        name: "ComponentMarket",
        component: () => import("@/pages/home/<USER>/componentMarket.vue"),
        meta: { title: "组件市场" },
      },
      {
        path: "deployment",
        name: "Deployment",
        component: () => import("@/pages/home/<USER>/deployment.vue"),
        meta: { title: "布署" },
      },
    ],
  },
  {
    path: "/designFrame",
    name: "DesignFrame",
    component: () => import("@/pages/designFrame/index.vue"),
    meta: { title: "页面设计" },
  },
  {
    path: "/demo",
    name: "Demo",
    component: () => import("@/pages/demo.vue"),
    meta: { title: "demo" },
  },
];

const router = createRouter({
  history: createWebHashHistory(),
  routes,
});

router.afterEach((to, from) => {
  document.title = to.meta.title;
});

export default router;
