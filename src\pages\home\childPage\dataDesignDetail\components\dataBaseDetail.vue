<template>
  <el-descriptions :column="1" label-width="110" style="margin: 20px 0">
    <el-descriptions-item label="ID">
      {{ databaseValue.id }}
    </el-descriptions-item>
    <el-descriptions-item label="数据库英文名">
      {{ databaseValue.name }}
    </el-descriptions-item>
    <el-descriptions-item label="库类型">
      {{ ["MySQL", "MongoDB"][databaseValue.type] }}
    </el-descriptions-item>
    <el-descriptions-item label="数据库中文名">
      {{ databaseValue.fullname }}
    </el-descriptions-item>
    <!-- <el-descriptions-item label="IP">
      {{
        databaseValue.dbChoose === 1
          ? "平台默认测试库"
          : databaseValue.dbChoose === 2
          ? "平台已购生产库"
          : databaseValue.IP
      }}
    </el-descriptions-item> -->
    <el-descriptions-item label="归属">
      {{ databaseValue.isSelf === 0 ? "直属" : "授权" }}
    </el-descriptions-item>
    <el-descriptions-item label="创建人">
      {{ databaseValue.creatorName }}
    </el-descriptions-item>
    <el-descriptions-item label="创建时间">
      {{ databaseValue.createTime }}
    </el-descriptions-item>
  </el-descriptions>
</template>

<script setup>
import { toRefs, } from "vue";

const props = defineProps({
  databaseValue: {
    type: Object,
    default: {},
  },
});

const { databaseValue } = toRefs(props);
</script>
