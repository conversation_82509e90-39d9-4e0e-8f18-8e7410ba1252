<template>
	<div class="event-box" @dragover.prevent="onDragover" @drop="onDrop">
		<div style="display: flex;justify-content: flex-start;align-items: center;">
			<div class="event-title" :style="{color: checkContent() ? '#212121' : '#ababab',width:'230px',textAlign:'left'}">
				{{props.eventItem.init.name}}
			</div>
			<el-select v-model="props.eventItem.data.used" placeholder="Select" style="width: 90px;" @change="onChange">
			  <el-option key="open" label="已启用" value="open" />
			  <el-option key="none" label="未启用" value="none" />
			</el-select>
		</div>
		<div class="event-content" v-if="props.eventItem.data.used=='open'">
			<input class="event-input" type="text" v-model = "props.eventItem.data.data.eventFile" 
			      :placeholder="'未设置事件方法，只接收'+props.eventItem.init.fileType+'文件'" />
			<div class="event-edit" @click.stop="onSetParam()" v-if="checkShowParam()">参数</div>
			<div class="event-edit" @click.stop="onEdit()">编辑</div>
		</div>
	</div>
	<el-dialog v-model="paramMgr.show" title="参数设置" width="800" >
		<div class="param-body">
			<div class="param-header">
				<div class="header-name">参数名</div>
				<div class="header-type">参数名称</div>
				<div class="header-value">参数值</div>
				<div class="oper-btn">
					<el-tooltip content="刷新" placement="top" effect="light" :show-after="500">
					<el-icon style="cursor:pointer;" :size="20" @click.stop="onReadParam()"> <Refresh /></el-icon>
					</el-tooltip>
  			    </div>
			</div>
			<div class="param-row" v-for="(param,index) in props.eventItem.data.data.params" :key="index" >
				<div class="row-name">
					<el-tooltip :content="param.remark" placement="top-start" effect="dark" :show-after="500">
						<div >{{param.name}}</div>
					</el-tooltip>
				</div>
				<div class="row-type">{{param.type}}</div>
				<div class="row-value">{{param.value}}</div>
				<div class="oper-btn" @click.stop="expressDialog.openShow(index)">
					<el-tooltip content="设置实际值" placement="top" effect="light" :show-after="500">
					<el-icon style="cursor:pointer;" :size="20"> <Paperclip /></el-icon>
					</el-tooltip>
				</div>
			</div>
		</div>
	</el-dialog>
	<ExpressionDialog
	  v-model="expressDialog.show"
	  :globalData = "expressDialog.globalData"
	  :pageData = "expressDialog.pageData"
	  :pageParams = "expressDialog.pageParams"
	  :textareaDefaultValue="expressDialog.initTxt"
	  @success="expressDialog.handleExpressionSuccess"
	/>
</template>

<script lang="ts" setup>
	import {reactive,inject, toRaw ,onMounted} from 'vue'
	import { ElMessage, ElMessageBox } from 'element-plus'
	import {apiLoadFileJson} from '../../../apis/designer.js'
	import {useExplainVar} from '../../../common/useExplainVar.js'
	
	const {parse} = useExplainVar()
	const props = defineProps({
		eventItem:{
			type:Object,
			default:{}
		},
		targetName:{
			type:String,
			default:""
		}
	})
	const ProjectRef = inject("ProjectRef")
	const FileScript = inject("FileScript")
	
	const expressDialog = reactive({
		show:false,
		initTxt:"",
		index:0,
		globalData:[],
		pageData:[],
		pageParams:[]
	})

	function checkContent(){
		if(props.eventItem.data.hasOwnProperty('used')){
			return props.eventItem.data.used=='open'
		}
		return false
	}
	
	function onChange(data){
		if(data=="open"){
			if(!props.eventItem.data.hasOwnProperty('data')){
				props.eventItem.data.data = JSON.parse(JSON.stringify(toRaw(props.eventItem.init)))
			}
		}else if(data=='none'){
			if(props.eventItem.data.hasOwnProperty('data')){
			   delete props.eventItem.data.data	
			}
		}
	}
	
	expressDialog.handleExpressionSuccess = (data)=>{
		paramMgr.params[expressDialog.index].value = data
		expressDialog.show = false
	}
	
	expressDialog.openShow = (index)=>{
		expressDialog.initTxt = props.eventItem.data.data.params[index].value
		expressDialog.index = index
		expressDialog.globalData = parse(ProjectRef.BasicData.globalData.varText,'global')
		expressDialog.pageData = parse(FileScript.instance.varText,"PageData")
		expressDialog.pageParams = FileScript.params
		expressDialog.show = true
	}
	
	const paramMgr = reactive({
		show:false,
		params:[]
	})
	
	function onDragover(e){
		e.preventDefault()
	}
	function checkShowParam(){
		if(props.eventItem.data.data.eventFile && props.eventItem.data.data.eventFile.length > 0){
			return true
		}
		return false
	}
	function onDrop(e){
		const res = e.dataTransfer.getData("treeItem")
		if(res){
			if(res.split(".")[0] == props.eventItem.data.data.fileType){
				props.eventItem.data.data.eventFile = res
			}else{
				ElMessage({type: 'warning',message: '文件类型错误，必须是 ['+props.eventItem.data.fileType+'] 类型文件',})
			}
		}
	}
	function onEdit(){
		if(props.eventItem.data.data && props.eventItem.data.data.eventFile.length > 0){
			let pathArray = props.eventItem.data.data.eventFile.split(".")
			if(props.eventItem.data.data.fileType=='methrod'){
				if(pathArray[0]!='methrod' || pathArray[pathArray.length - 1] !='mhd'){
					ElMessage({type: 'warning',message: '当前文件路径不是一个有效的函数路径！',})
					return
				}
			}else{
				if(pathArray[0]!='event' || pathArray[pathArray.length - 1] !='evt'){
					ElMessage({type: 'warning',message: '当前文件路径不是一个有效的事件路径！',})
					return
				}
			}
			// 使用通用的文件检查和打开函数
			if(ProjectRef.funcObj.otherFunc && ProjectRef.funcObj.otherFunc.checkAndOpenFileWithRefresh){
				ProjectRef.funcObj.otherFunc.checkAndOpenFileWithRefresh(
					props.eventItem.data.data.eventFile,
					props.eventItem.data.data.fileType,
					() => {
						// 文件不存在时的回调
						ElMessageBox.confirm('当前文件没有找到，是否新创建脚本文件？','提示',
						    {
						      confirmButtonText: '创建',
						      cancelButtonText: '放弃',
						      type: 'warning',
						    }
						  )
						    .then(() => {
						       ProjectRef.funcObj.createFile(props.eventItem.data.data.eventFile,(data)=>{
							   if(data.code==200){
								   ProjectRef.funcObj.sendLog("创建文件：" + props.eventItem.data.data.eventFile)
								   ProjectRef.funcObj.openFile(props.eventItem.data.data.eventFile)
							   }
						   })
						})
						.catch(() => {

						})
					}
				)
			}
		}else{
			let fileArray = FileScript.filePath.split(".")
			if((fileArray[0]=='page' && fileArray[fileArray.length - 1]=='vpg') 
			     || (fileArray[0]=='component' && fileArray[fileArray.length - 1]=='cpt')){
					 
				ElMessageBox.confirm('当前 [ '+props.eventItem.data.data.name+' ] 还未设置，是否现在创建?','提示',
				    {
				      confirmButtonText: '创建',
				      cancelButtonText: '放弃',
				      type: 'warning',
				    }
				  )
				    .then(() => {
						let fileName = fileArray[fileArray.length - 2]
						if(props.targetName != fileName){
							fileArray[fileArray.length - 1] = props.targetName
							fileArray.push(props.eventItem.data.data.eventName )
						}else{
							fileArray[fileArray.length - 1] = props.eventItem.data.data.eventName 
						}
				       if(props.eventItem.data.data.fileType=='methrod'){
							fileArray[0] = 'methrod'
							fileArray.push('mhd')
				       }else{
							fileArray[0] = 'event'
							fileArray.push('evt')
				       }
				       let newPath = fileArray.join(".")
				       ProjectRef.funcObj.createFile(newPath,(data)=>{
						   if(data.code==200){
							   ProjectRef.funcObj.openFile(newPath)
							   props.eventItem.data.data.eventFile = newPath
							   ProjectRef.funcObj.sendLog("创建文件：" + newPath)
						   }else if(data.code==6501){ //创建时文件已经存在了
							   props.eventItem.data.data.eventFile = newPath
							   if(!ProjectRef.funcObj.checkFileInList(newPath)){
								   let newArray = newPath.split(".")
									ProjectRef.funcObj.updateNewFile({
										fileId:0,
										title:newArray.slice(-2).join("."),
										type:newArray[0],
										activie:0,
										lock:0,
										lockUser:'',
										filePath:newPath
									})
							   }
							   ProjectRef.funcObj.openFile(newPath)
							   ProjectRef.funcObj.sendLog("设置已存在文件：" + newPath)
						   }
						   
						   
						  
					   })
				    })
				    .catch(() => {
				      
				    })	 

			}
		}
	}

	function onSetParam(){
		if(!props.eventItem.data.hasOwnProperty("data")){
			props.eventItem.data.data = {}
		}
		if(!props.eventItem.data.data.hasOwnProperty("params")){
		   props.eventItem.data.data.params = []
		}
		paramMgr.params = props.eventItem.data.data.params
		paramMgr.show = true
	}
	
	function onReadParam(){
		loadJsonFile(props.eventItem.data.data.eventFile,(data)=>{
			// props.eventItem.data.data.params = data
			if(data && data.length > 0){
				let tMap = new Map
				data.forEach(cItem=>{
					tMap.set(cItem.name,cItem)
				})
				let srcMap = new Map
				let indexs = []
				if(paramMgr.params.length > 0){
					paramMgr.params.forEach(kItem=>{
						srcMap.set(kItem.name,kItem)
						if(!tMap.has(kItem.name)){
						 indexs.push(paramMgr.params.indexOf(kItem))
						}
					})
				}
				if(indexs.length > 0){
					indexs.forEach(i=>{
						paramMgr.params.splice(i,1)
					})
				}
				data.forEach(item=>{
					if(!srcMap.has(item.name)){
						paramMgr.params.push(item)
					}
				})
			}else{
				paramMgr.params.splice(0,paramMgr.params.length)
			}
		})
	}
	
	async function loadJsonFile(path,success){
		let res = await apiLoadFileJson({
			projectId:ProjectRef.BasicData.ProjectId,
			path:path
		})
		if(res.code==200){
			let scptFile = JSON.parse(res.data.content) 
			if(success && scptFile && scptFile.paramList) success(scptFile.paramList)
		}
	}
</script>

<style lang="scss" scoped>
	.event-box {
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		border-bottom:1px solid #E6E6E6;
		padding-bottom:10px;
		padding-top:20px;
		min-width: 60px;
		white-space: nowrap;  
		padding-left:10px;
		
		.event-title {
			margin-bottom: 8px;
			font-weight: 600;
		}
		.event-content{
			width:calc(100% - 10px);
			display: flex;
			justify-content: space-between;
			align-items: center;
			// padding-right:10px;
			margin:6px 0px;
			
			.event-input {
				flex:1;
				background-color: #F7F7F7;
				color:#212121;
				border:0px;
				padding:10px 10px;
				font-size: 15px;
				margin-right:10px;
				border-radius: 6px;
			}
			.event-input:focus {
				outline: none;
				box-shadow: 0 0 0 2px transparent; 
			}
			.event-edit {
				border:1px solid #E6E6E6;
				border-radius: 6px;
				color:#212121;
				padding:6px 10px;
				font-size: 15px;
				margin-right: 5px;
				cursor:pointer;
			}
				
			.event-edit:hover{
				border:1px solid #d0d0d0;
			}
			.event-edit:active{
				border:1px solid #d0d0d0;
				font-size: 14px;
			}
		}
		
	}
	.param-body {
		margin: 5px 10px 10px 10px;
		// height:300px;
		overflow: auto;
		text-align: top;
		
		.oper-btn {
			width:80px;
			text-align: center;
		}
		
		.param-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			background-color: #e2e2e2;
			text-align: left;
			border:1px solid #cacaca;
			
			.header-name {
				flex:1;
				padding:0px 8px;
				height:30px;
				line-height: 30px;
				border-right:1px solid #cacaca;
			}
			.header-type{
				width:100px;
				height:30px;
				padding:0px 8px;
				line-height: 30px;
				border-right:1px solid #cacaca;
			}
			.header-value {
				flex:1;
				padding:0px 8px;
				height:30px;
				line-height: 30px;
				border-right:1px solid #cacaca;
			}
		}
		.param-row {
			display: flex;
			justify-content: space-between;
			align-items: center;
			text-align: left;
			border-left:1px solid #cacaca;
			border-right:1px solid #cacaca;
			border-bottom:1px solid #cacaca;
			.row-name{
				flex:1;
				padding:0px 8px;
				height:40px;
				line-height: 40px;
				border-right:1px solid #cacaca;
			}
			.row-type{
				width:100px;
				padding:0px 8px;
				height:40px;
				line-height: 40px;
				border-right:1px solid #cacaca;
			}
			.row-value {
				flex:1;
				padding:0px 8px;
				height:40px;
				line-height: 40px;
				border-right:1px solid #cacaca;
			}
		}
	}
</style>