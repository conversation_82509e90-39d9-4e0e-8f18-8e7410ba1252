import{ck as xo,H as K,v as d,O as se,_ as Z,K as I,L as ae,N as ie,e as x,M as P,bg as Pe,aK as et,bh as pe,g as Q,o as D,f as Y,w as rt,a as tt,a8 as _o,n as de,j as Ve,ah as qt,aL as Ut,aB as ye,c as we,b as me,F as Vt,J as M,V as At,ag as z,Z as Oe,d as Mo,cl as ko,B as He,a2 as xt,aY as Bo,D as Ie,Q as ee,R as Lo,P as Xt,bz as Io,S as Ye,bs as ot,cm as jo,cn as Fo,bP as Ho,aV as ke,aE as at,bt as Yt,X as Zt,aC as zo,aR as _t,aI as No,ai as Qe,co as Gt,au as Do,bu as Ke,aA as Jt,ad as $o,bf as Wo,bO as Ko,cp as qo,bC as Uo,br as Vo,bi as Xo,be as Yo,t as Zo}from"./index-Dgb0NZ1J.js";import{t as Go,u as st}from"./index-CINbulG0.js";import{i as Mt,E as Jo,t as Qo}from"./vnode-CV7pw3rS.js";import{a as kt,d as en,h as tn}from"./el-button-B8TCQS4u.js";function on(e){return e===void 0}function Bt(){let e;const t=(n,r)=>{o(),e=window.setTimeout(n,r)},o=()=>window.clearTimeout(e);return xo(()=>o()),{registerTimeout:t,cancelTimeout:o}}const nn=K({showAfter:{type:Number,default:0},hideAfter:{type:Number,default:200},autoClose:{type:Number,default:0}}),rn=({showAfter:e,hideAfter:t,autoClose:o,open:n,close:r})=>{const{registerTimeout:a}=Bt(),{registerTimeout:u,cancelTimeout:i}=Bt();return{onOpen:v=>{a(()=>{n(v);const f=d(o);se(f)&&f>0&&u(()=>{r(v)},f)},d(e))},onClose:v=>{i(),a(()=>{r(v)},d(t))}}},he=4,an={vertical:{offset:"offsetHeight",scroll:"scrollTop",scrollSize:"scrollHeight",size:"height",key:"vertical",axis:"Y",client:"clientY",direction:"top"},horizontal:{offset:"offsetWidth",scroll:"scrollLeft",scrollSize:"scrollWidth",size:"width",key:"horizontal",axis:"X",client:"clientX",direction:"left"}},sn=({move:e,size:t,bar:o})=>({[o.size]:t,transform:`translate${o.axis}(${e}%)`}),it=Symbol("scrollbarContextKey"),ln=K({vertical:Boolean,size:String,move:Number,ratio:{type:Number,required:!0},always:Boolean}),un="Thumb",cn=I({__name:"thumb",props:ln,setup(e){const t=e,o=ae(it),n=ie("scrollbar");o||Go(un,"can not inject scrollbar context");const r=x(),a=x(),u=x({}),i=x(!1);let s=!1,l=!1,v=0,f=ye?document.onselectstart:null;const c=P(()=>an[t.vertical?"vertical":"horizontal"]),g=P(()=>sn({size:t.size,move:t.move,bar:c.value})),b=P(()=>r.value[c.value.offset]**2/o.wrapElement[c.value.scrollSize]/t.ratio/a.value[c.value.offset]),m=y=>{var S;if(y.stopPropagation(),y.ctrlKey||[1,2].includes(y.button))return;(S=window.getSelection())==null||S.removeAllRanges(),O(y);const A=y.currentTarget;A&&(u.value[c.value.axis]=A[c.value.offset]-(y[c.value.client]-A.getBoundingClientRect()[c.value.direction]))},E=y=>{if(!a.value||!r.value||!o.wrapElement)return;const S=Math.abs(y.target.getBoundingClientRect()[c.value.direction]-y[c.value.client]),A=a.value[c.value.offset]/2,_=(S-A)*100*b.value/r.value[c.value.offset];o.wrapElement[c.value.scroll]=_*o.wrapElement[c.value.scrollSize]/100},O=y=>{y.stopImmediatePropagation(),s=!0,v=o.wrapElement.scrollHeight,document.addEventListener("mousemove",T),document.addEventListener("mouseup",p),f=document.onselectstart,document.onselectstart=()=>!1},T=y=>{if(!r.value||!a.value||s===!1)return;const S=u.value[c.value.axis];if(!S)return;const A=(r.value.getBoundingClientRect()[c.value.direction]-y[c.value.client])*-1,_=a.value[c.value.offset]-S,k=(A-_)*100*b.value/r.value[c.value.offset];o.wrapElement[c.value.scroll]=k*v/100},p=()=>{s=!1,u.value[c.value.axis]=0,document.removeEventListener("mousemove",T),document.removeEventListener("mouseup",p),h(),l&&(i.value=!1)},w=()=>{l=!1,i.value=!!t.size},R=()=>{l=!0,i.value=s};Pe(()=>{h(),document.removeEventListener("mouseup",p)});const h=()=>{document.onselectstart!==f&&(document.onselectstart=f)};return et(pe(o,"scrollbarElement"),"mousemove",w),et(pe(o,"scrollbarElement"),"mouseleave",R),(y,S)=>(D(),Q(Ut,{name:d(n).b("fade"),persisted:""},{default:Y(()=>[rt(tt("div",{ref_key:"instance",ref:r,class:de([d(n).e("bar"),d(n).is(d(c).key)]),onMousedown:E,onClick:_o(()=>{},["stop"])},[tt("div",{ref_key:"thumb",ref:a,class:de(d(n).e("thumb")),style:Ve(d(g)),onMousedown:m},null,38)],42,["onClick"]),[[qt,y.always||i.value]])]),_:1},8,["name"]))}});var Lt=Z(cn,[["__file","thumb.vue"]]);const fn=K({always:{type:Boolean,default:!0},minSize:{type:Number,required:!0}}),pn=I({__name:"bar",props:fn,setup(e,{expose:t}){const o=e,n=ae(it),r=x(0),a=x(0),u=x(""),i=x(""),s=x(1),l=x(1);return t({handleScroll:c=>{if(c){const g=c.offsetHeight-he,b=c.offsetWidth-he;a.value=c.scrollTop*100/g*s.value,r.value=c.scrollLeft*100/b*l.value}},update:()=>{const c=n==null?void 0:n.wrapElement;if(!c)return;const g=c.offsetHeight-he,b=c.offsetWidth-he,m=g**2/c.scrollHeight,E=b**2/c.scrollWidth,O=Math.max(m,o.minSize),T=Math.max(E,o.minSize);s.value=m/(g-m)/(O/(g-O)),l.value=E/(b-E)/(T/(b-T)),i.value=O+he<g?`${O}px`:"",u.value=T+he<b?`${T}px`:""}}),(c,g)=>(D(),we(Vt,null,[me(Lt,{move:r.value,ratio:l.value,size:u.value,always:c.always},null,8,["move","ratio","size","always"]),me(Lt,{move:a.value,ratio:s.value,size:i.value,vertical:"",always:c.always},null,8,["move","ratio","size","always"])],64))}});var dn=Z(pn,[["__file","bar.vue"]]);const vn=K({height:{type:[String,Number],default:""},maxHeight:{type:[String,Number],default:""},native:{type:Boolean,default:!1},wrapStyle:{type:M([String,Object,Array]),default:""},wrapClass:{type:[String,Array],default:""},viewClass:{type:[String,Array],default:""},viewStyle:{type:[String,Array,Object],default:""},noresize:Boolean,tag:{type:String,default:"div"},always:Boolean,minSize:{type:Number,default:20},tabindex:{type:[String,Number],default:void 0},id:String,role:String,...st(["ariaLabel","ariaOrientation"])}),mn={"end-reached":e=>["left","right","top","bottom"].includes(e),scroll:({scrollTop:e,scrollLeft:t})=>[e,t].every(se)},gn="ElScrollbar",hn=I({name:gn}),bn=I({...hn,props:vn,emits:mn,setup(e,{expose:t,emit:o}){const n=e,r=ie("scrollbar");let a,u,i=0,s=0,l="";const v=x(),f=x(),c=x(),g=x(),b=P(()=>{const h={};return n.height&&(h.height=At(n.height)),n.maxHeight&&(h.maxHeight=At(n.maxHeight)),[n.wrapStyle,h]}),m=P(()=>[n.wrapClass,r.e("wrap"),{[r.em("wrap","hidden-default")]:!n.native}]),E=P(()=>[r.e("view"),n.viewClass]),O=()=>{var h;if(f.value){(h=g.value)==null||h.handleScroll(f.value);const y=i,S=s;i=f.value.scrollTop,s=f.value.scrollLeft;const A={bottom:i+f.value.clientHeight>=f.value.scrollHeight,top:i<=0&&y!==0,right:s+f.value.clientWidth>=f.value.scrollWidth&&S!==s,left:s<=0&&S!==0};y!==i&&(l=i>y?"bottom":"top"),S!==s&&(l=s>S?"right":"left"),o("scroll",{scrollTop:i,scrollLeft:s}),A[l]&&o("end-reached",l)}};function T(h,y){Xt(h)?f.value.scrollTo(h):se(h)&&se(y)&&f.value.scrollTo(h,y)}const p=h=>{se(h)&&(f.value.scrollTop=h)},w=h=>{se(h)&&(f.value.scrollLeft=h)},R=()=>{var h;(h=g.value)==null||h.update()};return z(()=>n.noresize,h=>{h?(a==null||a(),u==null||u()):({stop:a}=Io(c,R),u=et("resize",R))},{immediate:!0}),z(()=>[n.maxHeight,n.height],()=>{n.native||xt(()=>{var h;R(),f.value&&((h=g.value)==null||h.handleScroll(f.value))})}),Oe(it,Mo({scrollbarElement:v,wrapElement:f})),ko(()=>{f.value&&(f.value.scrollTop=i,f.value.scrollLeft=s)}),He(()=>{n.native||xt(()=>{R()})}),Bo(()=>R()),t({wrapRef:f,update:R,scrollTo:T,setScrollTop:p,setScrollLeft:w,handleScroll:O}),(h,y)=>(D(),we("div",{ref_key:"scrollbarRef",ref:v,class:de(d(r).b())},[tt("div",{ref_key:"wrapRef",ref:f,class:de(d(m)),style:Ve(d(b)),tabindex:h.tabindex,onScroll:O},[(D(),Q(Lo(h.tag),{id:h.id,ref_key:"resizeRef",ref:c,class:de(d(E)),style:Ve(h.viewStyle),role:h.role,"aria-label":h.ariaLabel,"aria-orientation":h.ariaOrientation},{default:Y(()=>[ee(h.$slots,"default")]),_:3},8,["id","class","style","role","aria-label","aria-orientation"]))],46,["tabindex"]),h.native?Ie("v-if",!0):(D(),Q(dn,{key:0,ref_key:"barRef",ref:g,always:h.always,"min-size":h.minSize},null,8,["always","min-size"]))],2))}});var yn=Z(bn,[["__file","scrollbar.vue"]]);const Ra=Ye(yn),lt=Symbol("popper"),Qt=Symbol("popperContent"),wn=["dialog","grid","group","listbox","menu","navigation","tooltip","tree"],eo=K({role:{type:String,values:wn,default:"tooltip"}}),On=I({name:"ElPopper",inheritAttrs:!1}),En=I({...On,props:eo,setup(e,{expose:t}){const o=e,n=x(),r=x(),a=x(),u=x(),i=P(()=>o.role),s={triggerRef:n,popperInstanceRef:r,contentRef:a,referenceRef:u,role:i};return t(s),Oe(lt,s),(l,v)=>ee(l.$slots,"default")}});var Tn=Z(En,[["__file","popper.vue"]]);const Sn=I({name:"ElPopperArrow",inheritAttrs:!1}),Cn=I({...Sn,setup(e,{expose:t}){const o=ie("popper"),{arrowRef:n,arrowStyle:r}=ae(Qt,void 0);return Pe(()=>{n.value=void 0}),t({arrowRef:n}),(a,u)=>(D(),we("span",{ref_key:"arrowRef",ref:n,class:de(d(o).e("arrow")),style:Ve(d(r)),"data-popper-arrow":""},null,6))}});var Rn=Z(Cn,[["__file","arrow.vue"]]);const to=K({virtualRef:{type:M(Object)},virtualTriggering:Boolean,onMouseenter:{type:M(Function)},onMouseleave:{type:M(Function)},onClick:{type:M(Function)},onKeydown:{type:M(Function)},onFocus:{type:M(Function)},onBlur:{type:M(Function)},onContextmenu:{type:M(Function)},id:String,open:Boolean}),oo=Symbol("elForwardRef"),Pn=e=>{Oe(oo,{setForwardRef:o=>{e.value=o}})},An=e=>({mounted(t){e(t)},updated(t){e(t)},unmounted(){e(null)}}),xn="ElOnlyChild",_n=I({name:xn,setup(e,{slots:t,attrs:o}){var n;const r=ae(oo),a=An((n=r==null?void 0:r.setForwardRef)!=null?n:ot);return()=>{var u;const i=(u=t.default)==null?void 0:u.call(t,o);if(!i||i.length>1)return null;const s=no(i);return s?rt(jo(s,o),[[a]]):null}}});function no(e){if(!e)return null;const t=e;for(const o of t){if(Xt(o))switch(o.type){case Ho:continue;case Fo:case"svg":return It(o);case Vt:return no(o.children);default:return o}return It(o)}return null}function It(e){const t=ie("only-child");return me("span",{class:t.e("content")},[e])}const Mn=I({name:"ElPopperTrigger",inheritAttrs:!1}),kn=I({...Mn,props:to,setup(e,{expose:t}){const o=e,{role:n,triggerRef:r}=ae(lt,void 0);Pn(r);const a=P(()=>i.value?o.id:void 0),u=P(()=>{if(n&&n.value==="tooltip")return o.open&&o.id?o.id:void 0}),i=P(()=>{if(n&&n.value!=="tooltip")return n.value}),s=P(()=>i.value?`${o.open}`:void 0);let l;const v=["onMouseenter","onMouseleave","onClick","onKeydown","onFocus","onBlur","onContextmenu"];return He(()=>{z(()=>o.virtualRef,f=>{f&&(r.value=Yt(f))},{immediate:!0}),z(r,(f,c)=>{l==null||l(),l=void 0,ke(f)&&(v.forEach(g=>{var b;const m=o[g];m&&(f.addEventListener(g.slice(2).toLowerCase(),m),(b=c==null?void 0:c.removeEventListener)==null||b.call(c,g.slice(2).toLowerCase(),m))}),Mt(f)&&(l=z([a,u,i,s],g=>{["aria-controls","aria-describedby","aria-haspopup","aria-expanded"].forEach((b,m)=>{Zt(g[m])?f.removeAttribute(b):f.setAttribute(b,g[m])})},{immediate:!0}))),ke(c)&&Mt(c)&&["aria-controls","aria-describedby","aria-haspopup","aria-expanded"].forEach(g=>c.removeAttribute(g))},{immediate:!0})}),Pe(()=>{if(l==null||l(),l=void 0,r.value&&ke(r.value)){const f=r.value;v.forEach(c=>{const g=o[c];g&&f.removeEventListener(c.slice(2).toLowerCase(),g)}),r.value=void 0}}),t({triggerRef:r}),(f,c)=>f.virtualTriggering?Ie("v-if",!0):(D(),Q(d(_n),at({key:0},f.$attrs,{"aria-controls":d(a),"aria-describedby":d(u),"aria-expanded":d(s),"aria-haspopup":d(i)}),{default:Y(()=>[ee(f.$slots,"default")]),_:3},16,["aria-controls","aria-describedby","aria-expanded","aria-haspopup"]))}});var Bn=Z(kn,[["__file","trigger.vue"]]),$="top",U="bottom",V="right",W="left",ut="auto",ze=[$,U,V,W],Ee="start",je="end",Ln="clippingParents",ro="viewport",Me="popper",In="reference",jt=ze.reduce(function(e,t){return e.concat([t+"-"+Ee,t+"-"+je])},[]),ct=[].concat(ze,[ut]).reduce(function(e,t){return e.concat([t,t+"-"+Ee,t+"-"+je])},[]),jn="beforeRead",Fn="read",Hn="afterRead",zn="beforeMain",Nn="main",Dn="afterMain",$n="beforeWrite",Wn="write",Kn="afterWrite",qn=[jn,Fn,Hn,zn,Nn,Dn,$n,Wn,Kn];function te(e){return e?(e.nodeName||"").toLowerCase():null}function G(e){if(e==null)return window;if(e.toString()!=="[object Window]"){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function Te(e){var t=G(e).Element;return e instanceof t||e instanceof Element}function q(e){var t=G(e).HTMLElement;return e instanceof t||e instanceof HTMLElement}function ft(e){if(typeof ShadowRoot>"u")return!1;var t=G(e).ShadowRoot;return e instanceof t||e instanceof ShadowRoot}function Un(e){var t=e.state;Object.keys(t.elements).forEach(function(o){var n=t.styles[o]||{},r=t.attributes[o]||{},a=t.elements[o];!q(a)||!te(a)||(Object.assign(a.style,n),Object.keys(r).forEach(function(u){var i=r[u];i===!1?a.removeAttribute(u):a.setAttribute(u,i===!0?"":i)}))})}function Vn(e){var t=e.state,o={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,o.popper),t.styles=o,t.elements.arrow&&Object.assign(t.elements.arrow.style,o.arrow),function(){Object.keys(t.elements).forEach(function(n){var r=t.elements[n],a=t.attributes[n]||{},u=Object.keys(t.styles.hasOwnProperty(n)?t.styles[n]:o[n]),i=u.reduce(function(s,l){return s[l]="",s},{});!q(r)||!te(r)||(Object.assign(r.style,i),Object.keys(a).forEach(function(s){r.removeAttribute(s)}))})}}var ao={name:"applyStyles",enabled:!0,phase:"write",fn:Un,effect:Vn,requires:["computeStyles"]};function J(e){return e.split("-")[0]}var ve=Math.max,Xe=Math.min,Se=Math.round;function Ce(e,t){t===void 0&&(t=!1);var o=e.getBoundingClientRect(),n=1,r=1;if(q(e)&&t){var a=e.offsetHeight,u=e.offsetWidth;u>0&&(n=Se(o.width)/u||1),a>0&&(r=Se(o.height)/a||1)}return{width:o.width/n,height:o.height/r,top:o.top/r,right:o.right/n,bottom:o.bottom/r,left:o.left/n,x:o.left/n,y:o.top/r}}function pt(e){var t=Ce(e),o=e.offsetWidth,n=e.offsetHeight;return Math.abs(t.width-o)<=1&&(o=t.width),Math.abs(t.height-n)<=1&&(n=t.height),{x:e.offsetLeft,y:e.offsetTop,width:o,height:n}}function so(e,t){var o=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(o&&ft(o)){var n=t;do{if(n&&e.isSameNode(n))return!0;n=n.parentNode||n.host}while(n)}return!1}function re(e){return G(e).getComputedStyle(e)}function Xn(e){return["table","td","th"].indexOf(te(e))>=0}function le(e){return((Te(e)?e.ownerDocument:e.document)||window.document).documentElement}function Ze(e){return te(e)==="html"?e:e.assignedSlot||e.parentNode||(ft(e)?e.host:null)||le(e)}function Ft(e){return!q(e)||re(e).position==="fixed"?null:e.offsetParent}function Yn(e){var t=navigator.userAgent.toLowerCase().indexOf("firefox")!==-1,o=navigator.userAgent.indexOf("Trident")!==-1;if(o&&q(e)){var n=re(e);if(n.position==="fixed")return null}var r=Ze(e);for(ft(r)&&(r=r.host);q(r)&&["html","body"].indexOf(te(r))<0;){var a=re(r);if(a.transform!=="none"||a.perspective!=="none"||a.contain==="paint"||["transform","perspective"].indexOf(a.willChange)!==-1||t&&a.willChange==="filter"||t&&a.filter&&a.filter!=="none")return r;r=r.parentNode}return null}function Ne(e){for(var t=G(e),o=Ft(e);o&&Xn(o)&&re(o).position==="static";)o=Ft(o);return o&&(te(o)==="html"||te(o)==="body"&&re(o).position==="static")?t:o||Yn(e)||t}function dt(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function Be(e,t,o){return ve(e,Xe(t,o))}function Zn(e,t,o){var n=Be(e,t,o);return n>o?o:n}function io(){return{top:0,right:0,bottom:0,left:0}}function lo(e){return Object.assign({},io(),e)}function uo(e,t){return t.reduce(function(o,n){return o[n]=e,o},{})}var Gn=function(e,t){return e=typeof e=="function"?e(Object.assign({},t.rects,{placement:t.placement})):e,lo(typeof e!="number"?e:uo(e,ze))};function Jn(e){var t,o=e.state,n=e.name,r=e.options,a=o.elements.arrow,u=o.modifiersData.popperOffsets,i=J(o.placement),s=dt(i),l=[W,V].indexOf(i)>=0,v=l?"height":"width";if(!(!a||!u)){var f=Gn(r.padding,o),c=pt(a),g=s==="y"?$:W,b=s==="y"?U:V,m=o.rects.reference[v]+o.rects.reference[s]-u[s]-o.rects.popper[v],E=u[s]-o.rects.reference[s],O=Ne(a),T=O?s==="y"?O.clientHeight||0:O.clientWidth||0:0,p=m/2-E/2,w=f[g],R=T-c[v]-f[b],h=T/2-c[v]/2+p,y=Be(w,h,R),S=s;o.modifiersData[n]=(t={},t[S]=y,t.centerOffset=y-h,t)}}function Qn(e){var t=e.state,o=e.options,n=o.element,r=n===void 0?"[data-popper-arrow]":n;r!=null&&(typeof r=="string"&&(r=t.elements.popper.querySelector(r),!r)||!so(t.elements.popper,r)||(t.elements.arrow=r))}var er={name:"arrow",enabled:!0,phase:"main",fn:Jn,effect:Qn,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Re(e){return e.split("-")[1]}var tr={top:"auto",right:"auto",bottom:"auto",left:"auto"};function or(e){var t=e.x,o=e.y,n=window,r=n.devicePixelRatio||1;return{x:Se(t*r)/r||0,y:Se(o*r)/r||0}}function Ht(e){var t,o=e.popper,n=e.popperRect,r=e.placement,a=e.variation,u=e.offsets,i=e.position,s=e.gpuAcceleration,l=e.adaptive,v=e.roundOffsets,f=e.isFixed,c=u.x,g=c===void 0?0:c,b=u.y,m=b===void 0?0:b,E=typeof v=="function"?v({x:g,y:m}):{x:g,y:m};g=E.x,m=E.y;var O=u.hasOwnProperty("x"),T=u.hasOwnProperty("y"),p=W,w=$,R=window;if(l){var h=Ne(o),y="clientHeight",S="clientWidth";if(h===G(o)&&(h=le(o),re(h).position!=="static"&&i==="absolute"&&(y="scrollHeight",S="scrollWidth")),h=h,r===$||(r===W||r===V)&&a===je){w=U;var A=f&&h===R&&R.visualViewport?R.visualViewport.height:h[y];m-=A-n.height,m*=s?1:-1}if(r===W||(r===$||r===U)&&a===je){p=V;var _=f&&h===R&&R.visualViewport?R.visualViewport.width:h[S];g-=_-n.width,g*=s?1:-1}}var k=Object.assign({position:i},l&&tr),B=v===!0?or({x:g,y:m}):{x:g,y:m};if(g=B.x,m=B.y,s){var j;return Object.assign({},k,(j={},j[w]=T?"0":"",j[p]=O?"0":"",j.transform=(R.devicePixelRatio||1)<=1?"translate("+g+"px, "+m+"px)":"translate3d("+g+"px, "+m+"px, 0)",j))}return Object.assign({},k,(t={},t[w]=T?m+"px":"",t[p]=O?g+"px":"",t.transform="",t))}function nr(e){var t=e.state,o=e.options,n=o.gpuAcceleration,r=n===void 0?!0:n,a=o.adaptive,u=a===void 0?!0:a,i=o.roundOffsets,s=i===void 0?!0:i,l={placement:J(t.placement),variation:Re(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:r,isFixed:t.options.strategy==="fixed"};t.modifiersData.popperOffsets!=null&&(t.styles.popper=Object.assign({},t.styles.popper,Ht(Object.assign({},l,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:u,roundOffsets:s})))),t.modifiersData.arrow!=null&&(t.styles.arrow=Object.assign({},t.styles.arrow,Ht(Object.assign({},l,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:s})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})}var co={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:nr,data:{}},qe={passive:!0};function rr(e){var t=e.state,o=e.instance,n=e.options,r=n.scroll,a=r===void 0?!0:r,u=n.resize,i=u===void 0?!0:u,s=G(t.elements.popper),l=[].concat(t.scrollParents.reference,t.scrollParents.popper);return a&&l.forEach(function(v){v.addEventListener("scroll",o.update,qe)}),i&&s.addEventListener("resize",o.update,qe),function(){a&&l.forEach(function(v){v.removeEventListener("scroll",o.update,qe)}),i&&s.removeEventListener("resize",o.update,qe)}}var fo={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:rr,data:{}},ar={left:"right",right:"left",bottom:"top",top:"bottom"};function Ue(e){return e.replace(/left|right|bottom|top/g,function(t){return ar[t]})}var sr={start:"end",end:"start"};function zt(e){return e.replace(/start|end/g,function(t){return sr[t]})}function vt(e){var t=G(e),o=t.pageXOffset,n=t.pageYOffset;return{scrollLeft:o,scrollTop:n}}function mt(e){return Ce(le(e)).left+vt(e).scrollLeft}function ir(e){var t=G(e),o=le(e),n=t.visualViewport,r=o.clientWidth,a=o.clientHeight,u=0,i=0;return n&&(r=n.width,a=n.height,/^((?!chrome|android).)*safari/i.test(navigator.userAgent)||(u=n.offsetLeft,i=n.offsetTop)),{width:r,height:a,x:u+mt(e),y:i}}function lr(e){var t,o=le(e),n=vt(e),r=(t=e.ownerDocument)==null?void 0:t.body,a=ve(o.scrollWidth,o.clientWidth,r?r.scrollWidth:0,r?r.clientWidth:0),u=ve(o.scrollHeight,o.clientHeight,r?r.scrollHeight:0,r?r.clientHeight:0),i=-n.scrollLeft+mt(e),s=-n.scrollTop;return re(r||o).direction==="rtl"&&(i+=ve(o.clientWidth,r?r.clientWidth:0)-a),{width:a,height:u,x:i,y:s}}function gt(e){var t=re(e),o=t.overflow,n=t.overflowX,r=t.overflowY;return/auto|scroll|overlay|hidden/.test(o+r+n)}function po(e){return["html","body","#document"].indexOf(te(e))>=0?e.ownerDocument.body:q(e)&&gt(e)?e:po(Ze(e))}function Le(e,t){var o;t===void 0&&(t=[]);var n=po(e),r=n===((o=e.ownerDocument)==null?void 0:o.body),a=G(n),u=r?[a].concat(a.visualViewport||[],gt(n)?n:[]):n,i=t.concat(u);return r?i:i.concat(Le(Ze(u)))}function nt(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function ur(e){var t=Ce(e);return t.top=t.top+e.clientTop,t.left=t.left+e.clientLeft,t.bottom=t.top+e.clientHeight,t.right=t.left+e.clientWidth,t.width=e.clientWidth,t.height=e.clientHeight,t.x=t.left,t.y=t.top,t}function Nt(e,t){return t===ro?nt(ir(e)):Te(t)?ur(t):nt(lr(le(e)))}function cr(e){var t=Le(Ze(e)),o=["absolute","fixed"].indexOf(re(e).position)>=0,n=o&&q(e)?Ne(e):e;return Te(n)?t.filter(function(r){return Te(r)&&so(r,n)&&te(r)!=="body"}):[]}function fr(e,t,o){var n=t==="clippingParents"?cr(e):[].concat(t),r=[].concat(n,[o]),a=r[0],u=r.reduce(function(i,s){var l=Nt(e,s);return i.top=ve(l.top,i.top),i.right=Xe(l.right,i.right),i.bottom=Xe(l.bottom,i.bottom),i.left=ve(l.left,i.left),i},Nt(e,a));return u.width=u.right-u.left,u.height=u.bottom-u.top,u.x=u.left,u.y=u.top,u}function vo(e){var t=e.reference,o=e.element,n=e.placement,r=n?J(n):null,a=n?Re(n):null,u=t.x+t.width/2-o.width/2,i=t.y+t.height/2-o.height/2,s;switch(r){case $:s={x:u,y:t.y-o.height};break;case U:s={x:u,y:t.y+t.height};break;case V:s={x:t.x+t.width,y:i};break;case W:s={x:t.x-o.width,y:i};break;default:s={x:t.x,y:t.y}}var l=r?dt(r):null;if(l!=null){var v=l==="y"?"height":"width";switch(a){case Ee:s[l]=s[l]-(t[v]/2-o[v]/2);break;case je:s[l]=s[l]+(t[v]/2-o[v]/2);break}}return s}function Fe(e,t){t===void 0&&(t={});var o=t,n=o.placement,r=n===void 0?e.placement:n,a=o.boundary,u=a===void 0?Ln:a,i=o.rootBoundary,s=i===void 0?ro:i,l=o.elementContext,v=l===void 0?Me:l,f=o.altBoundary,c=f===void 0?!1:f,g=o.padding,b=g===void 0?0:g,m=lo(typeof b!="number"?b:uo(b,ze)),E=v===Me?In:Me,O=e.rects.popper,T=e.elements[c?E:v],p=fr(Te(T)?T:T.contextElement||le(e.elements.popper),u,s),w=Ce(e.elements.reference),R=vo({reference:w,element:O,placement:r}),h=nt(Object.assign({},O,R)),y=v===Me?h:w,S={top:p.top-y.top+m.top,bottom:y.bottom-p.bottom+m.bottom,left:p.left-y.left+m.left,right:y.right-p.right+m.right},A=e.modifiersData.offset;if(v===Me&&A){var _=A[r];Object.keys(S).forEach(function(k){var B=[V,U].indexOf(k)>=0?1:-1,j=[$,U].indexOf(k)>=0?"y":"x";S[k]+=_[j]*B})}return S}function pr(e,t){t===void 0&&(t={});var o=t,n=o.placement,r=o.boundary,a=o.rootBoundary,u=o.padding,i=o.flipVariations,s=o.allowedAutoPlacements,l=s===void 0?ct:s,v=Re(n),f=v?i?jt:jt.filter(function(b){return Re(b)===v}):ze,c=f.filter(function(b){return l.indexOf(b)>=0});c.length===0&&(c=f);var g=c.reduce(function(b,m){return b[m]=Fe(e,{placement:m,boundary:r,rootBoundary:a,padding:u})[J(m)],b},{});return Object.keys(g).sort(function(b,m){return g[b]-g[m]})}function dr(e){if(J(e)===ut)return[];var t=Ue(e);return[zt(e),t,zt(t)]}function vr(e){var t=e.state,o=e.options,n=e.name;if(!t.modifiersData[n]._skip){for(var r=o.mainAxis,a=r===void 0?!0:r,u=o.altAxis,i=u===void 0?!0:u,s=o.fallbackPlacements,l=o.padding,v=o.boundary,f=o.rootBoundary,c=o.altBoundary,g=o.flipVariations,b=g===void 0?!0:g,m=o.allowedAutoPlacements,E=t.options.placement,O=J(E),T=O===E,p=s||(T||!b?[Ue(E)]:dr(E)),w=[E].concat(p).reduce(function(ce,oe){return ce.concat(J(oe)===ut?pr(t,{placement:oe,boundary:v,rootBoundary:f,padding:l,flipVariations:b,allowedAutoPlacements:m}):oe)},[]),R=t.rects.reference,h=t.rects.popper,y=new Map,S=!0,A=w[0],_=0;_<w.length;_++){var k=w[_],B=J(k),j=Re(k)===Ee,N=[$,U].indexOf(B)>=0,X=N?"width":"height",L=Fe(t,{placement:k,boundary:v,rootBoundary:f,altBoundary:c,padding:l}),H=N?j?V:W:j?U:$;R[X]>h[X]&&(H=Ue(H));var C=Ue(H),F=[];if(a&&F.push(L[B]<=0),i&&F.push(L[H]<=0,L[C]<=0),F.every(function(ce){return ce})){A=k,S=!1;break}y.set(k,F)}if(S)for(var ue=b?3:1,Ae=function(ce){var oe=w.find(function($e){var _e=y.get($e);if(_e)return _e.slice(0,ce).every(function(ge){return ge})});if(oe)return A=oe,"break"},xe=ue;xe>0;xe--){var De=Ae(xe);if(De==="break")break}t.placement!==A&&(t.modifiersData[n]._skip=!0,t.placement=A,t.reset=!0)}}var mr={name:"flip",enabled:!0,phase:"main",fn:vr,requiresIfExists:["offset"],data:{_skip:!1}};function Dt(e,t,o){return o===void 0&&(o={x:0,y:0}),{top:e.top-t.height-o.y,right:e.right-t.width+o.x,bottom:e.bottom-t.height+o.y,left:e.left-t.width-o.x}}function $t(e){return[$,V,U,W].some(function(t){return e[t]>=0})}function gr(e){var t=e.state,o=e.name,n=t.rects.reference,r=t.rects.popper,a=t.modifiersData.preventOverflow,u=Fe(t,{elementContext:"reference"}),i=Fe(t,{altBoundary:!0}),s=Dt(u,n),l=Dt(i,r,a),v=$t(s),f=$t(l);t.modifiersData[o]={referenceClippingOffsets:s,popperEscapeOffsets:l,isReferenceHidden:v,hasPopperEscaped:f},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":v,"data-popper-escaped":f})}var hr={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:gr};function br(e,t,o){var n=J(e),r=[W,$].indexOf(n)>=0?-1:1,a=typeof o=="function"?o(Object.assign({},t,{placement:e})):o,u=a[0],i=a[1];return u=u||0,i=(i||0)*r,[W,V].indexOf(n)>=0?{x:i,y:u}:{x:u,y:i}}function yr(e){var t=e.state,o=e.options,n=e.name,r=o.offset,a=r===void 0?[0,0]:r,u=ct.reduce(function(v,f){return v[f]=br(f,t.rects,a),v},{}),i=u[t.placement],s=i.x,l=i.y;t.modifiersData.popperOffsets!=null&&(t.modifiersData.popperOffsets.x+=s,t.modifiersData.popperOffsets.y+=l),t.modifiersData[n]=u}var wr={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:yr};function Or(e){var t=e.state,o=e.name;t.modifiersData[o]=vo({reference:t.rects.reference,element:t.rects.popper,placement:t.placement})}var mo={name:"popperOffsets",enabled:!0,phase:"read",fn:Or,data:{}};function Er(e){return e==="x"?"y":"x"}function Tr(e){var t=e.state,o=e.options,n=e.name,r=o.mainAxis,a=r===void 0?!0:r,u=o.altAxis,i=u===void 0?!1:u,s=o.boundary,l=o.rootBoundary,v=o.altBoundary,f=o.padding,c=o.tether,g=c===void 0?!0:c,b=o.tetherOffset,m=b===void 0?0:b,E=Fe(t,{boundary:s,rootBoundary:l,padding:f,altBoundary:v}),O=J(t.placement),T=Re(t.placement),p=!T,w=dt(O),R=Er(w),h=t.modifiersData.popperOffsets,y=t.rects.reference,S=t.rects.popper,A=typeof m=="function"?m(Object.assign({},t.rects,{placement:t.placement})):m,_=typeof A=="number"?{mainAxis:A,altAxis:A}:Object.assign({mainAxis:0,altAxis:0},A),k=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,B={x:0,y:0};if(h){if(a){var j,N=w==="y"?$:W,X=w==="y"?U:V,L=w==="y"?"height":"width",H=h[w],C=H+E[N],F=H-E[X],ue=g?-S[L]/2:0,Ae=T===Ee?y[L]:S[L],xe=T===Ee?-S[L]:-y[L],De=t.elements.arrow,ce=g&&De?pt(De):{width:0,height:0},oe=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:io(),$e=oe[N],_e=oe[X],ge=Be(0,y[L],ce[L]),Eo=p?y[L]/2-ue-ge-$e-_.mainAxis:Ae-ge-$e-_.mainAxis,To=p?-y[L]/2+ue+ge+_e+_.mainAxis:xe+ge+_e+_.mainAxis,Ge=t.elements.arrow&&Ne(t.elements.arrow),So=Ge?w==="y"?Ge.clientTop||0:Ge.clientLeft||0:0,yt=(j=k==null?void 0:k[w])!=null?j:0,Co=H+Eo-yt-So,Ro=H+To-yt,wt=Be(g?Xe(C,Co):C,H,g?ve(F,Ro):F);h[w]=wt,B[w]=wt-H}if(i){var Ot,Po=w==="x"?$:W,Ao=w==="x"?U:V,fe=h[R],We=R==="y"?"height":"width",Et=fe+E[Po],Tt=fe-E[Ao],Je=[$,W].indexOf(O)!==-1,St=(Ot=k==null?void 0:k[R])!=null?Ot:0,Ct=Je?Et:fe-y[We]-S[We]-St+_.altAxis,Rt=Je?fe+y[We]+S[We]-St-_.altAxis:Tt,Pt=g&&Je?Zn(Ct,fe,Rt):Be(g?Ct:Et,fe,g?Rt:Tt);h[R]=Pt,B[R]=Pt-fe}t.modifiersData[n]=B}}var Sr={name:"preventOverflow",enabled:!0,phase:"main",fn:Tr,requiresIfExists:["offset"]};function Cr(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}function Rr(e){return e===G(e)||!q(e)?vt(e):Cr(e)}function Pr(e){var t=e.getBoundingClientRect(),o=Se(t.width)/e.offsetWidth||1,n=Se(t.height)/e.offsetHeight||1;return o!==1||n!==1}function Ar(e,t,o){o===void 0&&(o=!1);var n=q(t),r=q(t)&&Pr(t),a=le(t),u=Ce(e,r),i={scrollLeft:0,scrollTop:0},s={x:0,y:0};return(n||!n&&!o)&&((te(t)!=="body"||gt(a))&&(i=Rr(t)),q(t)?(s=Ce(t,!0),s.x+=t.clientLeft,s.y+=t.clientTop):a&&(s.x=mt(a))),{x:u.left+i.scrollLeft-s.x,y:u.top+i.scrollTop-s.y,width:u.width,height:u.height}}function xr(e){var t=new Map,o=new Set,n=[];e.forEach(function(a){t.set(a.name,a)});function r(a){o.add(a.name);var u=[].concat(a.requires||[],a.requiresIfExists||[]);u.forEach(function(i){if(!o.has(i)){var s=t.get(i);s&&r(s)}}),n.push(a)}return e.forEach(function(a){o.has(a.name)||r(a)}),n}function _r(e){var t=xr(e);return qn.reduce(function(o,n){return o.concat(t.filter(function(r){return r.phase===n}))},[])}function Mr(e){var t;return function(){return t||(t=new Promise(function(o){Promise.resolve().then(function(){t=void 0,o(e())})})),t}}function kr(e){var t=e.reduce(function(o,n){var r=o[n.name];return o[n.name]=r?Object.assign({},r,n,{options:Object.assign({},r.options,n.options),data:Object.assign({},r.data,n.data)}):n,o},{});return Object.keys(t).map(function(o){return t[o]})}var Wt={placement:"bottom",modifiers:[],strategy:"absolute"};function Kt(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return!t.some(function(n){return!(n&&typeof n.getBoundingClientRect=="function")})}function ht(e){e===void 0&&(e={});var t=e,o=t.defaultModifiers,n=o===void 0?[]:o,r=t.defaultOptions,a=r===void 0?Wt:r;return function(u,i,s){s===void 0&&(s=a);var l={placement:"bottom",orderedModifiers:[],options:Object.assign({},Wt,a),modifiersData:{},elements:{reference:u,popper:i},attributes:{},styles:{}},v=[],f=!1,c={state:l,setOptions:function(m){var E=typeof m=="function"?m(l.options):m;b(),l.options=Object.assign({},a,l.options,E),l.scrollParents={reference:Te(u)?Le(u):u.contextElement?Le(u.contextElement):[],popper:Le(i)};var O=_r(kr([].concat(n,l.options.modifiers)));return l.orderedModifiers=O.filter(function(T){return T.enabled}),g(),c.update()},forceUpdate:function(){if(!f){var m=l.elements,E=m.reference,O=m.popper;if(Kt(E,O)){l.rects={reference:Ar(E,Ne(O),l.options.strategy==="fixed"),popper:pt(O)},l.reset=!1,l.placement=l.options.placement,l.orderedModifiers.forEach(function(S){return l.modifiersData[S.name]=Object.assign({},S.data)});for(var T=0;T<l.orderedModifiers.length;T++){if(l.reset===!0){l.reset=!1,T=-1;continue}var p=l.orderedModifiers[T],w=p.fn,R=p.options,h=R===void 0?{}:R,y=p.name;typeof w=="function"&&(l=w({state:l,options:h,name:y,instance:c})||l)}}}},update:Mr(function(){return new Promise(function(m){c.forceUpdate(),m(l)})}),destroy:function(){b(),f=!0}};if(!Kt(u,i))return c;c.setOptions(s).then(function(m){!f&&s.onFirstUpdate&&s.onFirstUpdate(m)});function g(){l.orderedModifiers.forEach(function(m){var E=m.name,O=m.options,T=O===void 0?{}:O,p=m.effect;if(typeof p=="function"){var w=p({state:l,name:E,instance:c,options:T}),R=function(){};v.push(w||R)}})}function b(){v.forEach(function(m){return m()}),v=[]}return c}}ht();var Br=[fo,mo,co,ao];ht({defaultModifiers:Br});var Lr=[fo,mo,co,ao,wr,mr,Sr,er,hr],Ir=ht({defaultModifiers:Lr});const go=K({arrowOffset:{type:Number,default:5}}),jr=["fixed","absolute"],Fr=K({boundariesPadding:{type:Number,default:0},fallbackPlacements:{type:M(Array),default:void 0},gpuAcceleration:{type:Boolean,default:!0},offset:{type:Number,default:12},placement:{type:String,values:ct,default:"bottom"},popperOptions:{type:M(Object),default:()=>({})},strategy:{type:String,values:jr,default:"absolute"}}),ho=K({...Fr,...go,id:String,style:{type:M([String,Array,Object])},className:{type:M([String,Array,Object])},effect:{type:M(String),default:"dark"},visible:Boolean,enterable:{type:Boolean,default:!0},pure:Boolean,focusOnShow:{type:Boolean,default:!1},trapping:{type:Boolean,default:!1},popperClass:{type:M([String,Array,Object])},popperStyle:{type:M([String,Array,Object])},referenceEl:{type:M(Object)},triggerTargetEl:{type:M(Object)},stopPopperMouseEvent:{type:Boolean,default:!0},virtualTriggering:Boolean,zIndex:Number,...st(["ariaLabel"])}),Hr={mouseenter:e=>e instanceof MouseEvent,mouseleave:e=>e instanceof MouseEvent,focus:()=>!0,blur:()=>!0,close:()=>!0},zr=(e,t)=>{const o=x(!1),n=x();return{focusStartRef:n,trapped:o,onFocusAfterReleased:l=>{var v;((v=l.detail)==null?void 0:v.focusReason)!=="pointer"&&(n.value="first",t("blur"))},onFocusAfterTrapped:()=>{t("focus")},onFocusInTrap:l=>{e.visible&&!o.value&&(l.target&&(n.value=l.target),o.value=!0)},onFocusoutPrevented:l=>{e.trapping||(l.detail.focusReason==="pointer"&&l.preventDefault(),o.value=!1)},onReleaseRequested:()=>{o.value=!1,t("close")}}},Nr=(e,t=[])=>{const{placement:o,strategy:n,popperOptions:r}=e,a={placement:o,strategy:n,...r,modifiers:[...$r(e),...t]};return Wr(a,r==null?void 0:r.modifiers),a},Dr=e=>{if(ye)return Yt(e)};function $r(e){const{offset:t,gpuAcceleration:o,fallbackPlacements:n}=e;return[{name:"offset",options:{offset:[0,t??12]}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5,fallbackPlacements:n}},{name:"computeStyles",options:{gpuAcceleration:o}}]}function Wr(e,t){t&&(e.modifiers=[...e.modifiers,...t??[]])}const Kr=(e,t,o={})=>{const n={name:"updateState",enabled:!0,phase:"write",fn:({state:s})=>{const l=qr(s);Object.assign(u.value,l)},requires:["computeStyles"]},r=P(()=>{const{onFirstUpdate:s,placement:l,strategy:v,modifiers:f}=d(o);return{onFirstUpdate:s,placement:l||"bottom",strategy:v||"absolute",modifiers:[...f||[],n,{name:"applyStyles",enabled:!1}]}}),a=zo(),u=x({styles:{popper:{position:d(r).strategy,left:"0",top:"0"},arrow:{position:"absolute"}},attributes:{}}),i=()=>{a.value&&(a.value.destroy(),a.value=void 0)};return z(r,s=>{const l=d(a);l&&l.setOptions(s)},{deep:!0}),z([e,t],([s,l])=>{i(),!(!s||!l)&&(a.value=Ir(s,l,d(r)))}),Pe(()=>{i()}),{state:P(()=>{var s;return{...((s=d(a))==null?void 0:s.state)||{}}}),styles:P(()=>d(u).styles),attributes:P(()=>d(u).attributes),update:()=>{var s;return(s=d(a))==null?void 0:s.update()},forceUpdate:()=>{var s;return(s=d(a))==null?void 0:s.forceUpdate()},instanceRef:P(()=>d(a))}};function qr(e){const t=Object.keys(e.elements),o=_t(t.map(r=>[r,e.styles[r]||{}])),n=_t(t.map(r=>[r,e.attributes[r]]));return{styles:o,attributes:n}}const Ur=0,Vr=e=>{const{popperInstanceRef:t,contentRef:o,triggerRef:n,role:r}=ae(lt,void 0),a=x(),u=P(()=>e.arrowOffset),i=P(()=>({name:"eventListeners",enabled:!!e.visible})),s=P(()=>{var O;const T=d(a),p=(O=d(u))!=null?O:Ur;return{name:"arrow",enabled:!on(T),options:{element:T,padding:p}}}),l=P(()=>({onFirstUpdate:()=>{b()},...Nr(e,[d(s),d(i)])})),v=P(()=>Dr(e.referenceEl)||d(n)),{attributes:f,state:c,styles:g,update:b,forceUpdate:m,instanceRef:E}=Kr(v,o,l);return z(E,O=>t.value=O,{flush:"sync"}),He(()=>{z(()=>{var O;return(O=d(v))==null?void 0:O.getBoundingClientRect()},()=>{b()})}),{attributes:f,arrowRef:a,contentRef:o,instanceRef:E,state:c,styles:g,role:r,forceUpdate:m,update:b}},Xr=(e,{attributes:t,styles:o,role:n})=>{const{nextZIndex:r}=No(),a=ie("popper"),u=P(()=>d(t).popper),i=x(se(e.zIndex)?e.zIndex:r()),s=P(()=>[a.b(),a.is("pure",e.pure),a.is(e.effect),e.popperClass]),l=P(()=>[{zIndex:d(i)},d(o).popper,e.popperStyle||{}]),v=P(()=>n.value==="dialog"?"false":void 0),f=P(()=>d(o).arrow||{});return{ariaModal:v,arrowStyle:f,contentAttrs:u,contentClass:s,contentStyle:l,contentZIndex:i,updateZIndex:()=>{i.value=se(e.zIndex)?e.zIndex:r()}}},Yr=I({name:"ElPopperContent"}),Zr=I({...Yr,props:ho,emits:Hr,setup(e,{expose:t,emit:o}){const n=e,{focusStartRef:r,trapped:a,onFocusAfterReleased:u,onFocusAfterTrapped:i,onFocusInTrap:s,onFocusoutPrevented:l,onReleaseRequested:v}=zr(n,o),{attributes:f,arrowRef:c,contentRef:g,styles:b,instanceRef:m,role:E,update:O}=Vr(n),{ariaModal:T,arrowStyle:p,contentAttrs:w,contentClass:R,contentStyle:h,updateZIndex:y}=Xr(n,{styles:b,attributes:f,role:E}),S=ae(kt,void 0);Oe(Qt,{arrowStyle:p,arrowRef:c}),S&&Oe(kt,{...S,addInputId:ot,removeInputId:ot});let A;const _=(B=!0)=>{O(),B&&y()},k=()=>{_(!1),n.visible&&n.focusOnShow?a.value=!0:n.visible===!1&&(a.value=!1)};return He(()=>{z(()=>n.triggerTargetEl,(B,j)=>{A==null||A(),A=void 0;const N=d(B||g.value),X=d(j||g.value);ke(N)&&(A=z([E,()=>n.ariaLabel,T,()=>n.id],L=>{["role","aria-label","aria-modal","id"].forEach((H,C)=>{Zt(L[C])?N.removeAttribute(H):N.setAttribute(H,L[C])})},{immediate:!0})),X!==N&&ke(X)&&["role","aria-label","aria-modal","id"].forEach(L=>{X.removeAttribute(L)})},{immediate:!0}),z(()=>n.visible,k,{immediate:!0})}),Pe(()=>{A==null||A(),A=void 0}),t({popperContentRef:g,popperInstanceRef:m,updatePopper:_,contentStyle:h}),(B,j)=>(D(),we("div",at({ref_key:"contentRef",ref:g},d(w),{style:d(h),class:d(R),tabindex:"-1",onMouseenter:N=>B.$emit("mouseenter",N),onMouseleave:N=>B.$emit("mouseleave",N)}),[me(d(Jo),{trapped:d(a),"trap-on-focus-in":!0,"focus-trap-el":d(g),"focus-start-el":d(r),onFocusAfterTrapped:d(i),onFocusAfterReleased:d(u),onFocusin:d(s),onFocusoutPrevented:d(l),onReleaseRequested:d(v)},{default:Y(()=>[ee(B.$slots,"default")]),_:3},8,["trapped","focus-trap-el","focus-start-el","onFocusAfterTrapped","onFocusAfterReleased","onFocusin","onFocusoutPrevented","onReleaseRequested"])],16,["onMouseenter","onMouseleave"]))}});var Gr=Z(Zr,[["__file","content.vue"]]);const Jr=Ye(Tn),bt=Symbol("elTooltip"),bo=K({to:{type:M([String,Object]),required:!0},disabled:Boolean}),yo=K({...nn,...ho,appendTo:{type:bo.to.type},content:{type:String,default:""},rawContent:Boolean,persistent:Boolean,visible:{type:M(Boolean),default:null},transition:String,teleported:{type:Boolean,default:!0},disabled:Boolean,...st(["ariaLabel"])}),wo=K({...to,disabled:Boolean,trigger:{type:M([String,Array]),default:"hover"},triggerKeys:{type:M(Array),default:()=>[Qe.enter,Qe.numpadEnter,Qe.space]}}),Qr=Gt({type:M(Boolean),default:null}),ea=Gt({type:M(Function)}),ta=e=>{const t=`update:${e}`,o=`onUpdate:${e}`,n=[t],r={[e]:Qr,[o]:ea};return{useModelToggle:({indicator:u,toggleReason:i,shouldHideWhenRouteChanges:s,shouldProceed:l,onShow:v,onHide:f})=>{const c=Do(),{emit:g}=c,b=c.props,m=P(()=>Ke(b[o])),E=P(()=>b[e]===null),O=y=>{u.value!==!0&&(u.value=!0,i&&(i.value=y),Ke(v)&&v(y))},T=y=>{u.value!==!1&&(u.value=!1,i&&(i.value=y),Ke(f)&&f(y))},p=y=>{if(b.disabled===!0||Ke(l)&&!l())return;const S=m.value&&ye;S&&g(t,!0),(E.value||!S)&&O(y)},w=y=>{if(b.disabled===!0||!ye)return;const S=m.value&&ye;S&&g(t,!1),(E.value||!S)&&T(y)},R=y=>{Jt(y)&&(b.disabled&&y?m.value&&g(t,!1):u.value!==y&&(y?O():T()))},h=()=>{u.value?w():p()};return z(()=>b[e],R),s&&c.appContext.config.globalProperties.$route!==void 0&&z(()=>({...c.proxy.$route}),()=>{s.value&&u.value&&w()}),He(()=>{R(b[e])}),{hide:w,show:p,toggle:h,hasUpdateHandler:m}},useModelToggleProps:r,useModelToggleEmits:n}},{useModelToggleProps:oa,useModelToggleEmits:na,useModelToggle:ra}=ta("visible"),aa=K({...eo,...oa,...yo,...wo,...go,showArrow:{type:Boolean,default:!0}}),sa=[...na,"before-show","before-hide","show","hide","open","close"],ia=(e,t)=>$o(e)?e.includes(t):e===t,be=(e,t,o)=>n=>{ia(d(e),t)&&o(n)},ne=(e,t,{checkForDefaultPrevented:o=!0}={})=>r=>{const a=e==null?void 0:e(r);if(o===!1||!a)return t==null?void 0:t(r)},Pa=e=>t=>t.pointerType==="mouse"?e(t):void 0,la=I({name:"ElTooltipTrigger"}),ua=I({...la,props:wo,setup(e,{expose:t}){const o=e,n=ie("tooltip"),{controlled:r,id:a,open:u,onOpen:i,onClose:s,onToggle:l}=ae(bt,void 0),v=x(null),f=()=>{if(d(r)||o.disabled)return!0},c=pe(o,"trigger"),g=ne(f,be(c,"hover",i)),b=ne(f,be(c,"hover",s)),m=ne(f,be(c,"click",w=>{w.button===0&&l(w)})),E=ne(f,be(c,"focus",i)),O=ne(f,be(c,"focus",s)),T=ne(f,be(c,"contextmenu",w=>{w.preventDefault(),l(w)})),p=ne(f,w=>{const{code:R}=w;o.triggerKeys.includes(R)&&(w.preventDefault(),l(w))});return t({triggerRef:v}),(w,R)=>(D(),Q(d(Bn),{id:d(a),"virtual-ref":w.virtualRef,open:d(u),"virtual-triggering":w.virtualTriggering,class:de(d(n).e("trigger")),onBlur:d(O),onClick:d(m),onContextmenu:d(T),onFocus:d(E),onMouseenter:d(g),onMouseleave:d(b),onKeydown:d(p)},{default:Y(()=>[ee(w.$slots,"default")]),_:3},8,["id","virtual-ref","open","virtual-triggering","class","onBlur","onClick","onContextmenu","onFocus","onMouseenter","onMouseleave","onKeydown"]))}});var ca=Z(ua,[["__file","trigger.vue"]]);const fa=I({__name:"teleport",props:bo,setup(e){return(t,o)=>t.disabled?ee(t.$slots,"default",{key:0}):(D(),Q(Wo,{key:1,to:t.to},[ee(t.$slots,"default")],8,["to"]))}});var pa=Z(fa,[["__file","teleport.vue"]]);const da=Ye(pa),Oo=()=>{const e=qo(),t=en(),o=P(()=>`${e.value}-popper-container-${t.prefix}`),n=P(()=>`#${o.value}`);return{id:o,selector:n}},va=e=>{const t=document.createElement("div");return t.id=e,document.body.appendChild(t),t},ma=()=>{const{id:e,selector:t}=Oo();return Ko(()=>{ye&&(document.body.querySelector(t.value)||va(e.value))}),{id:e,selector:t}},ga=I({name:"ElTooltipContent",inheritAttrs:!1}),ha=I({...ga,props:yo,setup(e,{expose:t}){const o=e,{selector:n}=Oo(),r=ie("tooltip"),a=x(),u=Uo(()=>{var C;return(C=a.value)==null?void 0:C.popperContentRef});let i;const{controlled:s,id:l,open:v,trigger:f,onClose:c,onOpen:g,onShow:b,onHide:m,onBeforeShow:E,onBeforeHide:O}=ae(bt,void 0),T=P(()=>o.transition||`${r.namespace.value}-fade-in-linear`),p=P(()=>o.persistent);Pe(()=>{i==null||i()});const w=P(()=>d(p)?!0:d(v)),R=P(()=>o.disabled?!1:d(v)),h=P(()=>o.appendTo||n.value),y=P(()=>{var C;return(C=o.style)!=null?C:{}}),S=x(!0),A=()=>{m(),H()&&Qo(document.body),S.value=!0},_=()=>{if(d(s))return!0},k=ne(_,()=>{o.enterable&&d(f)==="hover"&&g()}),B=ne(_,()=>{d(f)==="hover"&&c()}),j=()=>{var C,F;(F=(C=a.value)==null?void 0:C.updatePopper)==null||F.call(C),E==null||E()},N=()=>{O==null||O()},X=()=>{b()},L=()=>{o.virtualTriggering||c()},H=C=>{var F;const ue=(F=a.value)==null?void 0:F.popperContentRef,Ae=(C==null?void 0:C.relatedTarget)||document.activeElement;return ue==null?void 0:ue.contains(Ae)};return z(()=>d(v),C=>{C?(S.value=!1,i=Vo(u,()=>{if(d(s))return;d(f)!=="hover"&&c()})):i==null||i()},{flush:"post"}),z(()=>o.content,()=>{var C,F;(F=(C=a.value)==null?void 0:C.updatePopper)==null||F.call(C)}),t({contentRef:a,isFocusInsideContent:H}),(C,F)=>(D(),Q(d(da),{disabled:!C.teleported,to:d(h)},{default:Y(()=>[me(Ut,{name:d(T),onAfterLeave:A,onBeforeEnter:j,onAfterEnter:X,onBeforeLeave:N},{default:Y(()=>[d(w)?rt((D(),Q(d(Gr),at({key:0,id:d(l),ref_key:"contentRef",ref:a},C.$attrs,{"aria-label":C.ariaLabel,"aria-hidden":S.value,"boundaries-padding":C.boundariesPadding,"fallback-placements":C.fallbackPlacements,"gpu-acceleration":C.gpuAcceleration,offset:C.offset,placement:C.placement,"popper-options":C.popperOptions,"arrow-offset":C.arrowOffset,strategy:C.strategy,effect:C.effect,enterable:C.enterable,pure:C.pure,"popper-class":C.popperClass,"popper-style":[C.popperStyle,d(y)],"reference-el":C.referenceEl,"trigger-target-el":C.triggerTargetEl,visible:d(R),"z-index":C.zIndex,onMouseenter:d(k),onMouseleave:d(B),onBlur:L,onClose:d(c)}),{default:Y(()=>[ee(C.$slots,"default")]),_:3},16,["id","aria-label","aria-hidden","boundaries-padding","fallback-placements","gpu-acceleration","offset","placement","popper-options","arrow-offset","strategy","effect","enterable","pure","popper-class","popper-style","reference-el","trigger-target-el","visible","z-index","onMouseenter","onMouseleave","onClose"])),[[qt,d(R)]]):Ie("v-if",!0)]),_:3},8,["name"])]),_:3},8,["disabled","to"]))}});var ba=Z(ha,[["__file","content.vue"]]);const ya=I({name:"ElTooltip"}),wa=I({...ya,props:aa,emits:sa,setup(e,{expose:t,emit:o}){const n=e;ma();const r=ie("tooltip"),a=tn(),u=x(),i=x(),s=()=>{var p;const w=d(u);w&&((p=w.popperInstanceRef)==null||p.update())},l=x(!1),v=x(),{show:f,hide:c,hasUpdateHandler:g}=ra({indicator:l,toggleReason:v}),{onOpen:b,onClose:m}=rn({showAfter:pe(n,"showAfter"),hideAfter:pe(n,"hideAfter"),autoClose:pe(n,"autoClose"),open:f,close:c}),E=P(()=>Jt(n.visible)&&!g.value),O=P(()=>[r.b(),n.popperClass]);Oe(bt,{controlled:E,id:a,open:Xo(l),trigger:pe(n,"trigger"),onOpen:p=>{b(p)},onClose:p=>{m(p)},onToggle:p=>{d(l)?m(p):b(p)},onShow:()=>{o("show",v.value)},onHide:()=>{o("hide",v.value)},onBeforeShow:()=>{o("before-show",v.value)},onBeforeHide:()=>{o("before-hide",v.value)},updatePopper:s}),z(()=>n.disabled,p=>{p&&l.value&&(l.value=!1)});const T=p=>{var w;return(w=i.value)==null?void 0:w.isFocusInsideContent(p)};return Yo(()=>l.value&&c()),t({popperRef:u,contentRef:i,isFocusInsideContent:T,updatePopper:s,onOpen:b,onClose:m,hide:c}),(p,w)=>(D(),Q(d(Jr),{ref_key:"popperRef",ref:u,role:p.role},{default:Y(()=>[me(ca,{disabled:p.disabled,trigger:p.trigger,"trigger-keys":p.triggerKeys,"virtual-ref":p.virtualRef,"virtual-triggering":p.virtualTriggering},{default:Y(()=>[p.$slots.default?ee(p.$slots,"default",{key:0}):Ie("v-if",!0)]),_:3},8,["disabled","trigger","trigger-keys","virtual-ref","virtual-triggering"]),me(ba,{ref_key:"contentRef",ref:i,"aria-label":p.ariaLabel,"boundaries-padding":p.boundariesPadding,content:p.content,disabled:p.disabled,effect:p.effect,enterable:p.enterable,"fallback-placements":p.fallbackPlacements,"hide-after":p.hideAfter,"gpu-acceleration":p.gpuAcceleration,offset:p.offset,persistent:p.persistent,"popper-class":d(O),"popper-style":p.popperStyle,placement:p.placement,"popper-options":p.popperOptions,"arrow-offset":p.arrowOffset,pure:p.pure,"raw-content":p.rawContent,"reference-el":p.referenceEl,"trigger-target-el":p.triggerTargetEl,"show-after":p.showAfter,strategy:p.strategy,teleported:p.teleported,transition:p.transition,"virtual-triggering":p.virtualTriggering,"z-index":p.zIndex,"append-to":p.appendTo},{default:Y(()=>[ee(p.$slots,"content",{},()=>[p.rawContent?(D(),we("span",{key:0,innerHTML:p.content},null,8,["innerHTML"])):(D(),we("span",{key:1},Zo(p.content),1))]),p.showArrow?(D(),Q(d(Rn),{key:0})):Ie("v-if",!0)]),_:3},8,["aria-label","boundaries-padding","content","disabled","effect","enterable","fallback-placements","hide-after","gpu-acceleration","offset","persistent","popper-class","popper-style","placement","popper-options","arrow-offset","pure","raw-content","reference-el","trigger-target-el","show-after","strategy","teleported","transition","virtual-triggering","z-index","append-to"])]),_:3},8,["role"]))}});var Oa=Z(wa,[["__file","tooltip.vue"]]);const Aa=Ye(Oa);export{Aa as E,_n as O,bt as T,da as a,wo as b,ne as c,Ra as d,ct as e,wn as r,mn as s,bo as t,yo as u,Pa as w};
