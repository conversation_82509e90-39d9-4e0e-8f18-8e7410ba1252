import{f as Ee,a as ke,u as Be,b as Ie,E as Te}from"./el-button-B8TCQS4u.js";import{E as Se,a as De}from"./el-form-item-Beaw8WQV.js";import{E as Fe,a as Ne}from"./el-select-DNFSsBSe.js";import"./el-scrollbar-DLTsEwUl.js";import{_ as he}from"./UploadImg-DiG_g-wU.js";import{E as Me,a as Ue}from"./el-progress-BWXbzDlP.js";import{E as Pe}from"./el-overlay-CF-bF7w3.js";import{E as He}from"./el-message-box-BNmbnUjS.js";/* empty css                 */import{O as de,H as Oe,Y as Re,I as me,J as oe,aa as W,ab as pe,ac as je,_ as Ae,K as we,L as ce,N as $e,e as V,M as _,ad as ae,P as ve,ae as te,af as fe,ag as ze,c,o as m,D as se,F as k,C as J,n as S,v as s,b as t,f as r,w as re,g as D,R as q,ah as _e,y as Ve,j as ne,t as Ce,ai as G,aj as ye,S as Le,d as Ke,a as n,ak as ie,i as We,k as x,al as qe}from"./index-Dgb0NZ1J.js";import{c as be,p as Ge}from"./list_icon_comput-BNFIRqSF.js";import{E as Je}from"./index-C-aKrRv3.js";import{U as X,C as ue,u as Xe}from"./index-CINbulG0.js";import{E as xe}from"./index-DzgKufUD.js";import"./castArray-BpiBxx45.js";import"./isEqual-CwZW0B1R.js";import"./vnode-CV7pw3rS.js";import"./plugin-vue_export-helper-DlAUqK2U.js";import"./refs-CFD094o4.js";const Ye=Oe({modelValue:{type:Number,default:0},id:{type:String,default:void 0},lowThreshold:{type:Number,default:2},highThreshold:{type:Number,default:4},max:{type:Number,default:5},colors:{type:oe([Array,Object]),default:()=>me(["","",""])},voidColor:{type:String,default:""},disabledVoidColor:{type:String,default:""},icons:{type:oe([Array,Object]),default:()=>[W,W,W]},voidIcon:{type:pe,default:()=>je},disabledVoidIcon:{type:pe,default:()=>W},disabled:Boolean,allowHalf:Boolean,showText:Boolean,showScore:Boolean,textColor:{type:String,default:""},texts:{type:oe(Array),default:()=>me(["Extremely bad","Disappointed","Fair","Satisfied","Surprise"])},scoreTemplate:{type:String,default:"{value}"},size:Re,clearable:Boolean,...Xe(["ariaLabel"])}),Qe={[ue]:B=>de(B),[X]:B=>de(B)},Ze=we({name:"ElRate"}),el=we({...Ze,props:Ye,emits:Qe,setup(B,{expose:F,emit:y}){const l=B;function N(o,u){const d=f=>ve(f),b=Object.keys(u).map(f=>+f).filter(f=>{const K=u[f];return(d(K)?K.excluded:!1)?o<f:o<=f}).sort((f,K)=>f-K),H=u[b[0]];return d(H)&&H.value||H}const I=ce(Ee,void 0),C=ce(ke,void 0),Y=Be(),i=$e("rate"),{inputId:Q,isLabeledByFormItem:O}=Ie(l,{formItemContext:C}),p=V(l.modelValue),h=V(-1),g=V(!0),M=_(()=>[i.b(),i.m(Y.value)]),e=_(()=>l.disabled||(I==null?void 0:I.disabled)),E=_(()=>i.cssVarBlock({"void-color":l.voidColor,"disabled-void-color":l.disabledVoidColor,"fill-color":P.value})),v=_(()=>{let o="";return l.showScore?o=l.scoreTemplate.replace(/\{\s*value\s*\}/,e.value?`${l.modelValue}`:`${p.value}`):l.showText&&(o=l.texts[Math.ceil(p.value)-1]),o}),U=_(()=>l.modelValue*100-Math.floor(l.modelValue)*100),Z=_(()=>ae(l.colors)?{[l.lowThreshold]:l.colors[0],[l.highThreshold]:{value:l.colors[1],excluded:!0},[l.max]:l.colors[2]}:l.colors),P=_(()=>{const o=N(p.value,Z.value);return ve(o)?"":o}),R=_(()=>{let o="";return e.value?o=`${U.value}%`:l.allowHalf&&(o="50%"),{color:P.value,width:o}}),j=_(()=>{let o=ae(l.icons)?[...l.icons]:{...l.icons};return o=te(o),ae(o)?{[l.lowThreshold]:o[0],[l.highThreshold]:{value:o[1],excluded:!0},[l.max]:o[2]}:o}),w=_(()=>N(l.modelValue,j.value)),A=_(()=>e.value?fe(l.disabledVoidIcon)?l.disabledVoidIcon:te(l.disabledVoidIcon):fe(l.voidIcon)?l.voidIcon:te(l.voidIcon)),ee=_(()=>N(p.value,j.value));function $(o){const u=e.value&&U.value>0&&o-1<l.modelValue&&o>l.modelValue,d=l.allowHalf&&g.value&&o-.5<=p.value&&o>p.value;return u||d}function z(o){l.clearable&&o===l.modelValue&&(o=0),y(X,o),l.modelValue!==o&&y(ue,o)}function le(o){e.value||(l.allowHalf&&g.value?z(p.value):z(o))}function a(o){if(e.value)return;let u=p.value;const d=o.code;return d===G.up||d===G.right?(l.allowHalf?u+=.5:u+=1,o.stopPropagation(),o.preventDefault()):(d===G.left||d===G.down)&&(l.allowHalf?u-=.5:u-=1,o.stopPropagation(),o.preventDefault()),u=u<0?0:u,u=u>l.max?l.max:u,y(X,u),y(ue,u),u}function L(o,u){if(!e.value){if(l.allowHalf&&u){let d=u.target;ye(d,i.e("item"))&&(d=d.querySelector(`.${i.e("icon")}`)),(d.clientWidth===0||ye(d,i.e("decimal")))&&(d=d.parentNode),g.value=u.offsetX*2<=d.clientWidth,p.value=g.value?o-.5:o}else p.value=o;h.value=o}}function T(){e.value||(l.allowHalf&&(g.value=l.modelValue!==Math.floor(l.modelValue)),p.value=l.modelValue,h.value=-1)}return ze(()=>l.modelValue,o=>{p.value=o,g.value=l.modelValue!==Math.floor(l.modelValue)}),l.modelValue||y(X,0),F({setCurrentValue:L,resetCurrentValue:T}),(o,u)=>{var d;return m(),c("div",{id:s(Q),class:S([s(M),s(i).is("disabled",s(e))]),role:"slider","aria-label":s(O)?void 0:o.ariaLabel||"rating","aria-labelledby":s(O)?(d=s(C))==null?void 0:d.labelId:void 0,"aria-valuenow":p.value,"aria-valuetext":s(v)||void 0,"aria-valuemin":"0","aria-valuemax":o.max,tabindex:"0",style:ne(s(E)),onKeydown:a},[(m(!0),c(k,null,J(o.max,(b,H)=>(m(),c("span",{key:H,class:S(s(i).e("item")),onMousemove:f=>L(b,f),onMouseleave:T,onClick:f=>le(b)},[t(s(Ve),{class:S([s(i).e("icon"),{hover:h.value===b},s(i).is("active",b<=p.value)])},{default:r(()=>[$(b)?se("v-if",!0):(m(),c(k,{key:0},[re((m(),D(q(s(ee)),null,null,512)),[[_e,b<=p.value]]),re((m(),D(q(s(A)),null,null,512)),[[_e,!(b<=p.value)]])],64)),$(b)?(m(),c(k,{key:1},[(m(),D(q(s(A)),{class:S([s(i).em("decimal","box")])},null,8,["class"])),t(s(Ve),{style:ne(s(R)),class:S([s(i).e("icon"),s(i).e("decimal")])},{default:r(()=>[(m(),D(q(s(w))))]),_:1},8,["style","class"])],64)):se("v-if",!0)]),_:2},1032,["class"])],42,["onMousemove","onClick"]))),128)),o.showText||o.showScore?(m(),c("span",{key:0,class:S(s(i).e("text")),style:ne({color:o.textColor})},Ce(s(v)),7)):se("v-if",!0)],46,["id","aria-label","aria-labelledby","aria-valuenow","aria-valuetext","aria-valuemax"])}}});var ll=Ae(el,[["__file","rate.vue"]]);const ol=Le(ll),ge="/assets/logo-CPmPqqKk.png",al={key:0,class:"market_header"},tl={key:1,class:"flex_box my_header"},sl={class:"comp_list"},nl={class:"detail"},il={class:"flex_box tag"},rl={class:"type_icon_box"},ul=["src"],dl={key:0,class:"operation"},ml={key:1,class:"operation"},pl={class:"pagination"},cl={class:"flex_box comp_list_item dialog_comp_list_item"},vl={class:"detail"},fl={class:"flex_box tag"},_l={class:"type_icon_box"},Vl=["src"],yl={style:{display:"flex","justify-content":"center"}},Rl={__name:"componentMarket",setup(B){const F=V(!1),y=V(""),l=V(3),N=V(["列表","按钮","图片","广告滚动","Tabbar","Fragment","下拉窗","富文本编辑","WebView"]),I=V(!1),C=V(!1),Y=V(),i=V({}),Q=Ke({}),O=()=>{},p=()=>{},h=()=>{xe.confirm("是否删除组件","删除组件",{confirmButtonText:"确定",cancelButtonText:"取消",center:!0,customClass:"custom_message_box"}).then(()=>{})},g=()=>{xe.confirm("组件下载成功，已经放入【我的仓库】中，可以进入查看","下载组件",{confirmButtonText:"转到我的仓库",cancelButtonText:"确定",center:!0,customClass:"custom_message_box"}).then(()=>{F.value=!0})};return(M,e)=>{const E=Je,v=Te,U=ol,Z=He,P=Pe,R=Ue,j=Me,w=De,A=he,ee=Ne,$=Fe,z=Se,le=We("debounce");return m(),c(k,null,[F.value?(m(),c("div",tl,[e[15]||(e[15]=n("div",{class:"title"},"我的组件仓库",-1)),t(E,{modelValue:y.value,"onUpdate:modelValue":e[1]||(e[1]=a=>y.value=a),placeholder:"请输入关键字搜索","prefix-icon":s(ie)},null,8,["modelValue","prefix-icon"])])):(m(),c("div",al,[t(E,{modelValue:y.value,"onUpdate:modelValue":e[0]||(e[0]=a=>y.value=a),placeholder:"请输入关键字搜索","prefix-icon":s(ie)},null,8,["modelValue","prefix-icon"]),n("div",null,[(m(!0),c(k,null,J(N.value,a=>(m(),D(v,{key:a},{default:r(()=>[x(Ce(a),1)]),_:2},1024))),128))])])),n("div",sl,[(m(),c(k,null,J(6,(a,L)=>n("div",{class:"flex_box comp_list_item",key:L},[e[25]||(e[25]=n("img",{class:"comp_list_item_logo",src:ge,alt:""},null,-1)),n("div",nl,[e[22]||(e[22]=n("div",{class:"title"},"列表组建（V1.0.25）",-1)),n("div",il,[e[16]||(e[16]=n("div",{class:"type"},"列表",-1)),n("div",rl,[n("img",{class:"type_icon",src:s(be)},null,8,ul)]),e[17]||(e[17]=n("div",{class:"time"},"2025.03.12",-1)),t(U,{modelValue:l.value,"onUpdate:modelValue":e[2]||(e[2]=T=>l.value=T),disabled:""},null,8,["modelValue"])]),e[23]||(e[23]=n("div",{class:"author"},"作者：九天站祖（NO.234590）",-1)),e[24]||(e[24]=n("div",{class:"desc"}," 说明：这个列表控制可以多选这个列表控制可以多选这个列表控制可以多选这个列表控制可以多选这个列表控这个列表控制可以多选 ",-1)),F.value?(m(),c("div",ml,[t(v,{type:"primary",color:"#2B6BFF",onClick:e[4]||(e[4]=T=>C.value=!0)},{default:r(()=>e[20]||(e[20]=[x(" 上传市场 ")])),_:1,__:[20]}),t(v,{type:"primary",plain:"",color:"#2B6BFF",class:"custom-button"},{default:r(()=>e[21]||(e[21]=[x(" 编辑组件 ")])),_:1,__:[21]}),t(v,{icon:s(qe),onClick:h},null,8,["icon"]),t(v,{icon:s(ie)},null,8,["icon"])])):(m(),c("div",dl,[t(v,{type:"primary",color:"#2B6BFF",onClick:g},{default:r(()=>e[18]||(e[18]=[x(" 下载 (12次) ")])),_:1,__:[18]}),t(v,{type:"primary",plain:"",color:"#2B6BFF",class:"custom-button",onClick:e[3]||(e[3]=T=>I.value=!0)},{default:r(()=>e[19]||(e[19]=[x(" 接口说明 ")])),_:1,__:[19]})]))])])),64))]),n("div",pl,[t(Z,{layout:"prev, pager, next",total:1e3})]),t(P,{modelValue:I.value,"onUpdate:modelValue":e[6]||(e[6]=a=>I.value=a),"destroy-on-close":"",width:"1000"},{header:r(()=>e[26]||(e[26]=[n("div",{style:{"text-align":"center","font-weight":"600","font-size":"18px"}}," 接口说明 ",-1)])),default:r(()=>[n("div",cl,[e[32]||(e[32]=n("img",{class:"comp_list_item_logo",src:ge,alt:""},null,-1)),n("div",vl,[e[29]||(e[29]=n("div",{class:"title"},"列表组建（V1.0.25）",-1)),n("div",fl,[e[27]||(e[27]=n("div",{class:"type"},"列表",-1)),n("div",_l,[n("img",{class:"type_icon",src:M.index%2?s(be):s(Ge)},null,8,Vl)]),e[28]||(e[28]=n("div",{class:"time"},"2025.03.12",-1)),t(U,{modelValue:l.value,"onUpdate:modelValue":e[5]||(e[5]=a=>l.value=a),disabled:""},null,8,["modelValue"])]),e[30]||(e[30]=n("div",{class:"author"},"作者：九天站祖（NO.234590）",-1)),e[31]||(e[31]=n("div",{class:"desc"}," 说明：这个列表控制可以多选这个列表控制可以多选这个列表控制可以多选这个列表控制可以多选这个列表控这个列表控制可以多选 ",-1))])]),e[33]||(e[33]=n("div",{class:"descripetion"}," 1、数据：列表类型，单个列表项 { id：234， image：’xxxxx.png’， text：‘好货’ } ",-1))]),_:1,__:[33]},8,["modelValue"]),t(P,{modelValue:C.value,"onUpdate:modelValue":e[14]||(e[14]=a=>C.value=a),"destroy-on-close":"",width:"600",onClose:O},{header:r(()=>e[34]||(e[34]=[n("div",{style:{"text-align":"center","font-weight":"600","font-size":"18px"}}," 上传市场 ",-1)])),footer:r(()=>[n("div",yl,[t(v,{onClick:e[13]||(e[13]=a=>C.value=!1)},{default:r(()=>e[37]||(e[37]=[x("取消")])),_:1,__:[37]}),re((m(),D(v,{type:"primary",color:"#2B6BFF"},{default:r(()=>e[38]||(e[38]=[x(" 保存 ")])),_:1,__:[38]})),[[le,p]])])]),default:r(()=>[t(z,{ref_key:"formRef",ref:Y,model:i.value,rules:Q,"label-width":"100px","status-icon":"","label-position":"left"},{default:r(()=>[t(w,{label:"类型",prop:"clientType"},{default:r(()=>[t(j,{modelValue:i.value.clientType,"onUpdate:modelValue":e[7]||(e[7]=a=>i.value.clientType=a)},{default:r(()=>[t(R,{value:0},{default:r(()=>e[35]||(e[35]=[x("手机")])),_:1,__:[35]}),t(R,{value:1},{default:r(()=>e[36]||(e[36]=[x("pc")])),_:1,__:[36]})]),_:1},8,["modelValue"])]),_:1}),t(w,{label:"图片",prop:"icon"},{default:r(()=>[t(A,{placeholder:"应用图标",modelValue:i.value.icon,"onUpdate:modelValue":e[8]||(e[8]=a=>i.value.icon=a)},null,8,["modelValue"])]),_:1}),t(w,{label:"名称",prop:"name"},{default:r(()=>[t(E,{modelValue:i.value.name,"onUpdate:modelValue":e[9]||(e[9]=a=>i.value.name=a),placeholder:"请输入组件名称"},null,8,["modelValue"])]),_:1}),t(w,{label:"分类",prop:"name"},{default:r(()=>[t($,{modelValue:M.value,"onUpdate:modelValue":e[10]||(e[10]=a=>M.value=a),placeholder:"请选择组件分类"},{default:r(()=>[(m(),c(k,null,J([],a=>t(ee,{key:a.value,label:a.label,value:a.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),t(w,{label:"版本号",prop:"name"},{default:r(()=>[t(E,{modelValue:i.value.name,"onUpdate:modelValue":e[11]||(e[11]=a=>i.value.name=a),placeholder:"请填写版本号"},null,8,["modelValue"])]),_:1}),t(w,{label:"说明",prop:"description"},{default:r(()=>[t(E,{modelValue:i.value.description,"onUpdate:modelValue":e[12]||(e[12]=a=>i.value.description=a),placeholder:"请输入内容",type:"textarea"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])],64)}}};export{Rl as default};
