<template>
	<div class="content" :style="{height:'calc(100vh - 200px - '+layoutParam.bottomTabHeight+'px)'}">
		<EventClickItem :eventItem="eventMgr.onClicked"   :targetName="eventMgr.currentName" v-if="eventMgr.onClicked.show"/>
		<EventSetItem   :eventItem="eventMgr.onLoad"      :targetName="eventMgr.currentName" v-if="eventMgr.onLoad.show" />
		<EventSetItem   :eventItem="eventMgr.onMounted"   :targetName="eventMgr.currentName" v-if="eventMgr.onMounted.show"/>
		<EventSetItem   :eventItem="eventMgr.onUnMounted" :targetName="eventMgr.currentName" v-if="eventMgr.onUnMounted.show"/>
		<EventSetItem   :eventItem="eventMgr.onDbClicked" :targetName="eventMgr.currentName" v-if="eventMgr.onDbClicked.show"/>
		<EventSetItem   :eventItem="eventMgr.onChanged"   :targetName="eventMgr.currentName" v-if="eventMgr.onChanged.show"/>
		<EventSetItem   :eventItem="eventMgr.onBlur"   :targetName="eventMgr.currentName" v-if="eventMgr.onBlur.show"/>
		<EventSetItem   :eventItem="eventMgr.onFocus"   :targetName="eventMgr.currentName" v-if="eventMgr.onFocus.show"/>
		<EventSetItem   :eventItem="eventMgr.onPullDown"   :targetName="eventMgr.currentName" v-if="eventMgr.onPullDown.show"/>
		<EventSetItem   :eventItem="eventMgr.onPullUp"   :targetName="eventMgr.currentName" v-if="eventMgr.onPullUp.show"/>
		<EventSetItem   :eventItem="eventMgr.onPageNo"   :targetName="eventMgr.currentName" v-if="eventMgr.onPageNo.show"/>
		<EventSetItem   :eventItem="eventMgr.onChangePage"   :targetName="eventMgr.currentName" v-if="eventMgr.onChangePage.show"/>
	</div>
</template>

<script lang="ts" setup>
	import {reactive,ref,onMounted,inject,toRaw} from 'vue'
	const ProjectRef = inject("ProjectRef")
	const FileScript = inject("FileScript")
	const layoutParam = inject("layoutParam")
	const ChooseComponentList = inject("ChooseComponentList")
	
	const eventMgr = reactive({
		currentId:'0',
		currentName:"",
		onLoad:{
			show:false,
		    data:{},
			init:{
				eventName:'onload',
				name:"页面加载事件(onLoad)",
				fileType:'methrod',
				eventFile:"",
				remark:"",
				params:[]
			}
		},
        onMounted:{
			show:false,
			data:{},
			init:{
				eventName:'onmounted',
				name:"页面挂载事件(onMounted)",
				fileType:'event',
				eventFile:"",
				remark:"",
				params:[]
			}
		},
		onUnMounted:{
			show:false,
			data:{},
			init:{
				eventName:'onunmounted',
				name:"页面卸载事件(onUnMounted)",
				fileType:'event',
				eventFile:"",
				remark:"",
				params:[]
			}
		},
		onClicked:{
			show:false,
			data:{},
			init:{
				type:"none", //link  event  command,none
				linked:{
					target:"page", //page  part  popup
					page:{
						pagePath:"", //页面路径
					},
					part:{
						componentPath:"", //组件文件路径
						parentName:"", //target=part时，
						mode:'replace', //replace替换, add追加
					},
					popup:{
						direct:'bottom', //弹出方式有：left, top,right,bottom,center
						expose:0.6, //弹出的窗口占全屏的比例，值范围： 0 ~ 1之间
						componentPath:"", //组件文件路径
					},
					params:[]
				},
				command:{
					command:"closePage",
					varText:"",
					params:[]
				},
				clicked:{
					eventName:'onclicked',
					name:"点击事件(onClicked)",
					fileType:'event',
					eventFile:"",
					remark:"",
					params:[]
				}
			}
		},
		onDbClicked:{
			show:false,
			data:{},
			init:{
				eventName:'ondbclicked',
				name:"双击事件(onDbClicked)",
				fileType:'event',
				eventFile:"",
				remark:"",
				params:[]
			}
		},
		onChanged:{
			show:false,
			data:{},
			init:{
				eventName:'onchanged',
				name:"内容更改事件(onChanged)",
				fileType:'event',
				eventFile:"",
				remark:"",
				params:[]
			}
		},
		onChangePage:{
			show:false,
			data:{},
			init:{
				eventName:'onChangePage',
				name:"页面切换(onChangePage)",
				fileType:'event',
				eventFile:"",
				remark:"",
				params:[]
			}
		},
		onBlur:{
			show:false,
			data:{},
			init:{
				eventName:'onblur',
				name:"失去焦点事件(onBlur)",
				fileType:'event',
				eventFile:"",
				remark:"",
				params:[]
			}
		},
		onFocus:{
			show:false,
			data:{},
			init:{
				eventName:'onfocus',
				name:"获得焦点事件(onFocus)",
				fileType:'event',
				eventFile:"",
				remark:"",
				params:[]
			}
		},
		onPullUp:{
			show:false,
			data:{},
			init:{
				eventName:'onPullUp',
				name:"上拉事件(onPullUp)",
				fileType:'event',
				eventFile:"",
				remark:"",
				params:[]
			}
		},
		onPullDown:{
			show:false,
			data:{},
			init:{
				eventName:'onPullDown',
				name:"下拉事件(onPullDown)",
				fileType:'event',
				eventFile:"",
				remark:"",
				params:[]
			}
		},
		onPageNo:{
			show:false,
			data:{},
			init:{
				eventName:'onPageNo',
				name:"列表分页(onPageNo)",
				fileType:'event',
				eventFile:"",
				remark:"",
				params:[]
			}
		}
	})
	
	ChooseComponentList.doOtherFunc.noticeEvent = ()=>{
		
		if(ChooseComponentList.chooseMap.size == 1){
			for(let item of ChooseComponentList.chooseMap.values()){
				if(item.id == eventMgr.currentId) return 
				eventMgr.currentId = item.id
				eventMgr.currentName = item.name
				let cmptEvent = ProjectRef.funcObj.getComponentEvents(item.key)
                let eventArray = cmptEvent.split(",")
				initEvents()
				if(eventArray.length > 0){
					if(!item.triggers) item.triggers = {}
					for(let key of eventArray){
						if(eventMgr.hasOwnProperty(key)){
							if(key=='onLoad'){
								if(!item.triggers.onLoad){
									item.triggers.onLoad = {used:'none'}
								}
								eventMgr.onLoad.data = item.triggers.onLoad
								eventMgr.onLoad.show = true
							}else if(key=='onMounted'){
								if(!item.triggers.onMounted){
									item.triggers.onMounted = {used:'none'}
								}
								eventMgr.onMounted.data = item.triggers.onMounted
								eventMgr.onMounted.show = true
							}else if(key=='onUnMounted'){
								if(!item.triggers.onUnMounted){
									item.triggers.onUnMounted = {used:'none'}
								}
								eventMgr.onUnMounted.data = item.triggers.onUnMounted
								eventMgr.onUnMounted.show = true
							}else if(key=='onClicked'){
								if(!item.triggers.onClicked){
									item.triggers.onClicked = {type:"none"}
								}
								eventMgr.onClicked.data = item.triggers.onClicked
								eventMgr.onClicked.show = true
							}else if(key=='onDbClicked'){
								if(!item.triggers.onDbClicked){
									item.triggers.onDbClicked = {used:'none'}
								}
								eventMgr.onDbClicked.data = item.triggers.onDbClicked
								eventMgr.onDbClicked.show = true
							}else if(key=='onChanged'){
								if(!item.triggers.onChanged){
									item.triggers.onChanged = {used:'none'}
								}
								eventMgr.onChanged.data = item.triggers.onChanged
								eventMgr.onChanged.show = true
							}else if(key=='onBlur'){
								if(!item.triggers.onBlur){
									item.triggers.onBlur = {used:'none'}
								}
								eventMgr.onBlur.data = item.triggers.onBlur
								eventMgr.onBlur.show = true
							}else if(key=='onFocus'){
								if(!item.triggers.onFocus){
									item.triggers.onFocus = {used:'none'}
								}
								eventMgr.onFocus.data = item.triggers.onFocus
								eventMgr.onFocus.show = true
							}else if(key=='onPullUp'){
								if(!item.triggers.onPullUp){
									item.triggers.onPullUp = {used:'none'}
								}
								eventMgr.onPullUp.data = item.triggers.onPullUp
								eventMgr.onPullUp.show = true
							}else if(key=='onPullDown'){
								if(!item.triggers.onPullDown){
									item.triggers.onPullDown = {used:'none'}
								}
								eventMgr.onPullDown.data = item.triggers.onPullDown
								eventMgr.onPullDown.show = true
							}else if(key=='onPageNo'){
								if(!item.triggers.onPageNo){
									item.triggers.onPageNo = {used:'none'}
								}
								eventMgr.onPageNo.data = item.triggers.onPageNo
								eventMgr.onPageNo.show = true
							}else if(key=='onChangePage'){
								if(!item.triggers.onChangePage){
									item.triggers.onChangePage = {used:'none'}
								}
								eventMgr.onChangePage.data = item.triggers.onChangePage
								eventMgr.onChangePage.show = true
							}
						}
					}
				}
			}
			
		}else if(ChooseComponentList.chooseMap.size > 1 ){
			initEvents()
		}else{
			eventMgr.currentId = '10001'
			let pathArray = FileScript.filePath.split(".")
			eventMgr.currentName = pathArray[pathArray.length - 2]
			
			initEvents()
			
			if(!FileScript.triggers.onLoad){
				FileScript.triggers.onLoad = {used:'none'}
			}
			eventMgr.onLoad.data = FileScript.triggers.onLoad
			eventMgr.onLoad.show = true
			
			if(!FileScript.triggers.onMounted){
				FileScript.triggers.onMounted = {used:'none'}
			}
			eventMgr.onMounted.data = FileScript.triggers.onMounted
			eventMgr.onMounted.show = true
			
			if(!FileScript.triggers.onUnMounted){
				FileScript.triggers.onUnMounted = {used:'none'}
			}
			eventMgr.onUnMounted.data = FileScript.triggers.onUnMounted
			eventMgr.onUnMounted.show = true
			
			if(!FileScript.triggers.onPullDown){
				FileScript.triggers.onPullDown = {used:'none'}
			}
			eventMgr.onPullDown.data = FileScript.triggers.onPullDown
			eventMgr.onPullDown.show = true
		}
	}
	
	function initEvents(){
		eventMgr.onLoad.show = false
		eventMgr.onLoad.data = {}
		
		eventMgr.onMounted.show = false
		eventMgr.onMounted.data = {}
		
		eventMgr.onUnMounted.show = false
		eventMgr.onUnMounted.data = {}
		
		eventMgr.onClicked.show = false
		eventMgr.onClicked.data = {}
		
		eventMgr.onDbClicked.show = false
		eventMgr.onDbClicked.data = {}
		
		eventMgr.onChanged.show = false
		eventMgr.onChanged.data = {}
		
		eventMgr.onBlur.show = false
		eventMgr.onBlur.data = {}
		
		eventMgr.onFocus.show = false
		eventMgr.onFocus.data = {}
		
		eventMgr.onPullUp.show = false
		eventMgr.onPullUp.data = {}
		
		eventMgr.onPullDown.show = false
		eventMgr.onPullDown.data = {}
		
		eventMgr.onPageNo.show = false
		eventMgr.onPageNo.data = {}
		
		eventMgr.onChangePage.show = false
		eventMgr.onChangePage.data = {}
	}
	
	
	onMounted(()=>{
		ChooseComponentList.doOtherFunc.noticeEvent()
	})
 	
</script>

<style lang="scss" scoped>
	.content {
		overflow:auto;
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: stretch;
	}
</style>