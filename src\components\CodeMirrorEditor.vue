<template>
  <!-- CodeMirror 编辑器容器 -->
  <div ref="editorContainer"></div>
</template>

<script>
import { onMounted, ref, watch, toRaw, onUnmounted, inject } from "vue";
//npm install 以下依赖包
import { EditorView, basicSetup } from "codemirror";
import { javascript } from "@codemirror/lang-javascript";
import { html } from "@codemirror/lang-html";
import { css } from "@codemirror/lang-css";
import { json } from "@codemirror/lang-json";
import { xml } from "@codemirror/lang-xml";
import { sql, SQLite } from "@codemirror/lang-sql";
import { oneDark } from "@codemirror/theme-one-dark";

export default {
  name: "CodeMirrorEditor",
  props: {
    modelValue: {
      type: String,
      default: "",
    },
    // 默认语言 支持的语言列表:javascript,html,css,json,xml,sql
    language: {
      type: String,
      default: "javascript",
    },
    // 默认主题 支持的主题列表:light(浅色), dark(深色)
    theme: {
      type: String,
      default: "light",
    },
    // 编辑器文字对齐方式
    textAlign: {
      type: String,
      default: "left",
    },
  },
  emits: ["update:modelValue", "change"],
  setup(props, { emit }) {
    // 编辑器实例引用
    const editorView = ref(null);
    // 编辑器容器DOM引用
    const editorContainer = ref(null);

    const ProjectRef = inject("ProjectRef");

    // 根据语言获取对应的 CodeMirror 扩展
    const getLanguageExtension = (lang) => {
      switch (lang) {
        case "javascript":
          return javascript();
        case "html":
          return html();
        case "css":
          return css();
        case "json":
          return json();
        case "xml":
          return xml();
        case "sql":
          return sql({
            // 你可以指定特定的 SQL 方言，默认为标准 SQL
            dialect: SQLite,
            // 其他配置选项
            upperCaseKeywords: true, // 将关键字自动转为大写
          });
        default:
          return javascript();
      }
    };

    // 根据主题获取对应的 CodeMirror 扩展
    const getThemeExtension = (theme) => {
      return theme === "dark" ? [oneDark] : [];
    };

    // 创建文本对齐的扩展
    const getTextAlignExtension = () => {
      return EditorView.theme({
        ".cm-line, .cm-content": {
          textAlign: props.textAlign,
        },
      });
    };

    // 初始化编辑器
    const initEditor = () => {
      // 如果编辑器已经存在，先销毁
      if (editorView.value) {
        toRaw(editorView.value).destroy();
      }

      // 创建新编辑器实例
      editorView.value = new EditorView({
        doc: props.modelValue,
        extensions: [
          basicSetup, // 基本功能扩展
          getLanguageExtension(props.language), // 语言支持
          getThemeExtension(props.theme), // 主题支持
          getTextAlignExtension(), // 添加文本对齐扩展
          // 监听编辑器内容变化
          EditorView.updateListener.of((update) => {
            if (update.docChanged) {
              const content = update.state.doc.toString();
              emit("update:modelValue", content);
              emit("change", content); // 触发自定义事件
            }
          }),
          // 确保编辑器占满可用高度
          EditorView.theme({
            "&": {
              height: "100%",
            },
            ".cm-scroller": {
              overflow: "auto",
              height: "100%",
            },
            ".cm-content, .cm-gutter": {
              minHeight: "100%",
            },
          }),
        ],
        parent: editorContainer.value,
      });
    };

    // 在光标位置插入文本的函数
    const doInset = (text) => {
      if (!editorView.value) return;

      // 获取当前光标位置和选择范围
      const state = editorView.value.state;
      const selection = state.selection;
      const from = selection.main.from;
      const to = selection.main.to;

      // 创建事务来插入文本
      editorView.value.dispatch({
        changes: { from, to, insert: text },
        selection: { anchor: from + text.length }, // 将光标移动到插入文本后
      });

      // 聚焦编辑器
      editorView.value.focus();
    };

    // 监听父组件传递的 modelValue 变化
    watch(
      () => props.modelValue,
      (newValue) => {
        // 获取原始编辑器实例
        const view = toRaw(editorView.value);
        if (view) {
          // 获取当前文档
          const currentDoc = view.state.doc.toString();
          // 如果内容不同，则更新编辑器内容
          if (newValue !== currentDoc) {
            view.dispatch({
              changes: {
                from: 0,
                to: currentDoc.length,
                insert: newValue,
              },
            });
          }
        }
      }
    );

    // 组件挂载时初始化编辑器
    onMounted(() => {
      initEditor();
      editorContainer.value.addEventListener("dragover", (e) => {
        e.preventDefault();
      });
      editorContainer.value.addEventListener("drop", (e) => {
        e.preventDefault();
        const sptStr = e.dataTransfer.getData("item");
        if (sptStr) {
          const sptObj = JSON.parse(sptStr);
          if (sptObj && sptObj.type == "grammer") {
            doInset(sptObj.value);
          }
        }
      });

      ProjectRef.funcObj.otherFunc.insertCoder = (xText) => {
        doInset(xText);
      };
    });

    // 组件卸载时销毁编辑器
    onUnmounted(() => {
      if (editorView.value) {
        toRaw(editorView.value).destroy();
      }
    });

    return {
      editorContainer,
      doInset,
    };
  },
};
</script>

<style scoped></style>
