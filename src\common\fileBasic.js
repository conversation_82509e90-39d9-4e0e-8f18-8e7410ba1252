import {reactive,ref,onMounted,onUnmounted,getCurrentInstance} from 'vue'

export function useFileBase(xItem){
	const { emitter } = getCurrentInstance().appContext.config.globalProperties
	let onEnterActive = null
	let onLeaveActive = null 
	
	function onFWActived(func){
		onEnterActive = func
	}
	
	function onFWUnActived(func){
		onLeaveActive = func
	}
	
	const onChangeFile = (res)=>{
		if(onEnterActive   && xItem.fileItem.filePath == res.newPath){
			onEnterActive()
		}
		if(onLeaveActive && xItem.fileItem.filePath == res.oldPath){
			onLeaveActive()
		}
	}

	onMounted(()=>{
		emitter.on("onChangeFile",onChangeFile)
		if(onEnterActive) onEnterActive()
	})
	
	onUnmounted(()=>{
		emitter.off("onChangeFile",onChangeFile)
	})
	
	return {onFWActived,onFWUnActived}
}