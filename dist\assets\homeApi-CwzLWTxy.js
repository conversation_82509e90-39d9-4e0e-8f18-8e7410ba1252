import{a9 as e}from"./index-Dgb0NZ1J.js";const r=t=>e.post("/api/project/list",t),o=t=>e.get("/api/dbstore/list",{params:t}),n=t=>e.post("/yhyApi/project/applicationlist",t),p=t=>e.get(`/api/dynamictable/list/${t}/1/100`),c=t=>e.get(`/api/dbstore/get/${t}`),d=t=>e.get(`/api/dynamictable/get/${t}`),i=t=>e.post("/api/dynamictable/create",t),u=t=>e.delete(`/api/dbstore/delete/${t}`),l=t=>e.post("/api/dbstore/create",t),b=t=>e.put("/api/dbstore/update",t),h=t=>e.put("/api/dynamictable/update",t),y=t=>e.delete(`/api/dynamictable/delete/${t}`),j=t=>e.post(`/api/authDbProject/listByDbStoreId/${t}`),m=t=>e.post("/api/authDbProject/saveAuthDbProject",t),U=t=>e.delete(`/api/authDbProject/deleteById/${t}`),$=t=>e.post("/api/authDbUser/listByPage",t),D=t=>e.post("/api/authDbUser/addAuthDbUser",t),g=t=>e.delete(`/api/authDbUser/deleteById/${t}`),B=t=>e.post("/api/authDbUser/batchUpdateAuthDbUser",t),A=t=>e.get(`/api/dynamiccolumn/list/${t}/1/300`),I=t=>e.post(`/api/dynamicIndex/queryList/${t}`),P=(t,a)=>e.post(`/api/dynamicIndex/createOrUpdateIndexBatch/${t}`,a),x=t=>e.delete(`/api/dynamicIndex/deleteById/${t}`),v=t=>e.delete(`/api/dynamiccolumn/delete/${t}`),L=(t,a)=>e.post(`/api/dynamiccolumn/createOrUpdateBatch/${t}`,a),C=t=>e.post("/api/dynamictable/generate-sql",t),T=t=>e.post("/api/project/add",t),f=t=>e.post(`/api/project/update/${t.id}`,t),q=t=>e.get(`/api/project/deleteById/${t}`),S=t=>e.post(`/api/project/findUsersByPid/${t}`,{pageNo:1,pageSize:100}),z=t=>e.post(`/api/project/deleteAuthUserById/${t}`),M=t=>e.post("/api/project/addUpdateAuth",t),O=t=>e.post("/api/project/authUserByMobile",t),k=t=>e.post(`/api/project/toClone/${t}`),w=t=>e.post(`/api/project/doClone/${t.id}`,t);export{x as A,P as B,C,c as D,p as E,y as F,h as G,i as H,d as I,O as a,z as b,k as c,q as d,T as e,M as f,w as g,S as h,o as i,b as j,l as k,n as l,j as m,U as n,$ as o,r as p,g as q,u as r,m as s,D as t,f as u,B as v,A as w,v as x,L as y,I as z};
