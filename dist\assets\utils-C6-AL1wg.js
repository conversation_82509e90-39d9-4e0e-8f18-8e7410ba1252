import{E as c}from"./index-DzgKufUD.js";import{a2 as i,E as u}from"./index-Dgb0NZ1J.js";const f=(s="您有未保存的更改，是否保存?")=>new Promise(async(o,l)=>{c({title:"提示",message:s,showCancelButton:!0,confirmButtonText:"保存",cancelButtonText:"不保存",distinguishCancelAndClose:!0,showClose:!1,closeOnClickModal:!1,closeOnPressEscape:!1}),await i();const n=document.querySelector(".el-message-box");if(n){const t=n.querySelector(".el-message-box__btns");if(t){for(;t.firstChild;)t.removeChild(t.firstChild);const e=document.createElement("button");e.className="el-button el-button--primary",e.innerHTML="<span>保存</span>",e.onclick=()=>{o(),c.close()},t.appendChild(e);const a=document.createElement("button");a.className="el-button el-button--default",a.innerHTML="<span>不保存</span>",a.onclick=()=>{l(),c.close()},t.appendChild(a);const r=document.createElement("button");r.className="el-button el-button--default",r.innerHTML="<span>取消</span>",r.onclick=()=>{c.close()},t.appendChild(r)}}}),p=(s,o="json.txt")=>{try{const l=JSON.parse(s),n=JSON.stringify(l,null,4),t=new Blob([n],{type:"text/plain"}),e=document.createElement("a");e.href=URL.createObjectURL(t),e.download=o,document.body.appendChild(e),e.click(),document.body.removeChild(e),URL.revokeObjectURL(e.href),u({type:"success",message:"导出成功"})}catch(l){console.error("Invalid JSON string:",l),alert("导出失败：JSON格式不正确")}},b=s=>{if(!Array.isArray(s)||s.length===0)return!1;const o=s[0],l=["name","fullname","remark","mainColor"];for(const n in o)if((o[n]===null||o[n]===void 0||o[n]==="")&&n!=="remark"&&n!=="desc")return!1;for(let n=1;n<s.length;n++){const t=s[n];for(const e in t)if((t[e]===null||t[e]===void 0||t[e]==="")&&e!=="remark"&&e!=="desc")return!1;for(const e of l)if(t[e]!==o[e])return!1}return!0};export{f as c,p as e,b as v};
