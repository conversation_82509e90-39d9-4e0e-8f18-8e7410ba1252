import{E as C}from"./el-button-B8TCQS4u.js";import{E as F,a as T}from"./el-table-column-S0LD8t0E.js";import"./el-checkbox-DnOi5Qa-.js";import"./el-scrollbar-DLTsEwUl.js";import{E as I,a as L}from"./el-select-DNFSsBSe.js";import{E as z,a as D}from"./el-form-item-Beaw8WQV.js";import{p as j,c as N}from"./list_icon_comput-BNFIRqSF.js";import{l as O}from"./homeApi-CwzLWTxy.js";import{e as p,d as R,B as q,c as d,a as o,b as t,f as l,F as _,h as S,o as s,C as h,g as v,t as a,n as U,k as G}from"./index-Dgb0NZ1J.js";import{_ as M}from"./plugin-vue_export-helper-DlAUqK2U.js";import"./index-CINbulG0.js";import"./isEqual-CwZW0B1R.js";import"./vnode-CV7pw3rS.js";import"./castArray-BpiBxx45.js";const $={class:"flex_box filter_head"},A=["src"],H={class:"flex_box",style:{"justify-content":"flex-start"}},J={style:{"font-weight":"600","font-size":"18px",color:"#212121"}},K={style:{display:"flex","align-items":"center","margin-left":"24px"}},P={key:0,src:j,style:{width:"20px",height:"20px"}},Q={key:1,src:N,style:{width:"20px",height:"20px"}},W={style:{margin:"0 8px 0 4px","font-size":"16px",color:"#666666"}},X={class:"desc"},Y={class:"desc"},Z={style:{"font-size":"16px",color:"#999999"}},ee={__name:"projectReleaseList",setup(te){const x=S(),w=p([{label:"全部",value:"0"},{label:"手机",value:"1"},{label:"电脑",value:"2"}]),b=p([{label:"全部",value:"0"},{label:"直属",value:"1"},{label:"参与",value:"2"}]),r=R({type:"0",ownerType:""}),u=p([]),g=async()=>{try{const i=await O(r);u.value=i.data}catch{}},E=i=>{x.push({path:"/home/<USER>",query:{id:i.id}})};return q(()=>{g()}),(i,n)=>{const m=L,f=I,y=D,k=z,c=T,V=C,B=F;return s(),d(_,null,[o("div",$,[t(k,{model:r,inline:!0},{default:l(()=>[t(y,{label:"类型"},{default:l(()=>[t(f,{modelValue:r.type,"onUpdate:modelValue":n[0]||(n[0]=e=>r.type=e),placeholder:"请选择",style:{width:"111px"}},{default:l(()=>[(s(!0),d(_,null,h(w.value,e=>(s(),v(m,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(y,{label:"所属"},{default:l(()=>[t(f,{modelValue:r.type,"onUpdate:modelValue":n[1]||(n[1]=e=>r.type=e),placeholder:"请选择",style:{width:"111px"}},{default:l(()=>[(s(!0),d(_,null,h(b.value,e=>(s(),v(m,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),t(B,{data:u.value,"show-header":!1},{default:l(()=>[t(c,{width:"112px"},{default:l(e=>[o("img",{style:{width:"88px",height:"88px","border-radius":"8px"},src:e.row.pic},null,8,A)]),_:1}),t(c,null,{default:l(e=>[o("div",H,[o("div",J,a(e.row.applicationId)+"   |   "+a(e.row.title),1),o("div",K,[e.row.category===1?(s(),d("img",P)):(s(),d("img",Q)),o("span",W,a(e.row.category===1?"手机":"电脑"),1),o("div",{class:U(e.row.ower===0?"tag_primary":"tag_primary tag_success")},a(e.row.ower===0?"直属":"参与"),3)])]),o("div",X," 发布："+a(e.row.creator)+"  "+a(e.row.createTime),1),o("div",Y,"版本号："+a(e.row.versioncode),1)]),_:1}),t(c,null,{default:l(e=>[o("div",Z,a(e.row.description),1)]),_:1}),t(c,{width:"100px"},{default:l(({row:e})=>[t(V,{type:"primary",color:"#2B6BFF",onClick:le=>E(e)},{default:l(()=>n[2]||(n[2]=[G(" 布署 ")])),_:2,__:[2]},1032,["onClick"])]),_:1})]),_:1},8,["data"])],64)}}},he=M(ee,[["__scopeId","data-v-19a8bc3c"]]);export{he as default};
