<template>
	<div class="content" style="display: flex;justify-content: space-between;align-items: stretch;flex-direction: column;">
		<div  class="top-part" :style="props.TBase.getLayoutStyle()">
			<TComponents v-for="(childItem,r) in props.TItem.children" :key= "r" :TItem="childItem" :TParent="props.TItem" :TIndex="r"
			  v-show="getCurrentPage() == r"></TComponents>
		</div>
		<div class="bottom-part" :style="{backgroundColor: props.TItem.attribute.tabs.navigateBackground}">
			<div v-for="(item,index) in props.TItem.attribute.tabs.pages.items" :key="index" class="item-box" @click.stop="clickPage(index)">
				<div style="display: flex;justify-self: center;align-items: center;flex-direction: column;position: relative;">
					<img :src="getItemImgUrl(index)" 
				     class="img-top" >
			  	  	<div class="item-title" :style="{color:getItemFontColor(index),fontSize:getItemFontSize(index)+'px'}">{{ item.title }}</div>
					<div class="budge-num" v-if="item.badgeNum>0">{{ item.badgeNum }}</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
	import {reactive,ref,onMounted,inject,watch} from 'vue'
	const pjResouceMap = inject("pjResouceMap")
	const ProjectRef = inject("ProjectRef")


	
	const props = defineProps({
		TItem:{
			type:Object,
			default:{}
		},
		TParent:{
			type:Object,
			default:{id:'10001'}
		},
		TBase:{
			type:Object,
			default:{
			}
		}
	})
	
	const currentPageId = ref(0)

	function getSrc(srcValue){
		
		if(srcValue.trim().split('.')[0] =='resource'){
			return pjResouceMap.getResourceUrl(srcValue.trim())
		}else{
			return srcValue
		}
		
	}
	function getCurrentPage(){
		return props.TItem.attribute.tabs.currentPage >= 0 && props.TItem.attribute.tabs.currentPage<props.TItem.attribute.tabs.pages.items.length?props.TItem.attribute.tabs.currentPage:0
	}
	function getItemImgUrl(index){
		if(index==getCurrentPage()){
			return getSrc(props.TItem.attribute.tabs.pages.items[index].activePath)
		}else{
			return getSrc(props.TItem.attribute.tabs.pages.items[index].deactivePath)
		}
	}
	function getItemFontColor(index){
		if(index==getCurrentPage()){
			if(props.TItem.attribute.tabs.pages.items[index].activeColorThemeOpen){
				return ProjectRef.funcObj.getValueByThemeKey(props.TItem.attribute.tabs.pages.items[index].activeColorTheme)
			}else{
				return props.TItem.attribute.tabs.pages.items[index].activeColor
			}

		}else{
			if(props.TItem.attribute.tabs.pages.items[index].deactiveColorThemeOpen){
				return ProjectRef.funcObj.getValueByThemeKey(props.TItem.attribute.tabs.pages.items[index].deactiveColorTheme)
			}else{
				return props.TItem.attribute.tabs.pages.items[index].deactiveColor
			}
		}
	}
	function getItemFontSize(index){
		if(index==getCurrentPage()){
			return props.TItem.attribute.tabs.pages.items[index].activeFontSize
		}else{
			return props.TItem.attribute.tabs.pages.items[index].deactiveFontSize
		}
	}
	
	function clickPage(index){

		props.TItem.attribute.tabs.currentPage = index
	}

	function createTabPage(parentId){
		return {
			"w": 100,
			"name":"Page"+props.TItem.children.length + 1,
			"h": 100,
			"model": "TBox",
			"id": "id_" + (new Date().getTime()),
			"value": "TabPage体",
			"key": "TBox",
			"group": "layout",
			"scopeType": 0,
			"attribute": {
				"hover": {
					"used": false
				},
				"active": {
					"used": false
				},
				"background": {
					"backgroundImageOpen": false,
					"backgroundImageUrl": "",
					"backgroundColor": "#ffffff",
					"backgroundExpression": "",
					"theme": false,
					"themeKey": "",
					"gradientOpen": false,
					"gradientColor2": "",
					"gradientAngle": 90
				},
				"box": {
					"paddingLeft": 0,
					"paddingRight": 0,
					"paddingTop": 0,
					"paddingBottom": 0,
					"marginLeft": 0,
					"marginRight": 0,
					"marginTop": 0,
					"marginBottom": 0,
					"radiusTheme": false,
					"radiusThemeKey": "",
					"radiusLeftTop": 0,
					"radiusRightTop": 0,
					"radiusLeftBottom": 0,
					"radiusRightBottom": 0,
					"opacity": 100,
					"scale": 100,
					"cursorType": "default",
					"borderTheme": false,
					"borderThemeKey": "",
					"borderSize": 1,
					"borderStyle": "solid",
					"borderColor": "#2b6bff",
					"borderExpression": "",
					"borderLeft": true,
					"borderRight": true,
					"borderTop": true,
					"borderBottom": true,
					"shadowType": "none",
					"shadowColor": "#e2e2e2",
					"shadowOffsetX": 0,
					"shadowOffsetY": 0,
					"shadowBlur": 0,
					"shadowSpread": 0
				},
				"rotation": 0,
				"fixed": {
					"fixedOpen": false,
					"fixedMode": "free"
				},
				"layout": {
					"layoutDirection": "absolute",
					"hAlign": "align-center",
					"vAlign": "align-center",
					"scrollHOpen": false,
					"scrollVOpen": false
				},
				"css": {
					"str": ""
				}
			},
			"x": 0,
			"children": [],
			"lastZindex": 400,
			"firstZindex": 400,
			"y": 0,
			"wUnit": "%",
			"hUnit": "%",
			"visible": true,
			"enabled": true,
			"parent": {
				"id": parentId
			},
			"zindex": 400,
			"visibleExpression": "",
			"enableExpression": "",
			"checked": true,
			"checkedExpression": "",
			"valueExpression": "",
			"whOffsetOpen": false,
			"offsetW": 0,
			"offsetH": 0,
			"flexOpen": false,
			"flex": 1,
			"others": [{
				"name": "overflow",
				"type": "List",
				"label": "溢出处理方式",
				"value": "hidden",
				"listData": [{
					"name": "内容可溢出",
					"value": "visible"
				}, {
					"name": "内容溢出裁剪",
					"value": "hidden"
				}, {
					"name": "内容溢出时，始终显示滚动条",
					"value": "scroll"
				}, {
					"name": "内容溢出时，自动显示滚动条",
					"value": "auto"
				}]
			}],
			"triggers": {
				"onClicked": {
					"type": "none"
				},
				"onDbClicked": {
					"used": "none"
				}
			},
			"TIndex": 0
		}
	}
	
	props.TItem.attribute.tabs.addItem = addItem
	props.TItem.attribute.tabs.removeItem = removeItem
	
	onMounted(()=>{
		props.TItem.parent = props.TParent
		
		if(props.TItem.children.length == 0){
			if(props.TItem.attribute.tabs.pages.items.length > 0){
				for(let row = 0;row<props.TItem.attribute.tabs.pages.items.length;row++){
					props.TItem.children.push(createTabPage(props.TItem.id))
				}
			}
		}
		
	})
	
	function addItem(){
		console.log("additem one tab")
		props.TItem.children.push(createTabPage(props.TItem.id))
		props.TItem.attribute.tabs.currentPage = props.TItem.attribute.tabs.pages.items.length - 1
	}
	
	function removeItem(index){
		console.log("remove item "+index)
		if(index >= 0 && index < props.TItem.children.length ){
			props.TItem.children.splice(index,1)
		}
		
	}
</script>

<style lang="scss" scoped>
	.content {
		height:calc(100% - 3px);
		width:calc(100% - 3px);
		background-color: transparent;
		border:0px;
		
		.top-part {
			flex:1;
			position:static;
		}
		.bottom-part {
			height:50px;
			background-color: #eAeaea;
			display: flex; 
			justify-content: space-between;
			align-items: stretch;
			
			
			.item-box{
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				// border:1px solid red;
				position: relative;
				flex:1;
				// padding: 2px 10px;
				.img-top{
					width:24px;
					height:24px;
				}
				.item-title{
					font-size: 14px;
					color: #B3B3B3;
					margin-top: 2px;
				}
				.budge-num {
					position:absolute;
					right:-15px;
					top:-5px;
					background-color: red;
					color:white;
					border-radius: 30px;
					padding: 1px 6px;
					font-size:12px;
					text-align: center;
					border: 2px solid white;
				}
				&:hover{
					cursor: pointer;
				}
			}
		}
	}
</style>