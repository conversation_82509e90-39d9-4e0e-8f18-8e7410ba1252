import {reactive,ref,inject,toRaw } from 'vue'
import tNoneImg from "@/assets/images/tNone.png";
import tUnderlineImg from "@/assets/images/tUnderline.png";
import tDelImg from "@/assets/images/tDel.png";
import aLeft from "@/assets/images/aLeft.png";
import aCenter from "@/assets/images/aCenter.png";
import aRight from "@/assets/images/aRight.png";
import aJustify from "@/assets/images/aJustify.png";

export function useAttributeSelect(){
	
	const systemFonts = ref([{
			label: "微软雅黑",
			value: "Microsoft YaHei",
		},
		{
			label: "黑体",
			value: "SimHei",
		},
		{
			label: "宋体",
			value: "SimSun",
		},
		{
			label: "楷体",
			value: "KaiTi",
		},
		{
			label: "仿宋",
			value: "FangSong",
		},
	]);
	
	const fontSizeList = ref([
		12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40,
	]);
	
	const fontWeightList = ref([400, 500, 600, 700, 800, 900]);
	
	const borderStyleList = ref([{
			label: "实线",
			value: "solid",
		},
		{
			label: "虚线",
			value: "dashed",
		},
		{
			label: "点线",
			value: "dotted",
		},
	]);
	
	const textAlignList = ref([{
			label: "左对齐",
			value: "left",
			icon: aLeft,
		},
		{
			label: "居中",
			value: "center",
			icon: aCenter,
		},
		{
			label: "右对齐",
			value: "right",
			icon: aRight,
		},
		{
			label: "两端对齐",
			value: "justify",
			icon: aJustify,
		},
	]);
	
	const textDecorationList = ref([{
			label: "无",
			value: "none",
			icon: tNoneImg,
		},
		{
			label: "下划线",
			value: "underline",
			icon: tUnderlineImg,
		},
		{
			label: "删除线",
			value: "line-through",
			icon: tDelImg,
		},
	]);
	
	const shadowList = ref([
		{
			label: "无",
			value: "none",
		},
		{
			label: "外阴影",
			value: "outset",
		},
		{
			label: "内阴影",
			value: "inset",
		},
	]);
	const layoutDirectionList = ref([
		{
			label: "自由布局",
			value: "absolute",
		},
		{
			label: "纵向布局",
			value: "column",
		},
		{
			label: "横向布局",
			value: "row",
		},
	]);
	const layoutJustifyList = ref([{
			label: "左对齐",
			value: "flex-start",
		},
		{
			label: "居中",
			value: "center",
		},
		{
			label: "右对齐",
			value: "flex-end",
		},
	]);
	const cursorTypeList = ref([{
			label: "默认",
			value: "default",
		},
		{
			label: "手型",
			value: "pointer",
		},
		{
			label: "移动",
			value: "move",
		},
		{
			label: "文本",
			value: "text",
		},
		{
			label: "禁止",
			value: "not-allowed",
		},
	]);
	
	return {systemFonts,fontSizeList,fontWeightList,borderStyleList,textAlignList,textDecorationList,shadowList
	        ,layoutDirectionList,layoutJustifyList,cursorTypeList}
	
}
