<template>
  <div class="content file-box">
    <div class="title-box">
      <el-icon :size="18"> <UploadFilled /></el-icon>
      <div style="margin-left: 10px">管理一个图片资源或者一个视频资源</div>
    </div>
    <div
      class="content-box"
      :style="{
        height: 'calc(100vh - 140px - ' + layoutParam.bottomTabHeight + 'px)',
      }"
    >
      <div class="select-box">
        <el-radio-group v-model="currentData.fileType">
          <el-radio value="image" size="large">图片资源</el-radio>
          <el-radio value="video" size="large">视频资源</el-radio>
        </el-radio-group>
      </div>

      <div class="path-box">
        <div>{{ currentData.fileType == "image" ? "图片" : "视频" }}地址:</div>
        <el-input
          v-model="currentData.urlPath"
          placeholder="输入图片地址 或者 视频地址..."
          clearable
          style="flex: 1; margin: 0px 20px"
        />
        <input
          type="file"
          accept="image/*"
          @change="handleFileSelect"
          ref="chooseImageRef"
          style="display: none"
        />
        <input
          type="file"
          accept="video/*"
          @change="handleFileSelect"
          ref="chooseVideoRef"
          style="display: none"
        />
        <el-button
          type="default"
          icon="PictureFilled"
          v-if="currentData.fileType == 'image'"
          @click.stop="onChooseImage()"
          >上传图片</el-button
        >
        <el-button
          type="default"
          icon="VideoCameraFilled"
          v-else-if="currentData.fileType == 'video'"
          @click.stop="onChooseVideo()"
          >上传视频</el-button
        >
      </div>
      <el-image
        :src="currentData.urlPath"
        lazy
        style="margin: 30px"
        v-if="currentData.fileType == 'image' && currentData.urlPath.length > 0"
      />
      <div
        v-if="currentData.fileType == 'video' && currentData.urlPath.length > 0"
      >
        <video
          ref="videoRef"
          controls
          width="640"
          height="360"
          @play="handlePlay"
          @pause="handlePause"
          @ended="handleEnded"
          :src="currentData.urlPath"
        ></video>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {
  reactive,
  ref,
  onMounted,
  inject,
  onUnmounted,
  toRaw,
  watch,
  nextTick,
} from "vue";
import { useFileBase } from "../../../common/fileBasic";
import {
  apiLoadFileJson,
  apiUpdateFile,
  imageUpload,
  videoUpload,
} from "../../../apis/designer.js";
import { ElMessage, ElMessageBox, ElLoading } from "element-plus";

const fileBase = useFileBase();
const props = defineProps({
  fileItem: {
    type: Object,
    default: {},
  },
});
const layoutParam = inject("layoutParam");
const ProjectRef = inject("ProjectRef");
const pjResouceMap = inject("pjResouceMap")

const currentData = reactive({
  fileId: 0,
  version: 0,
  filePath: "",
  fileType: "image",
  urlPath: "",
});

const chooseImageRef = ref(null);
const chooseVideoRef = ref(null);
const videoRef = ref(null);
const videoSrc = ref("");

const selectedFiles = ref([]);

watch(
  currentData,
  (v) => {
    props.fileItem.isModify = true;
  },
  { deep: true }
);

const handleFileSelect = async (event) => {
  if (event.target.files.length === 0) return;
  const file = event.target.files[0];

  if (currentData.fileType === "image" && file.type.indexOf("image") === -1) {
    alert("请选择图片文件！");
    return;
  }
  if (currentData.fileType === "video" && file.type.indexOf("video") === -1) {
    alert("请选择视频文件！");
    return;
  }

  try {
    const formData = new FormData();
    if (file) {
      formData.append("file", file);
    } else {
      console.error("文件对象为空，无法添加到 FormData");
      return;
    }
    formData.append("source", "2");
    const loadingInstance = ElLoading.service({
      lock: true,
      text: "上传中...",
      background: "rgba(0, 0, 0, 0.7)",
    });
    const res =
      currentData.fileType === "image"
        ? await imageUpload(formData)
        : await videoUpload(formData);
    if (res.code === 200) {
      currentData.urlPath = res.data.split("|")[0];
      loadingInstance.close();
    } else {
      loadingInstance.close();
      ElMessage({
        message: res.message,
        type: "error",
      });
    }
  } catch (error) {
    console.error("上传文件时出错:", error);
  }
};

// 控制视频播放
const playVideo = () => {
  videoRef.value.play();
};

const pauseVideo = () => {
  videoRef.value.pause();
};

// 事件处理
const handlePlay = () => {
  console.log("视频开始播放");
};

const handlePause = () => {
  console.log("视频暂停");
};

const handleEnded = () => {
  console.log("视频播放结束");
};

onMounted(() => {
  props.fileItem.func.saveData = saveData;
  loadFileJson();
});

onUnmounted(() => {
  if (videoSrc.value) {
    URL.revokeObjectURL(videoSrc.value);
  }
});

function saveData(success) {
  let json = toRaw(currentData);
  updateFile(props.fileItem.fileItem.filePath, json, (data) => {
    ElMessage({
      message: "保存成功",
      type: "success",
    });
    props.fileItem.isModify = false;
    if (success) success();
  });
}

function onChooseImage() {
  chooseImageRef.value.click();
}

function onChooseVideo() {
  chooseVideoRef.value.click();
}

async function updateFile(path, json, success) {
  let urlPath = json.urlPath
  let res = await apiUpdateFile({
    projectId: ProjectRef.BasicData.ProjectId,
    version: currentData.version,
    path: path,
    _json: json,
  });
  if (res.code == 200) {
    if (res.data.hasOwnProperty("success") && res.data.success == false) {
      ElMessage({
        message: res.message,
        type: "error",
      });
    } else {
      currentData.version++;
      if (success) success(res);
	  pjResouceMap.setResourceUrl(path,json.urlPath)
    }
  }
}

async function loadFileJson() {
  let param = {
    projectId: ProjectRef.BasicData.ProjectId,
    path: props.fileItem.fileItem.filePath,
  };
  let res = await apiLoadFileJson(param);
  if (res.code == 200) {
    if (res.data.content) {
		currentData.version = res.data.version;
      let tmpJson = JSON.parse(res.data.content);
      if (tmpJson) {
        currentData.fileId = tmpJson.fileId;
        currentData.filePath = tmpJson.filePath;
        currentData.fileType = tmpJson.fileType;
        currentData.urlPath = tmpJson.urlPath;
        nextTick(() => {
          props.fileItem.isModify = false;
        });
      }
    }else{
		currentData.version = res.data.version;
	}
  }
}
</script>

<style lang="scss" scoped>
.content {
  overflow: hidden;
  .title-box {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    background-color: white;
    padding: 8px 20px;
    font-size: 14px;
    border-bottom: 1px solid #e7e7e7;
  }
  .content-box {
    background-color: white;
    overflow-y: auto;
    // border:1px solid red;

    .select-box {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      // border:1px solid red;
      padding: 3px 30px;
    }

    .path-box {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 8px 30px;
    }
  }
}
:deep(.el-button) {
  width: 100px;
}
</style>
