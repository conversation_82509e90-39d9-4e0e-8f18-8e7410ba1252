<template>
  <div class="flex_box">
    <div class="add_box" v-if="!isAdd" @click="isAdd = true">
      <el-button :icon="Plus"></el-button>
      <span>增加授权用户</span>
    </div>
    <div v-else>
      <el-input
        v-model="mobile"
        placeholder="请输入授权账号"
        style="width: 200px"
      />
      <el-button
        type="primary"
        style="margin-left: 12px"
        v-debounce="handleAddUser"
      >
        加入
      </el-button>
      <el-button @click="isAdd = false">取消</el-button>
    </div>
    <el-button
      type="primary"
      v-debounce="handleSave"
      :disabled="authUserList.length === 0"
    >
      保存
    </el-button>
  </div>

  <el-table
    :data="authUserList"
    border
    table-layout="auto"
    :header-cell-style="props.headerCellStyleData"
    :cell-style="props.cellStyleData"
    style="margin-top: 16px"
  >
    <el-table-column prop="id" label="编号" />
    <el-table-column prop="mobile" label="账号" />
    <el-table-column prop="username" label="名称" />
    <el-table-column label="删除">
      <template #default="scope">
        <el-checkbox
          :true-value="1"
          :false-value="0"
          v-model="scope.row.delete"
        />
      </template>
    </el-table-column>
    <el-table-column label="更改">
      <template #default="scope">
        <el-checkbox
          :true-value="1"
          :false-value="0"
          v-model="scope.row.update"
        />
      </template>
    </el-table-column>
    <el-table-column label="新增">
      <template #default="scope">
        <el-checkbox
          :true-value="1"
          :false-value="0"
          v-model="scope.row.insert"
        />
      </template>
    </el-table-column>
    <el-table-column label="查询">
      <template #default="scope">
        <el-checkbox
          :true-value="1"
          :false-value="0"
          v-model="scope.row.query"
        />
      </template>
    </el-table-column>
    <el-table-column label="结构">
      <template #default="scope">
        <el-checkbox
          :true-value="1"
          :false-value="0"
          v-model="scope.row.schema"
        />
      </template>
    </el-table-column>
    <el-table-column label="操作">
      <template #default="scope">
        <el-popconfirm title="确认删除?" @confirm="handleDel(scope.row)">
          <template #reference>
            <img src="@/assets/images/form_icon_del.png" class="results_btn" />
          </template>
        </el-popconfirm>
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup>
import { onMounted, ref, } from "vue";
import { useRoute } from "vue-router";
import { Plus } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import {
  authUsers,
  addAuthUser,
  delAuthUser,
  updateAuthUser,
} from "@/apis/homeApi";

const props = defineProps({
  headerCellStyleData: {
    type: Object,
    default: {},
  },
  cellStyleData: {
    type: Object,
    default: {},
  },
});

const route = useRoute();
const isAdd = ref(false);
const mobile = ref("");
const authUserList = ref([]);

const handleSave = async () => {
  console.log(authUserList.value);
  try {
    await updateAuthUser({
      batchUpdateReqVos: JSON.stringify(authUserList.value),
    });
    ElMessage({
      type: "success",
      message: "保存成功",
    });
    isAdd.value = false;
    fetchAuthUser();
  } catch {
    fetchAuthUser();
  }
};

const fetchAuthUser = async () => {
  try {
    const res = await authUsers({
      dbStoreId: Number(route.query.dbSourceId),
      pageNo: 1,
      pageSize: 10,
    });
    authUserList.value = res.data.items;
  } catch {}
};

const handleAddUser = async () => {
  if (!mobile.value) {
    ElMessage.error("请输入授权账号");
    return;
  }
  try {
    await addAuthUser({
      mobile: mobile.value,
      dbStoreId: Number(route.query.dbSourceId),
    });
    ElMessage({
      type: "success",
      message: "添加成功",
    });
    mobile.value = "";
    fetchAuthUser();
  } catch {}
};

const handleDel = async (row) => {
  try {
    await delAuthUser(row.id);
    ElMessage({
      type: "success",
      message: "删除成功",
    });
    fetchAuthUser();
  } catch {}
};

onMounted(() => {
  fetchAuthUser();
});
</script>

<style lang="scss">
.add_box {
  display: flex;
  align-items: center;
  span {
    display: none;
    margin-left: 12px;
    font-size: 14px;
    color: #999999;
    line-height: 16px;
  }
  .el-button {
    padding: 8px;
  }
  .el-button:hover {
    background-color: #2b6bff;
    border-color: #2b6bff;
    color: #ffffff;
  }
  .el-button:hover + span {
    display: block;
  }
}
</style>
