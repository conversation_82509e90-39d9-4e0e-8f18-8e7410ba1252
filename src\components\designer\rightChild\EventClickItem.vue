<template>
	<div class="event-box">
		<div style="display: flex;justify-content: flex-start;align-items: center;padding-bottom: 6px;
		            width:100%;margin-bottom: 5px;">
			<div  :style="{color: !checkContent() ? '#212121' : '#ababab',width:'230px',textAlign:'left',fontWeight: '600'}">点击事件(onClicked):</div>
			<el-select v-model="props.eventItem.data.type" placeholder="Select" style="width: 100px;" @change="onChangeType">
			  <el-option key="none" label="未启用" value="none" />
			  <el-option key="link" label="跳转链接" value="link" />
			  <el-option key="event" label="执行事件" value="event" />
			  <el-option key="command" label="执行命令" value="command" />
			</el-select>
		    <div style="margin-left:30px;margin-right:5px;color:#8d8d8d;" v-if="props.eventItem.data.type=='link'">目标:</div>
		    <el-select v-model="props.eventItem.data.linked.target" placeholder="Select" style="width: 80px;" v-if="props.eventItem.data.type=='link'">
		      <el-option key="page" label="页面" value="page" />
		      <el-option key="part" label="组件" value="part" />
		      <el-option key="popup" label="弹窗" value="popup" />
		    </el-select>
			
			<div style="margin-left:30px;margin-right:5px;color:#8d8d8d;" v-if="props.eventItem.data.type=='command'">命令:</div>
			<el-select v-model="props.eventItem.data.command.command" placeholder="Select" style="width: 100px;" v-if="props.eventItem.data.type=='command'">
			  <el-option key="closePage" label="关闭页面" value="closePage" />
			  <el-option key="closePopup" label="关闭弹窗" value="closePopup" />
			  <el-option key="setVarValue" label="变量赋值" value="setVarValue" />
			</el-select>
		</div>
		<div v-if="props.eventItem.data.type=='command' && props.eventItem.data.command.command=='setVarValue'" 
		    class="edit-var-btn" @click.stop="onSetVarValue()">
			编辑变量赋值
		</div>
		<div v-if="props.eventItem.data.type=='link'" class="event-item">
			<div class="event-title" :style="{color:props.eventItem.data.clicked && props.eventItem.data.clicked.eventFile.length > 0 ? '#212121' : '#8d8d8d'}">
			    {{props.eventItem.data.linked.target=='page' ? '跳转页面(Page)' : (props.eventItem.data.linked.target=='part' ? '加载组件(Component)' : '弹窗组件(Component)')}}
			</div>
			<div class="event-content" v-if="props.eventItem.data.linked.target=='page'" @dragover.prevent="onDragover" @drop="onPageDrop">
				<input class="event-input" type="text" v-model = "props.eventItem.data.linked.page.pagePath" 
					  :placeholder="'未指定页面，只接收页面文件'" />
				<div class="event-edit" @click.stop="onSetParam('link','page')" v-if="props.eventItem.data.linked.page.pagePath.length > 0">参数</div>
				<div class="event-edit" @click.stop="onPageEdit()">编辑</div>
			</div>
			
			
			<div class="event-content" v-if="props.eventItem.data.linked.target=='part'" @dragover.prevent="onDragover" @drop="onCmptDrop1">
				<input class="event-input" type="text" v-model = "props.eventItem.data.linked.part.componentPath" 
					  :placeholder="'未指定组件，只接收组件文件'" />
				<div class="event-edit" @click.stop="onSetParam('link','part')" v-if="props.eventItem.data.linked.part.componentPath.length > 0">参数</div>
				<div class="event-edit" @click.stop="onPartEdit()">编辑</div>
			</div>
			<div class="event-title" style="color:#8d8d8d" v-if="props.eventItem.data.linked.target=='part'">装载组件名称(ParentComponent)</div>
			<div class="event-content" v-if="props.eventItem.data.linked.target=='part'">
				<input class="event-input" type="text" v-model = "props.eventItem.data.linked.part.parentName"
					  :placeholder="'输入装载组件名称'" />
				<el-radio-group v-model="props.eventItem.data.linked.part.mode" style="width: 160px;">
				      <el-radio value="replace" size="large">替换式</el-radio>
				      <el-radio value="add" size="large">追加式</el-radio>
				    </el-radio-group>
			</div>
			<div class="event-content" v-if="props.eventItem.data.linked.target=='popup'" @dragover.prevent="onDragover" @drop="onCmptDrop2">
				<input class="event-input" type="text" v-model = "props.eventItem.data.linked.popup.componentPath" 
					  :placeholder="'未指定组件，只接收组件文件'" />
				<div class="event-edit" @click.stop="onSetParam('link','popup')" v-if="props.eventItem.data.linked.popup.componentPath.length > 0">参数</div>
				<div class="event-edit" @click.stop="onPopupEdit()">编辑</div>
			</div>
			<div class="event-content" v-if="props.eventItem.data.linked.target=='popup'">
				<div  style="color:#8d8d8d;">占屏比例:</div>
				<input class="event-input" type="number" v-model = "props.eventItem.data.linked.popup.expose" :min="0"  :max="1"
					  placeholder="0-1比率" :step="0.1" style="width:80px;"/>
				<el-select v-model="props.eventItem.data.linked.popup.direct" placeholder="Select" style="width: 120px;">
				  <el-option key="bottom" label="下方弹出" value="bottom" />
				  <el-option key="left" label="左方弹出" value="left" />
				  <el-option key="top" label="上方弹出" value="top" />
				  <el-option key="right" label="右方弹出" value="right" />
				  <el-option key="center" label="中间弹出" value="center" />
				</el-select>
				
			</div>
		</div>
		
		
		<!-- <div v-if="props.eventItem.type=='command'" class="event-item">
			command
		</div> -->
		
		<div v-if="props.eventItem.data.type=='event'" class="event-item" @dragover.prevent="onDragover" @drop="onDrop">
			<div class="event-title" :style="{color:props.eventItem.data.clicked.eventFile.length > 0 ? '#212121' : '#8d8d8d'}">事件文件(eventFile)</div>
			<div class="event-content">
				<input class="event-input" type="text" v-model = "props.eventItem.data.clicked.eventFile" 
					  :placeholder="'未设置事件方法，只接收'+props.eventItem.data.clicked.fileType+'文件'" />
				<div class="event-edit" @click.stop="onSetParam('event','')" v-if="checkShowParam()">参数</div>
				<div class="event-edit" @click.stop="onEdit()">编辑</div>
			</div>
		</div>
	</div>
	<el-dialog v-model="paramMgr.show" title="参数设置" width="800" >
		<div class="param-body">
			<div class="param-header">
				<div class="header-name">参数名</div>
				<div class="header-type">参数名称</div>
				<div class="header-value">参数值</div>
				<div class="oper-btn">
					<el-tooltip content="刷新" placement="top" effect="light" :show-after="500">
					<el-icon style="cursor:pointer;" :size="20" @click.stop="onReadParam()"> <Refresh /></el-icon>
					</el-tooltip>
  			    </div>
			</div>
			<div class="param-row" v-for="(param,index) in paramMgr.params" :key="index" >
				<div class="row-name">
					<el-tooltip :content="param.remark" placement="top-start" effect="dark" :show-after="500">
						<div >{{param.name}}</div>
					</el-tooltip>
				</div>
				<div class="row-type">{{param.type}}</div>
				<div class="row-value">{{param.value}}</div>
				<div class="oper-btn" @click.stop="expressDialog.openShow(index)">
					<el-tooltip content="设置实际值" placement="top" effect="light" :show-after="500">
					<el-icon style="cursor:pointer;" :size="20"> <Paperclip /></el-icon>
					</el-tooltip>
				</div>
			</div>
		</div>
	</el-dialog>
	<ExpressionDialog
	  v-model="expressDialog.show"
	  :globalData = "expressDialog.globalData"
	  :pageData = "expressDialog.pageData"
	  :pageParams = "expressDialog.pageParams"
	  :textareaDefaultValue="expressDialog.initTxt"
	  @success="expressDialog.handleExpressionSuccess"
	/>
</template>

<script lang="ts" setup>
	import {reactive,inject ,toRaw} from 'vue'
	import { ElMessage, ElMessageBox } from 'element-plus'
	import {apiLoadFileJson} from '../../../apis/designer.js'
	import {useExplainVar} from '../../../common/useExplainVar.js'
	
	const {parse} = useExplainVar()
	const props = defineProps({
		eventItem:{
			type:Object,
			default:{}
		},
		targetName:{
			type:String,
			default:""
		}
	})
	const ProjectRef = inject("ProjectRef")
	const FileScript = inject("FileScript")
	
	const expressDialog = reactive({
		show:false,
		initTxt:"",
		index:0,
		globalData:[],
		pageData:[],
		pageParams:[]
	})
	
	function checkContent(){
		if(props.eventItem.data.hasOwnProperty('type')){
			return props.eventItem.data.type =='none'
		}
		return false
	}
	
	expressDialog.handleExpressionSuccess = (data)=>{
		console.log(data)
		paramMgr.params[expressDialog.index].value = data
		expressDialog.show = false
	}
	
	expressDialog.openShow = (index)=>{
		expressDialog.initTxt = paramMgr.params[index].value
		expressDialog.index = index
		expressDialog.globalData = parse(ProjectRef.BasicData.globalData.varText,'global')
		expressDialog.pageData = parse(FileScript.instance.varText,"PageData")
		expressDialog.pageParams = FileScript.params
		expressDialog.show = true
	}
	
	const paramMgr = reactive({
		show:false,
		tmpPath:"",
		params:[
			// {
			// 	name:'name',
			// 	type:'string',
			// 	value:"",
			// 	remark:""
			// }
		]
	})
	
	function onChangeType(typeValue){
		if(typeValue=='none'){
			if(props.eventItem.data.hasOwnProperty('linked')){
				delete props.eventItem.data.linked
			}
			if(props.eventItem.data.hasOwnProperty('command')){
				delete props.eventItem.data.command
			}
			if(props.eventItem.data.hasOwnProperty('clicked')){
				delete props.eventItem.data.clicked
			}
		}else if(typeValue=='link'){
			if(props.eventItem.data.hasOwnProperty('command')){
				delete props.eventItem.data.command
			}
			if(props.eventItem.data.hasOwnProperty('clicked')){
				delete props.eventItem.data.clicked
			}
			if(!props.eventItem.data.linked){
			   props.eventItem.data.linked = JSON.parse(JSON.stringify(toRaw(props.eventItem.init.linked)))
			}
		}else if(typeValue=='event'){
			if(props.eventItem.data.hasOwnProperty('linked')){
				delete props.eventItem.data.linked
			}
			if(props.eventItem.data.hasOwnProperty('command')){
				delete props.eventItem.data.command
			}
			if(!props.eventItem.data.clicked){
			   props.eventItem.data.clicked = JSON.parse(JSON.stringify(toRaw(props.eventItem.init.clicked)))
			}
		}else if(typeValue=='command'){
			if(props.eventItem.data.hasOwnProperty('linked')){
				delete props.eventItem.data.linked
			}
			if(props.eventItem.data.hasOwnProperty('clicked')){
				delete props.eventItem.data.clicked
			}
			if(!props.eventItem.data.command){
			   props.eventItem.data.command = JSON.parse(JSON.stringify(toRaw(props.eventItem.init.command)))
			}
		}
	}
	
	function onSetVarValue(){
		props.eventItem.data.command.varText = "xxxx"
	}
	
	function onDragover(e){
		e.preventDefault()
	}
	function checkShowParam(){
		if(props.eventItem.data.clicked.eventFile && props.eventItem.data.clicked.eventFile.length > 0){
			return true
		}
		return false
	}
	function onDrop(e){
		const res = e.dataTransfer.getData("treeItem")
		if(res){
			if(res.split(".")[0] == props.eventItem.data.clicked.fileType){
				props.eventItem.data.clicked.eventFile = res
			}else{
				ElMessage({type: 'warning',message: '文件类型错误，必须是 [ 事件 ] 类型文件',})
			}
		}
	}
	function onPageDrop(e){
		const res = e.dataTransfer.getData("treeItem")
		if(res){
			if(res.split(".")[0] == 'page'){
				props.eventItem.data.linked.page.pagePath = res
			}else{
				ElMessage({type: 'warning',message: '文件类型错误，必须是 [ 页面 ] 类型文件',})
			}
		}
	}
	function onCmptDrop1(e){
		const res = e.dataTransfer.getData("treeItem")
		if(res){
			if(res.split(".")[0] == 'component'){
				props.eventItem.data.linked.part.componentPath = res
			}else{
				ElMessage({type: 'warning',message: '文件类型错误，必须是 [ 组件 ] 类型文件',})
			}
		}
	}
	function onCmptDrop2(e){
		const res = e.dataTransfer.getData("treeItem")
		if(res){
			if(res.split(".")[0] == 'component'){
				props.eventItem.data.linked.popup.componentPath = res
			}else{
				ElMessage({type: 'warning',message: '文件类型错误，必须是 [ 组件 ] 类型文件',})
			}
		}
	}
	function onPageEdit(){
		if(props.eventItem.data.linked.page.pagePath.length > 0){
			let pathArray = props.eventItem.data.linked.page.pagePath.split(".")
			if(pathArray[0]!='page' || pathArray[pathArray.length - 1] !='vpg'){
				ElMessage({type: 'warning',message: '当前文件路径不是一个有效的页面路径！',})
				return
			}

			let fileList = ProjectRef.BasicData.fileData.pageFiles
			for(let f=0;f<fileList.length;f++){
				if(fileList[f].filePath==props.eventItem.data.linked.page.pagePath){
					ProjectRef.funcObj.openFile(props.eventItem.data.linked.page.pagePath)
					return
				}
			}
			ElMessageBox.alert('未找到页面，刷新一下左边页面树的数据后再试~', '提示', {
			    confirmButtonText: '确定',
			  })
		}else{
            ElMessageBox.alert('还未设置页面，可从左边页面树上拖拽一个页面放入此处', '提示', {
                confirmButtonText: '确定',
              })
		}
	}
	
	function onPartEdit(){
		if(props.eventItem.data.linked.part.componentPath.length > 0){
			let pathArray = props.eventItem.data.linked.part.componentPath.split(".")
			if(pathArray[0]!='component' || pathArray[pathArray.length - 1] !='cpt'){
				ElMessage({type: 'warning',message: '当前文件路径不是一个有效的组件路径！',})
				return
			}
		
			let fileList = ProjectRef.BasicData.fileData.componentFiles
			for(let f=0;f<fileList.length;f++){
				if(fileList[f].filePath==props.eventItem.data.linked.part.componentPath){
					ProjectRef.funcObj.openFile(props.eventItem.data.linked.part.componentPath)
					return
				}
			}
			ElMessageBox.alert('未找到组件文件，刷新一下左边组件树的数据后再试~', '提示', {
				    confirmButtonText: '确定',
				  })
		}else{
			ElMessageBox.alert('还未设置组件文件，可从左边组件树上拖拽一个组件文件放入此处', '提示', {
				confirmButtonText: '确定',
			  })
		}
	}
	
	function onPopupEdit(){
		if(props.eventItem.data.linked.popup.componentPath.length > 0){
			let pathArray = props.eventItem.data.linked.popup.componentPath.split(".")
			if(pathArray[0]!='component' || pathArray[pathArray.length - 1] !='cpt'){
				ElMessage({type: 'warning',message: '当前文件路径不是一个有效的组件路径！',})
				return
			}
		
			let fileList = ProjectRef.BasicData.fileData.componentFiles
			for(let f=0;f<fileList.length;f++){
				if(fileList[f].filePath==props.eventItem.data.linked.popup.componentPath){
					ProjectRef.funcObj.openFile(props.eventItem.data.linked.popup.componentPath)
					return
				}
			}
			ElMessageBox.alert('未找到组件文件，刷新一下左边组件树的数据后再试~', '提示', {
				    confirmButtonText: '确定',
				  })
		}else{
			ElMessageBox.alert('还未设置组件文件，可从左边组件树上拖拽一个组件文件放入此处', '提示', {
				confirmButtonText: '确定',
			  })
		}
	}
	
	function onEdit(){
		if(props.eventItem.data.clicked.eventFile.length > 0){
			let pathArray = props.eventItem.data.clicked.eventFile.split(".")
			if(props.eventItem.data.clicked.fileType=='methrod'){
				if(pathArray[0]!='methrod' || pathArray[pathArray.length - 1] !='mhd'){
					ElMessage({type: 'warning',message: '当前文件路径不是一个有效的函数路径！',})
					return
				}
			}else{
				if(pathArray[0]!='event' || pathArray[pathArray.length - 1] !='evt'){
					ElMessage({type: 'warning',message: '当前文件路径不是一个有效的事件路径！',})
					return
				}
			}
			// 使用通用的文件检查和打开函数
			if(ProjectRef.funcObj.otherFunc && ProjectRef.funcObj.otherFunc.checkAndOpenFileWithRefresh){
				ProjectRef.funcObj.otherFunc.checkAndOpenFileWithRefresh(
					props.eventItem.data.clicked.eventFile,
					props.eventItem.data.clicked.fileType,
					() => {
						// 文件不存在时的回调
						ElMessageBox.confirm('当前文件没有找到，是否新创建脚本文件？','提示',
						    {
						      confirmButtonText: '创建',
						      cancelButtonText: '放弃',
						      type: 'warning',
						    }
						  )
						    .then(() => {
						       ProjectRef.funcObj.createFile(props.eventItem.data.clicked.eventFile,(data)=>{
							   if(data.code==200){
								   ProjectRef.funcObj.sendLog("创建文件：" + props.eventItem.data.clicked.eventFile)
								   ProjectRef.funcObj.openFile(props.eventItem.data.clicked.eventFile)
							   }
						   })
						    })
						    .catch(() => {

						    })
					}
				)
			}
		}else{
			let fileArray = FileScript.filePath.split(".")
			if((fileArray[0]=='page' && fileArray[fileArray.length - 1]=='vpg') 
			     || (fileArray[0]=='component' && fileArray[fileArray.length - 1]=='cpt')){
					 
				ElMessageBox.confirm('当前 [ '+props.eventItem.data.clicked.name+' ] 还未设置，是否现在创建?','提示',
				    {
				      confirmButtonText: '创建',
				      cancelButtonText: '放弃',
				      type: 'warning',
				    }
				  )
				    .then(() => {
				       let fileName = fileArray[fileArray.length - 2]
						if(props.targetName != fileName){
							fileArray[fileArray.length - 1] = props.targetName
							fileArray.push(props.eventItem.data.clicked.eventName )
						}else{
							fileArray[fileArray.length - 1] = props.eventItem.data.clicked.eventName 
						}
						fileArray[0] = 'event'
						fileArray.push('evt')
				       let newPath = fileArray.join(".")
					   
				       ProjectRef.funcObj.createFile(newPath,(data)=>{
						   if(data.code==200){
							   ProjectRef.funcObj.openFile(newPath)
							   props.eventItem.data.clicked.eventFile = newPath
							   ProjectRef.funcObj.sendLog("创建文件：" + newPath)
						   }else if(data.code==6501){ //创建时文件已经存在了
							   props.eventItem.data.clicked.eventFile = newPath
							   if(!ProjectRef.funcObj.checkFileInList(newPath)){
								   let newArray = newPath.split(".")
									ProjectRef.funcObj.updateNewFile({
										fileId:0,
										title:newArray.slice(-2).join("."),
										type:newArray[0],
										activie:0,
										lock:0,
										lockUser:'',
										filePath:newPath
									})
							   }
							   ProjectRef.funcObj.openFile(newPath)
							   ProjectRef.funcObj.sendLog("设置已存在文件：" + newPath)
						   }
					   })
				    })
				    .catch(() => {
				      
				    })	 

			}
		}
	}
	
	function onSetParam(pos,type){
		if(pos=='event'){
			if(!props.eventItem.data.clicked.params){
				props.eventItem.data.clicked.params = []
			}
			paramMgr.params = props.eventItem.data.clicked.params
			paramMgr.tmpPath = props.eventItem.data.clicked.eventFile
		}else if(pos=='link'){
			if(!props.eventItem.data.linked.params){
				props.eventItem.data.linked.params = []
			}
			paramMgr.params = props.eventItem.data.linked.params
			if(type=='page'){
				paramMgr.tmpPath = props.eventItem.data.linked.page.pagePath
			}else if(type=='part'){
				paramMgr.tmpPath = props.eventItem.data.linked.part.componentPath
			}else if(type=='popup'){
				paramMgr.tmpPath = props.eventItem.data.linked.popup.componentPath
			}
			
		}else if(pos=='command'){
			if(!props.eventItem.data.command.params){
				props.eventItem.data.command.params = []
			}
			paramMgr.params = props.eventItem.data.command.params
		}
		paramMgr.show = true
	}
	
	function onReadParam(){
		loadJsonFile(paramMgr.tmpPath,(data)=>{
			if(data && data.length > 0){
				let tMap = new Map
				data.forEach(cItem=>{
					tMap.set(cItem.name,cItem)
				})
				let srcMap = new Map
				let indexs = []
				if(paramMgr.params.length > 0){
					paramMgr.params.forEach(kItem=>{
						srcMap.set(kItem.name,kItem)
						if(!tMap.has(kItem.name)){
						 indexs.push(paramMgr.params.indexOf(kItem))
						}
					})
				}
				if(indexs.length > 0){
					indexs.forEach(i=>{
						paramMgr.params.splice(i,1)
					})
				}
				data.forEach(item=>{
					if(!srcMap.has(item.name)){
						paramMgr.params.push(item)
					}
				})
			}else{
				paramMgr.params.splice(0,paramMgr.params.length)
			}
		})
	}
	
	async function loadJsonFile(path,success){
		let res = await apiLoadFileJson({
			projectId:ProjectRef.BasicData.ProjectId,
			path:path
		})
		if(res.code==200){
			let scptFile = JSON.parse(res.data.content) 
			let pathArray = path.split(".")
			if(pathArray[0]=='event' || pathArray[0]=='methrod'){
			   if(success && scptFile && scptFile.paramList) success(scptFile.paramList)
			}else if(pathArray[0]=='page' || pathArray[0]=='component'){
				if(scptFile && scptFile.hasOwnProperty("params") && scptFile.params.length > 0){
					success(scptFile.params)
				}
				// console.log(scptFile)
			}
		}
	}
</script>

<style lang="scss" scoped>
	.event-box {
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		border-bottom:1px solid #E6E6E6;
		padding-bottom:10px;
		padding-top:20px;
		min-width: 60px;
		white-space: nowrap;  
		padding-left:10px;
		
		.event-item {
			display: flex;
			flex-direction:column; 
			justify-content: flex-start;
			align-items: flex-start;
		    width:100%;
			border-top: 1px dotted #dedede;
		
			.event-title {
				margin-bottom: 8px;
				font-weight: 600;
			}
			.event-content{
				width:calc(100% - 10px);
				display: flex;
				justify-content: space-between;
				align-items: center;
				// padding-right:10px;
				margin:6px 0px;
				
				.event-input {
					flex:1;
					background-color: #F7F7F7;
					color:#212121;
					border:0px;
					padding:10px 10px;
					font-size: 15px;
					margin-right:10px;
					border-radius: 6px;
				}
				.event-input:focus {
					outline: none;
					box-shadow: 0 0 0 2px transparent; 
				}
				.event-edit {
					border:1px solid #E6E6E6;
					border-radius: 6px;
					color:#212121;
					padding:6px 10px;
					font-size: 15px;
					margin-right: 5px;
					cursor:pointer;
				}
					
				.event-edit:hover{
					border:1px solid #d0d0d0;
				}
				.event-edit:active{
					border:1px solid #d0d0d0;
					font-size: 14px;
				}
			}
		}
	}
	
	.edit-var-btn{
		padding:5px 10px;
		border:1px solid #d4d4d4;
		border-radius: 6px;
		width:calc(100% - 30px);
		cursor:pointer;
	}
	.edit-var-btn:hover{
		box-shadow: inset 0px 0px 3px 0px #35b2f5;
	}
	.edit-var-btn:active{
		box-shadow: inset 0px 0px 3px 0px #35b2f5;
		transform: scale(0.98);
	}
	.param-body {
		margin: 5px 10px 10px 10px;
		// height:300px;
		overflow: auto;
		text-align: top;
		
		.oper-btn {
			width:80px;
			text-align: center;
		}
		
		.param-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			background-color: #e2e2e2;
			text-align: left;
			border:1px solid #cacaca;
			
			.header-name {
				flex:1;
				padding:0px 8px;
				height:30px;
				line-height: 30px;
				border-right:1px solid #cacaca;
			}
			.header-type{
				width:100px;
				height:30px;
				padding:0px 8px;
				line-height: 30px;
				border-right:1px solid #cacaca;
			}
			.header-value {
				flex:1;
				padding:0px 8px;
				height:30px;
				line-height: 30px;
				border-right:1px solid #cacaca;
			}
		}
		.param-row {
			display: flex;
			justify-content: space-between;
			align-items: center;
			text-align: left;
			border-left:1px solid #cacaca;
			border-right:1px solid #cacaca;
			border-bottom:1px solid #cacaca;
			.row-name{
				flex:1;
				padding:0px 8px;
				height:40px;
				line-height: 40px;
				border-right:1px solid #cacaca;
			}
			.row-type{
				width:100px;
				padding:0px 8px;
				height:40px;
				line-height: 40px;
				border-right:1px solid #cacaca;
			}
			.row-value {
				flex:1;
				padding:0px 8px;
				height:40px;
				line-height: 40px;
				border-right:1px solid #cacaca;
			}
		}
	}
</style>