<template>
  <div class="flex_box filter_head">
    <el-form :model="queryData" :inline="true">
      <el-form-item label="类型">
        <el-select
          v-model="queryData.clientType"
          :placeholder="queryData.clientType === '' ? '全部' : '请选择'"
          style="width: 111px"
          @change="handleSelectChange"
        >
          <el-option
            v-for="item in typeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value || ''"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="所属">
        <el-select
          v-model="queryData.type"
          :placeholder="queryData.type === '' ? '全部' : '请选择'"
          style="width: 111px"
          @change="handleSelectChange"
        >
          <el-option
            v-for="item in ownerTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <el-button type="primary" class="create_btn" @click="handleOpenDialog(1)">
      新建项目
      <template #icon>
        <img
          src="@/assets/images/list_icon_add.png"
          style="width: 18px; height: 18px"
        />
      </template>
    </el-button>
  </div>
  <div class="project_list">
    <el-table :data="listData" :show-header="false">
      <el-table-column width="112px">
        <template #default="scope">
          <img
            style="width: 88px; height: 88px; border-radius: 8px"
            :src="scope.row.icon"
          />
        </template>
      </el-table-column>
      <el-table-column>
        <template #default="scope">
          <div style="font-weight: 600; font-size: 18px; color: #212121">
            {{ scope.row.name }}
          </div>
          <div style="display: flex; align-items: center; margin-top: 16px">
            <img
              v-if="scope.row.clientType === 0"
              src="@/assets/images/list_icon_pone.png"
              style="width: 20px; height: 20px"
            />
            <img
              v-else
              src="@/assets/images/list_icon_comput.png"
              style="width: 20px; height: 20px"
            />
            <span style="margin: 0 8px 0 4px; font-size: 16px; color: #666666">
              {{ scope.row.clientType === 0 ? "手机" : "电脑" }}
            </span>
            <div
              :class="
                scope.row.type === 0 ? 'tag_primary' : 'tag_primary tag_success'
              "
            >
              {{ ["直属", "参与", "克隆"][scope.row.type] }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column>
        <template #default="scope">
          <div class="desc">创建于：{{ scope.row.createTime }}</div>
          <div class="desc">版本号：V0.0.001</div>
        </template>
      </el-table-column>
      <el-table-column width="500px">
        <template #default="{ row }">
          <!-- <el-button type="primary" text>
            引导图
            <template #icon>
              <img
                class="btn_icon"
                src="@/assets/images/list_icon_yindao.png"
              />
            </template>
          </el-button> -->
          <el-button
            type="primary"
            text
            @click="handleToDesign(row)"
            :disabled="row.design === 0"
          >
            设计
            <template #icon>
              <img class="btn_icon" src="@/assets/images/list_icon_sheji.png" />
            </template>
          </el-button>
          <el-button
            type="primary"
            text
            @click="handleClone(row)"
            :disabled="row.clone === 0"
          >
            克隆
            <template #icon>
              <img
                class="btn_icon"
                src="@/assets/images/list_icon_kelong.png"
              />
            </template>
          </el-button>
          <el-button
            type="primary"
            text
            @click="handleOpenDialog(2, row)"
            :disabled="row.manager === 0"
          >
            管理
            <template #icon>
              <img
                class="btn_icon"
                src="@/assets/images/list_icon_guanli.png"
              />
            </template>
          </el-button>
          <el-button
            type="danger"
            text
            @click="handleDelete(row)"
            :disabled="row.delete === 0"
          >
            删除
            <template #icon>
              <img class="btn_icon" src="@/assets/images/list_icon_del.png" />
            </template>
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
  <div style="display: flex; justify-content: flex-end">
    <el-pagination
      v-model:current-page="queryData.pageNo"
      :page-size="queryData.pageSize"
      layout="total, prev, pager, next"
      :total="totalCount"
      @current-change="handleGetList"
    >
      <template #total="{ total }"> 共 {{ total }} 条数据 </template>
    </el-pagination>
  </div>
  <!-- 新建项目/管理项目 -->
  <el-dialog
    v-model="createDialogVisible"
    destroy-on-close
    width="700"
    @close="handleResetForm"
  >
    <template #header>
      <div style="text-align: center; font-weight: 600; font-size: 18px">
        {{
          openType === 1
            ? "新建项目"
            : `项目管理-【${createFormData.id}】${createFormData.name}`
        }}
      </div>
    </template>
    <el-tabs v-if="openType === 2" v-model="tabActive">
      <el-tab-pane label="基本资料" name="first"></el-tab-pane>
      <el-tab-pane label="授权用户" name="second">
        <div class="add_box" v-if="!isAdd" @click="isAdd = true">
          <el-button :icon="Plus"></el-button>
          <span>增加授权用户</span>
        </div>
        <div v-else class="flex_box">
          <div>
            <el-input
              v-model="mobile"
              placeholder="请输入授权账号"
              style="width: 200px"
            />
            <el-button
              type="primary"
              style="margin-left: 12px"
              v-debounce="handleAddUser"
            >
              加入
            </el-button>
            <el-button @click="isAdd = false">取消</el-button>
          </div>
        </div>
        <el-table
          :data="authUserList"
          border
          table-layout="auto"
          :header-cell-style="headerCellStyle"
          :cell-style="cellStyle"
          style="margin-top: 16px"
        >
          <el-table-column prop="id" label="编号" />
          <el-table-column prop="mobile" label="账号" />
          <el-table-column prop="nickName" label="名称" />
          <el-table-column label="权限">
            <template #default="scope">
              <el-checkbox
                :true-value="1"
                :false-value="0"
                v-model="scope.row.design"
                label="设计"
              />
              <el-checkbox
                :true-value="1"
                :false-value="0"
                v-model="scope.row.clone"
                label="克隆"
              />
              <el-checkbox
                :true-value="1"
                :false-value="0"
                v-model="scope.row.manager"
                label="管理"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <el-popconfirm
                title="确认删除?"
                @confirm="handleDelUser(scope.row)"
              >
                <template #reference>
                  <img
                    src="@/assets/images/form_icon_del.png"
                    style="width: 20px; height: 20px; cursor: pointer"
                  />
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>
    <el-form
      v-if="openType === 1 || tabActive === 'first'"
      ref="createFormRef"
      :model="createFormData"
      :rules="createRules"
      label-width="100px"
      status-icon
      label-position="left"
    >
      <el-form-item label="类型" prop="clientType">
        <el-radio-group v-model="createFormData.clientType">
          <el-radio :value="0">手机应用</el-radio>
          <el-radio :value="1">电脑应用</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="项目名称" prop="name">
        <el-input
          v-model="createFormData.name"
          placeholder="请输入项目名称，必填"
        />
      </el-form-item>
      <el-form-item label="介绍" prop="description">
        <el-input
          v-model="createFormData.description"
          placeholder="请输入内容"
          type="textarea"
        />
      </el-form-item>
      <el-form-item label="图片" prop="icon">
        <UploadImg placeholder="应用图标" v-model="createFormData.icon" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div style="display: flex; justify-content: center">
        <el-button @click="createDialogVisible = false">取消</el-button>
        <el-button type="primary" v-debounce="handleSubmit" style="color: #fff">
          保存
        </el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 克隆项目 -->
  <el-dialog
    v-model="cloneDialogVisible"
    destroy-on-close
    width="500"
    @close="handleResetCloneForm"
  >
    <template #header>
      <div style="text-align: center; font-weight: 600; font-size: 18px">
        克隆项目
      </div>
    </template>
    <el-form
      ref="cloneFormRef"
      :model="cloneFormData"
      :rules="cloneRules"
      label-width="100px"
      status-icon
      label-position="left"
    >
      <el-form-item label="当前项目">
        {{ cloneProject.projectId }} | {{ cloneProject.currentProjectName }}
      </el-form-item>
      <el-form-item label="目标用户" prop="mobile">
        <el-input
          v-model="cloneFormData.mobile"
          placeholder="请输入用户手机号"
          :maxlength="11"
        />
      </el-form-item>
      <el-form-item label="项目名称" prop="copyName">
        <el-input
          v-model="cloneFormData.copyName"
          placeholder="请输入项目名称"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div style="display: flex; justify-content: center">
        <el-button @click="cloneDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          v-debounce="handleDoClone"
          style="color: #fff"
        >
          保存
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { onMounted, reactive, ref } from "vue";
import { useRouter } from "vue-router";
import {
  projectList,
  addProject,
  updateProject,
  deleteProject,
  findUsersByPid,
  deleteAuthUserById,
  addUpdateAuth,
  queryDesign,
  authUserByMobile,
  cloneDetail,
  doClone,
} from "@/apis/homeApi";
import UploadImg from "@/components/UploadImg.vue";
import { Plus } from "@element-plus/icons-vue";

const router = useRouter();
const headerCellStyle = {
  background: "#FAFAFA",
  fontWeight: 600,
  fontSize: "16px",
  height: "40px",
  color: "#000",
};
const cellStyle = {
  fontSize: "14",
  height: "40px",
  color: "#000",
};
const typeOptions = ref([
  {
    label: "全部",
    value: "",
  },
  {
    label: "手机",
    value: "0",
  },
  {
    label: "电脑",
    value: "1",
  },
]);
const ownerTypeOptions = ref([
  {
    label: "全部",
    value: "",
  },
  {
    label: "直属",
    value: "0",
  },
  {
    label: "参与",
    value: "1",
  },
  {
    label: "克隆",
    value: "2",
  },
]);
const queryData = reactive({
  type: "",
  clientType: "",
  pageNo: 1,
  pageSize: 10,
});
const listData = ref([]);
const createDialogVisible = ref(false);
const createFormRef = ref();
const createFormData = ref({
  clientType: "",
  icon: "",
  name: "",
  description: "",
});
const createRules = reactive({
  clientType: [{ required: true, message: "请选择类型", trigger: "change" }],
  icon: [{ required: true, message: "请上传图片", trigger: "change" }],
  name: [{ required: true, message: "请输入项目名称", trigger: "change" }],
});
const openType = ref(2); //1新增 2管理
const tabActive = ref("first");
const authUserList = ref([]);
const isAdd = ref(false);
const mobile = ref("");
const cloneProject = ref({});
const cloneDialogVisible = ref(false);
const cloneFormRef = ref();
const cloneFormData = reactive({
  id: "",
  mobile: "",
  copyName: "",
});
const cloneRules = reactive({
  mobile: [
    { required: true, message: "请输入手机号", trigger: "blur" },
    {
      pattern: /^1[3-9]\d{9}$/,
      message: "请检查手机号码是否有误",
      trigger: "change",
    },
  ],
  copyName: [{ required: true, message: "请输入", trigger: "change" }],
});
const totalCount = ref(0);

const handleSelectChange = () => {
  queryData.pageNo = 1;
  handleGetList();
};
const handleResetCloneForm = () => {
  cloneFormData.id = "";
  cloneFormData.mobile = "";
  cloneFormData.copyName = "";
};
const handleDoClone = () => {
  cloneFormRef.value.validate(async (valid) => {
    if (valid) {
      console.log(cloneFormData);
      try {
        await doClone(cloneFormData);
        ElMessage({
          type: "success",
          message: createFormData.value.id ? "编辑成功" : "创建成功",
        });
        handleGetList();
        cloneDialogVisible.value = false;
      } catch (error) {
        console.log(error);
      }
    }
  });
};
const handleClone = async (e) => {
  try {
    const res = await cloneDetail(e.id);
    cloneProject.value = res.data;
    cloneFormData.copyName = res.data.copyName;
    cloneFormData.id = res.data.projectId;
    cloneDialogVisible.value = true;
  } catch {}
};
const handleAddUser = async () => {
  if (!mobile.value) {
    ElMessage.error("请输入授权账号");
    return;
  }
  try {
    await authUserByMobile({
      mobile: mobile.value,
      projectId: createFormData.value.id,
    });
    ElMessage({
      type: "success",
      message: "添加成功",
    });
    mobile.value = "";
    fetchAuthUser();
  } catch {}
};
const handleDelUser = async (row) => {
  try {
    await deleteAuthUserById(row.id);
    ElMessage({
      type: "success",
      message: "删除成功",
    });
    fetchAuthUser();
  } catch {}
};
const handleDelete = (e) => {
  ElMessageBox.confirm(
    `项目删除后将不可恢复，请谨慎！确定删除吗？`,
    `删除-${e.name}`,
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      center: true,
    }
  ).then(async () => {
    try {
      await deleteProject(e.id);
      handleGetList();
      ElMessage({
        type: "success",
        message: "删除成功",
      });
    } catch {}
  });
};
const fetchAuthUser = async () => {
  try {
    const res = await findUsersByPid(createFormData.value.id);
    authUserList.value = res.data.items;
  } catch {}
};
const handleOpenDialog = async (type, record) => {
  createFormData.value = record || {};
  openType.value = type;
  createDialogVisible.value = true;
  if (type === 2) {
    fetchAuthUser();
  }
};
const handleResetForm = () => {
  tabActive.value = "first";
  createFormData.value = {};
};
const handleSubmit = async () => {
  if (openType.value === 1 || tabActive.value === "first") {
    // 新增编辑项目
    createFormRef.value.validate(async (valid) => {
      if (valid) {
        try {
          if (createFormData.value.id) {
            await updateProject(createFormData.value);
          } else {
            await addProject(createFormData.value);
          }
          ElMessage({
            type: "success",
            message: createFormData.value.id ? "编辑成功" : "创建成功",
          });
          handleGetList();
          createDialogVisible.value = false;
        } catch (error) {
          console.log(error);
        }
      }
    });
  } else {
    // 保存授权用户
    try {
      await addUpdateAuth({
        data: authUserList.value,
      });
      ElMessage({
        type: "success",
        message: "保存成功",
      });
      isAdd.value = false;
      fetchAuthUser();
    } catch {
      fetchAuthUser();
    }
  }
};

const handleGetList = async () => {
  try {
    const res = await projectList(queryData);
    listData.value = res.data.items;
    totalCount.value = res.data.meta.total;
  } catch (error) {}
};

const handleToDesign = async (e) => {
  router.push({
    path: "/designFrame",
    query: { projectId: e.id },
  });
};

onMounted(() => {
  handleGetList();
});
</script>

<style lang="scss" scoped>
.project_list {
  .el-button {
    padding: 8px 10px;
    line-height: 19px;
    font-size: 16px;
  }
  .el-button--primary {
    color: #2b6bff;
  }
  .el-button--danger {
    color: #ff4444;
  }
}
.filter_head {
  padding: 0 24px;
  height: 64px;
  background: #ffffff;
  box-shadow: inset 0px -1px 0px 0px #ededed;
  .el-form-item {
    margin-bottom: 0;
  }
  .create_btn {
    font-weight: 600;
    font-size: 14px;
    color: #ffffff;
    background: #2b6bff;
  }
}
.tag_primary {
  width: 36px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  background: rgba(43, 107, 255, 0.1);
  border-radius: 3px;
  font-size: 14px;
  color: #2b6bff;
}
.tag_success {
  background: rgba(44, 219, 102, 0.1);
  color: #2cdb66;
}
.desc {
  font-size: 16px;
  color: #999999;
  line-height: 19px;
}
.desc:nth-child(2) {
  margin-top: 10px;
}
.btn_icon {
  width: 20px;
  height: 20px;
}
.add_box {
  display: flex;
  align-items: center;
  span {
    display: none;
    margin-left: 12px;
    font-size: 14px;
    color: #999999;
    line-height: 16px;
  }
  .el-button {
    padding: 8px;
  }
  .el-button:hover {
    background-color: #2b6bff;
    border-color: #2b6bff;
    color: #ffffff;
  }
  .el-button:hover + span {
    display: block;
  }
}
</style>
