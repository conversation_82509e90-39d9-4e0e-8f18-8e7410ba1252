import http from "@/axios/netRequest";

export const login = (data) => {
  return http.post(`/api/users/login`, data);
};

export const smsLogin = (data) => {
  return http.post(`/api/users/login/sms`, data);
};

export const fetchUserDetails = (data) => {
  return http.get(`/api/users/details`, data);
};

export const fetchPhoneCode = (data) => {
  return http.get(`/api/users/sms/${data}`);
};

export const register = (data) => {
  return http.post(`/api/users/register`, data);
};

export const resetPwd = (data) => {
  return http.post(`/api/users/reset-password`, data);
};

export const logout = (data) => {
  return http.post(`/api/users/logout`, data);
};
