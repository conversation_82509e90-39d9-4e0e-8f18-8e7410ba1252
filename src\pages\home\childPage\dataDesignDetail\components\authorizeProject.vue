<template>
  <div class="add_box" v-if="!isAdd" @click="isAdd = true">
    <el-button :icon="Plus"></el-button>
    <span>增加授权项目</span>
  </div>
  <div v-else>
    <el-select
      v-model="selectProjectId"
      clearable
      placeholder="请选择项目"
      style="width: 200px"
    >
      <el-option
        v-for="item in projects"
        :key="item.id"
        :label="item.name"
        :value="item.id"
      />
    </el-select>
    <el-button
      type="primary"
      style="margin-left: 12px"
      v-debounce="handleAddProject"
    >
      加入
    </el-button>
    <el-button @click="isAdd = false">取消</el-button>
  </div>

  <el-table
    :data="authProjectList"
    border
    table-layout="auto"
    :header-cell-style="props.headerCellStyleData"
    :cell-style="props.cellStyleData"
    style="margin-top: 16px"
  >
    <el-table-column prop="projectId" label="编号" />
    <el-table-column prop="projectName" label="项目名称" />
    <el-table-column label="操作">
      <template #default="scope">
        <el-popconfirm title="确认删除?" @confirm="handleDel(scope.row)">
          <template #reference>
            <img src="@/assets/images/form_icon_del.png" class="results_btn" />
          </template>
        </el-popconfirm>
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup>
import { onMounted, ref} from "vue";
import { useRoute } from "vue-router";
import { Plus } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import {
  authProjects,
  projectList,
  saveAuthProject,
  delAuthProject,
} from "@/apis/homeApi";

const props = defineProps({
  headerCellStyleData: {
    type: Object,
    default: {},
  },
  cellStyleData: {
    type: Object,
    default: {},
  },
});

const authProjectList = ref([]);
const projects = ref([]);
const selectProjectId = ref("");

const route = useRoute();
const isAdd = ref(false);

const fetchAuthProjects = async () => {
  try {
    const res = await authProjects(route.query.dbSourceId);
    authProjectList.value = res.data;
  } catch {}
};

const fetchProjects = async () => {
  try {
    const res = await projectList({});
    projects.value = res.data.items;
  } catch {}
};

const handleDel = async (row) => {
  try {
    await delAuthProject(row.id);
    ElMessage({
      type: "success",
      message: "删除成功",
    });
    fetchAuthProjects();
  } catch {}
};

const handleAddProject = async () => {
  if (!selectProjectId.value) {
    ElMessage.error("请选择项目");
    return;
  }
  try {
    await saveAuthProject({
      projectId: selectProjectId.value,
      dbStoreId: Number(route.query.dbSourceId),
    });
    ElMessage({
      type: "success",
      message: "添加成功",
    });
    selectProjectId.value = "";
    fetchAuthProjects();
  } catch {}
};

onMounted(() => {
  fetchAuthProjects();
  fetchProjects();
});
</script>

<style lang="scss">
.add_box {
  display: flex;
  align-items: center;
  span {
    display: none;
    margin-left: 12px;
    font-size: 14px;
    color: #999999;
    line-height: 16px;
  }
  .el-button {
    padding: 8px;
  }
  .el-button:hover {
    background-color: #2b6bff;
    border-color: #2b6bff;
    color: #ffffff;
  }
  .el-button:hover + span {
    display: block;
  }
}
</style>
