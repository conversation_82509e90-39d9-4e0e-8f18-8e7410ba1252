<template>
  <div class="content">
    <div class="title-box">
      <div class="title-label">设置项目主题样式</div>
      <div class="flex_box">
        <el-upload
          action="#"
          :auto-upload="false"
          :show-file-list="false"
          accept=".txt"
          :on-change="handleFileChange"
        >
          <el-button type="primary" color="#2b6bff">导入</el-button>
        </el-upload>
        <el-button style="margin-left: 12px" @click="drawerShow = true">
          创建
        </el-button>
      </div>
    </div>
    <div
      class="content-box"
      :style="{
        boxSizing: 'border-box',
        height: 'calc(100vh - 141px - ' + layoutParam.bottomTabHeight + 'px)',
        padding: '20px',
      }"
    >
      <el-table
        :data="themeList"
        border
        table-layout="fixed"
        :header-cell-style="headerCellStyle"
        :cell-style="cellStyle"
        @row-dblclick="handleRowDblclick"
      >
        <el-table-column label="主题名称">
          <template #default="{ row }">
            <div class="flex_box"></div>
            <span>{{ row.name }}</span>
            <el-tag
              v-if="row.isDefault === 1"
              type="danger"
              size="small"
              style="margin-left: 10px"
            >
              默认
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="中文显示" prop="fullname"></el-table-column>
        <el-table-column label="说明" prop="remark"></el-table-column>
        <el-table-column label="主题色">
          <template #default="{ row }">
            <el-color-picker v-model="row.mainColor" disabled />
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="{ row, $index }">
            <div class="flex_box" style="justify-content: flex-start">
              <el-button type="primary" link @click="handleClone(row)">
                克隆
              </el-button>
              <el-button type="primary" link @click="handleUpdate(row)">
                编辑
              </el-button>
              <el-button type="danger" link @click="handleDel($index)">
                删除
              </el-button>
              <el-button link @click="handleExport(row)">导出</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 抽屉 -->
    <transition name="mask">
      <div class="drawerMask" v-if="drawerShow" @click="drawerShow = false">
        <div class="drawerContainer" @click.stop>
          <div class="header">
            <div class="title">
              {{ formData.id ? "编辑主题" : "新增主题" }}
            </div>
            <div class="close" @click="drawerShow = false">
              <el-icon><Close /></el-icon>
            </div>
          </div>
          <div class="content">
            <el-form
              ref="formRef"
              :model="formData"
              :rules="rules"
              label-width="150px"
            >
              <el-form-item label="主题名称" prop="name">
                <el-input v-model="formData.name" clearable />
              </el-form-item>
              <el-form-item label="中文显示" prop="fullname">
                <el-input v-model="formData.fullname" clearable />
              </el-form-item>
              <el-form-item label="说明" prop="remark">
                <el-input v-model="formData.remark" clearable />
              </el-form-item>
              <el-form-item label="主题色" prop="mainColor">
                <el-color-picker v-model="formData.mainColor" />
              </el-form-item>
              <div
                style="
                  display: flex;
                  justify-content: flex-end;
                  margin-bottom: 10px;
                "
              >
                <el-button :icon="Plus" @click="handleAddRow"></el-button>
              </div>
              <el-form-item prop="configs" class="configsBox">
                <el-table
                  :data="formData.configs"
                  border
                  table-layout="fixed"
                  :header-cell-style="headerCellStyle"
                  :cell-style="cellStyle"
                >
                  <el-table-column label="键名" prop="keyName" width="180">
                    <template #default="{ row, $index }">
                      <el-form-item
                        :prop="`configs.${$index}.keyName`"
                        :rules="rules.keyName"
                      >
                        <el-input
                          v-model="row.keyName"
                          placeholder="请输入键名"
                        />
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <el-table-column label="所属" prop="type" width="140">
                    <template #default="{ row, $index }">
                      <el-form-item
                        :prop="`configs.${$index}.type`"
                        :rules="rules.type"
                      >
                        <el-select
                          v-model="row.type"
                          placeholder="所属类型"
                          @change="handleChangeType($index)"
                        >
                          <el-option label="颜色" value="color" />
                          <el-option label="字体大小" value="fontSize" />
                          <el-option label="字体" value="fontFamily" />
                          <el-option label="边框" value="border" />
                          <el-option label="圆角" value="radius" />
                        </el-select>
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <el-table-column label="值" prop="value" width="300">
                    <template #default="{ row, $index }">
                      <el-form-item
                        :prop="`configs.${$index}.value`"
                        :rules="rules.value"
                      >
                        <!-- 颜色 -->
                        <div v-if="row.type === 'color'" style="margin: 0 auto">
                          <span
                            :style="{
                              color: row.value || '#000',
                              marginRight: '10px',
                            }"
                          >
                            {{ row.value }}
                          </span>
                          <el-color-picker v-model="row.value" />
                        </div>
                        <!-- 字体大小 -->
                        <div
                          class="flex_box"
                          v-else-if="row.type === 'fontSize'"
                          style="margin: 0 auto"
                        >
                          <el-input-number
                            :min="0"
                            :max="100"
                            v-model="row.value"
                            placeholder="像素值"
                            :controls="false"
                            :precision="0"
                          />
                          <div
                            :style="{
                              marginLeft: '10px',
                              fontSize:
                                row.type === 'fontSize'
                                  ? `${row.value || 14}px`
                                  : '14px',
                            }"
                          >
                            px
                          </div>
                        </div>
                        <!-- 字体 -->
                        <el-select
                          v-else-if="row.type === 'fontFamily'"
                          v-model="row.value"
                          placeholder="请选择"
                          :style="{
                            fontFamily: row.value || 'Microsoft YaHei',
                            width: '150px',
                            margin: '0 auto',
                          }"
                        >
                          <el-option
                            v-for="item in systemFonts"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                            :style="{ fontFamily: item.value }"
                          />
                        </el-select>
                        <!-- 边框 -->
                        <div
                          v-else-if="row.type === 'border'"
                          style="margin: 0 auto"
                        >
                          <div class="flex_box">
                            <el-form-item
                              :prop="`configs.${$index}.borderWidth`"
                              :rules="rules.borderWidth"
                              style="width: 80px"
                            >
                              <div class="flex_box">
                                <el-input
                                  v-model="row.borderWidth"
                                  placeholder="宽度"
                                ></el-input>
                                <span style="margin-left: 10px">px</span>
                              </div>
                            </el-form-item>
                            <el-form-item
                              :prop="`configs.${$index}.borderStyle`"
                              :rules="rules.borderStyle"
                              style="width: 80px; margin: 0 10px"
                            >
                              <el-select
                                v-model="row.borderStyle"
                                placeholder="样式"
                              >
                                <el-option label="实线" value="solid" />
                                <el-option label="虚线" value="dashed" />
                                <el-option label="点线" value="dotted" />
                              </el-select>
                            </el-form-item>
                            <el-form-item
                              :prop="`configs.${$index}.borderColor`"
                              :rules="rules.borderColor"
                            >
                              <el-color-picker v-model="row.borderColor" />
                            </el-form-item>
                          </div>
                          <div
                            :style="{
                              borderTop:
                                row.borderWidth &&
                                row.borderStyle &&
                                row.borderColor
                                  ? `${row.borderWidth}px ${row.borderStyle} ${row.borderColor}`
                                  : 'none',
                              width: '200px',
                              marginTop: '10px',
                            }"
                          >
                            <!-- 边框预览 -->
                          </div>
                        </div>
                        <!-- 圆角 -->
                        <div
                          class="flex_box"
                          v-else-if="row.type === 'radius'"
                          style="margin: 0 auto"
                        >
                          <div
                            :style="{
                              width: '50px',
                              height: '50px',
                              border: '1px solid #ccc',
                              margin: '0 auto',
                              borderRadius: `${row.topRadius || 0}px ${
                                row.rightRadius || 0
                              }px ${row.bottomRadius || 0}px ${
                                row.leftRadius || 0
                              }px`,
                            }"
                          />
                          <div class="flex_box radius_box">
                            <el-form-item
                              :prop="`configs.${$index}.topRadius`"
                              :rules="rules.topRadius"
                            >
                              <el-input-number
                                :min="0"
                                :max="100"
                                v-model="row.topRadius"
                                placeholder="上"
                                :controls="false"
                                :precision="0"
                                style="width: 50px"
                              >
                              </el-input-number>
                            </el-form-item>
                            <el-form-item
                              :prop="`configs.${$index}.rightRadius`"
                              :rules="rules.rightRadius"
                            >
                              <el-input-number
                                :min="0"
                                :max="100"
                                v-model="row.rightRadius"
                                placeholder="右"
                                :controls="false"
                                :precision="0"
                                style="width: 50px"
                              />
                            </el-form-item>
                            <el-form-item
                              :prop="`configs.${$index}.bottomRadius`"
                              :rules="rules.bottomRadius"
                            >
                              <el-input-number
                                :min="0"
                                :max="100"
                                v-model="row.bottomRadius"
                                placeholder="下"
                                :controls="false"
                                :precision="0"
                                style="width: 50px"
                              />
                            </el-form-item>
                            <el-form-item
                              :prop="`configs.${$index}.leftRadius`"
                              :rules="rules.leftRadius"
                            >
                              <el-input-number
                                :min="0"
                                :max="100"
                                v-model="row.leftRadius"
                                placeholder="左"
                                :controls="false"
                                :precision="0"
                                style="width: 50px"
                              />
                            </el-form-item>
                          </div>
                        </div>
                        <el-input
                          v-else
                          disabled
                          placeholder="请先选择所属类型"
                        />
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <el-table-column label="说明">
                    <template #default="{ row }">
                      <el-form-item>
                        <el-input v-model="row.desc" />
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="70" fixed="right">
                    <template #default="{ $index }">
                      <el-form-item>
                        <el-popconfirm
                          title="确认删除?"
                          @confirm="handleDelConfig($index)"
                        >
                          <template #reference>
                            <el-button type="danger" link>删除</el-button>
                          </template>
                        </el-popconfirm>
                      </el-form-item>
                    </template>
                  </el-table-column>
                </el-table>
              </el-form-item>
            </el-form>
          </div>
          <div class="footer">
            <el-button @click="drawerShow = false">取消</el-button>
            <el-button
              type="primary"
              style="color: #fff"
              v-debounce="handleSave"
            >
              保存
            </el-button>
          </div>
        </div>
      </div>
    </transition>
  </div>
  <!-- 克隆 -->
  <el-dialog
    v-model="cloneDialogVisible"
    destroy-on-close
    width="500"
    @close="handleResetCloneForm"
  >
    <template #header>
      <div style="text-align: center; font-weight: 600; font-size: 18px">
        克隆
      </div>
    </template>
    <el-form
      ref="cloneFormRef"
      :model="cloneFormData"
      :rules="cloneRules"
      label-width="100px"
      status-icon
      label-position="left"
    >
      <el-form-item label="主题名称" prop="name">
        <el-input v-model="cloneFormData.name" placeholder="请输入主题名称" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div style="display: flex; justify-content: center">
        <el-button @click="cloneDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          v-debounce="handleDoClone"
          style="color: #fff"
        >
          保存
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted, inject, watch,toRaw } from "vue";
import { useFileBase } from "../../../common/fileBasic";
import { ElMessageBox, ElMessage } from "element-plus";
import { Plus, Close } from "@element-plus/icons-vue";
import { exportTxtFile, validateArrayItems } from "../../../common/utils";
import {
  fetchThemeList,
  saveTheme,
  deleteTheme,
  cloneTheme,
  setDefaultTheme,
} from "@/apis/designer";
const fileBase = useFileBase();
const props = defineProps({
  fileItem: {
    type: Object,
    default: {},
  },
});
const ProjectRef = inject("ProjectRef");
const layoutParam = inject("layoutParam");

const systemFonts = ref([
  {
    label: "微软雅黑",
    value: "Microsoft YaHei",
  },
  {
    label: "黑体",
    value: "SimHei",
  },
  {
    label: "宋体",
    value: "SimSun",
  },
  {
    label: "楷体",
    value: "KaiTi",
  },
  {
    label: "仿宋",
    value: "FangSong",
  },
]);
const headerCellStyle = {
  background: "#FAFAFA",
  fontWeight: 600,
  fontSize: "16px",
  height: "40px",
  color: "#000",
};
const cellStyle = {
  fontSize: "14",
  height: "40px",
  color: "#000",
};
const themeList = ref([
  // {
  //   id: 1,
  //   name: "theme1",
  //   fullname: "主题1",
  //   remark: "这是一个主题1",
  //   mainColor: "#2b6bff",
  //   configs: [
  //     {
  //       keyName: "backgroundColor",
  //       type: "radius",
  //       value: "1px 1px 1px 1px",
  //       desc: "背景颜色",
  //     },
  //     {
  //       keyName: "colorTheme",
  //       type: "color",
  //       value: "#2b6bff",
  //       desc: "主色",
  //     },
  //   ],
  // },
]);
const cloneFormRef = ref(null);
const cloneDialogVisible = ref(false);
const cloneFormData = ref({});
const cloneRules = reactive({
  name: [{ required: true, message: "请输入主题名称", trigger: "change" }],
});
const drawerShow = ref(false);
const formRef = ref(null);
const formData = ref({
  id: "",
  name: "",
  fullname: "",
  remark: "",
  mainColor: "",
  configs: [],
});
const rules = reactive({
  name: [
    { required: true, message: "请输入主题名称", trigger: "change" },
    // {
    //   pattern: /^[a-z][a-z0-9_]*[a-z0-9]$/,
    //   message:
    //     "只能包含小写字母、数字或下划线，首位必须是字母，末尾不能是下划线",
    //   trigger: "change",
    // },
  ],
  fullname: [{ required: true, message: "请输入中文名称", trigger: "change" }],
  mainColor: [{ required: true, message: "请选择主题色", trigger: "change" }],
  keyName: [
    { required: true, message: "请输入键名", trigger: "change" },
    // {
    //   pattern: /^[a-z][a-z0-9_]*[a-z0-9]$/,
    //   message:
    //     "只能包含小写字母、数字或下划线，首位必须是字母，末尾不能是下划线",
    //   trigger: "change",
    // },
  ],
  type: [{ required: true, message: "请选择所属类型", trigger: "change" }],
  value: [{ required: true, message: "请输入", trigger: "change" }],
  borderWidth: [{ required: true, message: "请输入", trigger: "change" }],
  borderStyle: [{ required: true, message: "请选择", trigger: "change" }],
  borderColor: [{ required: true, message: "请选择", trigger: "change" }],
  topRadius: [{ required: true, message: "", trigger: "change" }],
  bottomRadius: [{ required: true, message: "", trigger: "change" }],
  leftRadius: [{ required: true, message: "", trigger: "change" }],
  rightRadius: [{ required: true, message: "", trigger: "change" }],
});

const handleFileChange = (file) => {
  // 检查文件类型
  if (file.raw.type !== "text/plain" && !file.name.endsWith(".txt")) {
    ElMessage.error("请上传有效的 TXT 文件");
    return;
  }

  // 检查文件大小（限制5MB）
  if (file.size > 5 * 1024 * 1024) {
    ElMessage.error("文件大小不能超过5MB");
    return;
  }

  const reader = new FileReader();
  reader.onload = async (e) => {
    const fileContent = JSON.parse(e.target.result);
    if (fileContent && validateArrayItems(fileContent)) {
      // 导入
      const newTheme = fileContent.reduce(
        (acc, item) => {
          acc.name = item.name;
          acc.fullname = item.fullname;
          acc.remark = item.remark;
          acc.mainColor = item.mainColor;
          acc.configs.push({
            keyName: item.keyName,
            type: item.type,
            value: item.value,
            desc: item.desc,
          });
          return acc;
        },
        {
          configs: [],
        }
      );
      const isDuplicate = themeList.value.some(
        (item) => item.name === newTheme.name
      );
      if (isDuplicate) {
        return ElMessage({
          type: "error",
          message: "主题名称已存在",
        });
      }

      try {
        await saveTheme({
          ...newTheme,
          projectId: Number(ProjectRef.BasicData.ProjectId),
          configs: JSON.stringify(newTheme.configs),
        });
        ElMessage({
          type: "success",
          message: "导入成功",
        });
        getThemeList();
      } catch (error) {}
    } else {
      ElMessage.error("文件内容格式不正确");
    }
  };
  reader.onerror = () => {
    ElMessage.error("文件读取失败");
  };
  reader.readAsText(file.raw);
};

const handleChangeType = (index) => {
  formData.value.configs[index].borderWidth = "";
  formData.value.configs[index].borderStyle = "";
  formData.value.configs[index].borderColor = "";
  formData.value.configs[index].topRadius = "";
  formData.value.configs[index].bottomRadius = "";
  formData.value.configs[index].leftRadius = "";
  formData.value.configs[index].rightRadius = "";

  // 设置默认值
  switch (formData.value.configs[index].type) {
    case "color":
      formData.value.configs[index].value = "#000000";
      break;
    case "fontSize":
      formData.value.configs[index].value = 14;
      break;
    case "fontFamily":
      formData.value.configs[index].value = "Microsoft YaHei";
      break;
    case "border":
      formData.value.configs[index].value = "1px solid #000000";
      formData.value.configs[index].borderWidth = "1";
      formData.value.configs[index].borderStyle = "solid";
      formData.value.configs[index].borderColor = "#000000";
      break;
    case "radius":
      formData.value.configs[index].value = "5px 5px 5px 5px";
      formData.value.configs[index].topRadius = 5;
      formData.value.configs[index].bottomRadius = 5;
      formData.value.configs[index].leftRadius = 5;
      formData.value.configs[index].rightRadius = 5;
      break;
  }
};

const asyncCurrentTheme = (row) => {
  ProjectRef.BasicData.theme.currentTheme.fileId = row.id;
  ProjectRef.BasicData.theme.currentTheme.title = row.fullname;
  ProjectRef.BasicData.theme.currentTheme.color = row.mainColor;
  ProjectRef.BasicData.theme.currentTheme.configs = JSON.parse(JSON.stringify(toRaw(row.configs)));
  ProjectRef.funcObj.initThemeKeyMap()
  console.log(ProjectRef.BasicData.theme.currentTheme)
};

const handleRowDblclick = async (row) => {
	asyncCurrentTheme(row);
  if (row.isDefault) {
    return;
  }
  try {
    await setDefaultTheme(row.id);
    ElMessage({
      type: "success",
      message: "设置成功",
    });
    getThemeList();
    
  } catch (error) {}
};

const handleDrawerClose = () => {
  formData.value = {
    id: "",
    name: "",
    fullname: "",
    remark: "",
    mainColor: "",
    configs: [],
  };
};

const handleAddRow = () => {
  formData.value.configs.push({
    keyName: "",
    type: "",
    value: "",
    desc: "",
    borderWidth: "",
    borderStyle: "",
    borderColor: "",
    topRadius: "",
    bottomRadius: "",
    leftRadius: "",
    rightRadius: "",
  });
};

const handleDelConfig = (index) => {
  formData.value.configs.splice(index, 1);
};

const handleResetCloneForm = () => {
  cloneFormData.value = {};
};

const handleSave = () => {
  formRef.value.validate(async (valid) => {
    if (valid) {
      // 检查主题名称是否重复
      const isDuplicate = themeList.value.some(
        (item) =>
          item.name === formData.value.name && item.id !== formData.value.id
      );
      if (isDuplicate) {
        return ElMessage({
          type: "error",
          message: "主题名称已存在",
        });
      }
      // 判断配置项是否为空
      if (formData.value.configs.length === 0) {
        return ElMessage({
          type: "error",
          message: "配置项不能为空",
        });
      }
      //配置项键名是否重复
      const isKeyNameDuplicate = formData.value.configs.some(
        (item, index) =>
          formData.value.configs.findIndex(
            (subItem) => subItem.keyName === item.keyName
          ) !== index
      );
      if (isKeyNameDuplicate) {
        return ElMessage({
          type: "error",
          message: "配置项键名重复",
        });
      }
      formData.value.configs.forEach((item) => {
        if (item.type === "border") {
          item.value = `${item.borderWidth}px ${item.borderStyle} ${item.borderColor}`;
        } else if (item.type === "radius") {
          item.value = `${item.topRadius}px ${item.rightRadius}px ${item.bottomRadius}px ${item.leftRadius}px`;
        }
      });

      // 保存
      try {
        await saveTheme({
          ...formData.value,
          projectId: Number(ProjectRef.BasicData.ProjectId),
          configs: JSON.stringify(formData.value.configs),
        });
        ElMessage({
          type: "success",
          message: "保存成功",
        });
        if (formData.value.isDefault) {
          asyncCurrentTheme(formData.value);
        }
        drawerShow.value = false;
        getThemeList();
      } catch (error) {}
    }
  });
};

const handleClone = (row) => {
  cloneFormData.value = {
    name: "",
    id: row.id,
  };
  cloneDialogVisible.value = true;
};

const handleDoClone = () => {
  cloneFormRef.value.validate(async (valid) => {
    if (valid) {
      // 检查主题名称是否重复
      const isDuplicate = themeList.value.some(
        (item) => item.name === cloneFormData.value.name
      );
      if (isDuplicate) {
        return ElMessage({
          type: "error",
          message: "主题名称已存在",
        });
      }
      try {
        await cloneTheme(cloneFormData.value);
        ElMessage({
          type: "success",
          message: "克隆成功",
        });
        cloneDialogVisible.value = false;
        getThemeList();
      } catch (error) {}
    }
  });
};

const handleUpdate = (row) => {
  formData.value = JSON.parse(
    JSON.stringify({
      ...row,
      configs: row.configs.map((item) => {
        return {
          ...item,
          borderWidth:
            item.type === "border"
              ? parseInt(item.value.split(" ")[0], 10)
              : "",
          borderStyle: item.type === "border" ? item.value.split(" ")[1] : "",
          borderColor: item.type === "border" ? item.value.split(" ")[2] : "",
          topRadius:
            item.type === "radius"
              ? parseInt(item.value.split(" ")[0], 10)
              : "",
          rightRadius:
            item.type === "radius"
              ? parseInt(item.value.split(" ")[1], 10)
              : "",
          bottomRadius:
            item.type === "radius"
              ? parseInt(item.value.split(" ")[2], 10)
              : "",
          leftRadius:
            item.type === "radius"
              ? parseInt(item.value.split(" ")[3], 10)
              : "",
        };
      }),
    })
  );
  drawerShow.value = true;
};

const handleDel = (index) => {
  ElMessageBox.confirm(`确定要删除此项？`, `删除`, {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    center: true,
    customClass: "custom_message_box",
  }).then(async () => {
    // 删除
    try {
      await deleteTheme(themeList.value[index].id);
      ElMessage({
        type: "success",
        message: "删除成功",
      });
      getThemeList();
    } catch (error) {}
  });
};

const handleExport = (record) => {
  const data = record.configs.map((item) => {
    return {
      name: record.name,
      fullname: record.fullname,
      remark: record.remark,
      mainColor: record.mainColor,
      keyName: item.keyName,
      type: item.type,
      value: item.value,
      desc: item.desc,
    };
  });
  exportTxtFile(JSON.stringify(data), record.name + ".txt");
};

const getThemeList = async () => {
  try {
    const res = await fetchThemeList(ProjectRef.BasicData.ProjectId);
    themeList.value = res.data.items.map((item) => {
      return {
        ...item,
        configs: item.configs ? JSON.parse(item.configs) : [],
      };
    });
  } catch (error) {
    console.log(error);
  }
};

watch(
  () => drawerShow.value,
  (newValue) => {
    if (!newValue) {
      handleDrawerClose();
    }
  }
);

onMounted(() => {
  props.fileItem.func.saveData = saveData;
  getThemeList();
});

function saveData(success) {
  console.log("保存了themeFile上面的数据");
  if (success) success();
}
</script>

<style lang="scss">
.content {
  position: relative;
  .title-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: white;
    border-bottom: 1px solid #e2e2e2;
    padding: 6px 20px;
    position: sticky;
    top: 0;
    z-index: 100;

    .title-label {
      font-size: 13px;
    }
  }
  .content-box {
    background-color: white;
    overflow: auto;
  }
  /* 覆盖抽屉默认定位 */
  .content-box + .el-overlay {
    position: absolute !important;
    width: 100% !important;
    height: 100% !important;
    top: 0 !important;
    left: 0 !important;
    .el-drawer {
      text-align: left;
      .el-drawer__header {
        margin-bottom: 20px;
      }
      .el-drawer__body {
        padding-top: 0;
        //  scrollbar-width: none;
      }
    }
  }
  .el-color-picker__mask {
    background-color: rgba(255, 255, 255, 0);
  }
  .el-color-picker .el-color-picker__icon {
    display: none;
  }
  .configsBox {
    .el-form-item__content {
      margin-left: 0 !important;
    }
  }
  /* 蒙版渐变动画 */
  .mask-enter-active,
  .mask-leave-active {
    transition: opacity 0.3s ease;
  }
  .mask-enter-from,
  .mask-leave-to {
    opacity: 0;
  }
  .drawerMask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
    overflow: hidden;
    .drawerContainer {
      position: absolute;
      top: 0;
      right: 0;
      width: 60%;
      height: 100%;
      background-color: white;
      overflow: auto;
      animation: slide-in 0.3s ease-out forwards;
      .header {
        position: sticky;
        top: 0;
        left: 0;
        box-sizing: border-box;
        padding: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: white;
        z-index: 100;
        .close {
          cursor: pointer;
          padding: 5px;
        }
      }
      .content {
        padding: 20px;
      }
      .footer {
        position: sticky;
        bottom: 0;
        left: 0;
        box-sizing: border-box;
        width: 100%;
        padding: 20px;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        background-color: white;
        z-index: 100;
      }
    }
    @keyframes slide-in {
      from {
        transform: translateX(100%);
      }
      to {
        transform: translateX(0);
      }
    }
  }
}
.el-dialog__header.show-close {
  padding-right: 0 !important;
}
.custom_message_box {
  .el-message-box__header.show-close {
    padding-right: 0 !important;
  }
}

.radius_box {
  margin-left: 10px;
  .el-input-number.is-without-controls .el-input__wrapper {
    padding: 0 !important;
  }
}
</style>
