import { defineStore } from "pinia";
import { fetchUserDetails } from "../apis/loginApi";

export const useUserStore = defineStore("userInfo", {
  state: () => ({
    userInfo: {
      account: "",
      avator: "",
      name: "",
    },
  }),
  actions: {
    getUserInfo() {
      return new Promise(async (resolve, reject) => {
        try {
          const res = await fetchUserDetails();
          this.userInfo = res.data;
          resolve(res.data);
        } catch (error) {
          reject(error);
        }
      });
    },
  },
  persist: true, // 启用持久化
});
