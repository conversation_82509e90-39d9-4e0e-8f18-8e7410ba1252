<template>
	<div class="content" :style="{flexDirection: props.TBase.getChooseDirect()== 'horizontal' ? 'row' : 'column' }">
		<div v-for="(rItem,index) in props.TBase.getChooseItems() " :key="index" class="group-box" @click.stop="onSelectButton(props.TItem.key,rItem.value)">
			<div class="item-box basicStyle" :style="props.TBase.getChooseStyle()">
				<div class="btn-out" :style="{border: '1px solid ' + props.TBase.getFontColor()}" v-if="props.TItem.key=='TRadio'">
					<div class="btn-in" v-if="rItem.value == currentValue" :style="{backgroundColor:props.TBase.getFontColor()}"></div>
				</div>
				<el-icon :size="22" :color="props.TBase.getFontColor()" v-if="props.TItem.key=='TCheckBox'">
					<CircleCheck v-if="!moreCurrent.includes(rItem.value)"/>
					<CircleCheckFilled v-else-if="moreCurrent.includes(rItem.value)" />
				</el-icon>
				<div class="btn-text" :style="{textAlign:props.TBase.getTextAlign(),textDecoration:props.TBase.getTextDecoration()}">{{rItem.label}}</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
	import { reactive,ref ,onMounted,inject,computed,watch,toRaw} from 'vue';
	
	const props = defineProps({
		TItem:{
			type:Object,
			default:{}
		},
		TParent:{
			type:Object,
			default:{id:'10001'}
		},
		TBase:{
			type:Object,
			default:{
				getChooseDirect:()=>{},
				getChooseItems:()=>{},
				getChooseStyle:()=>{},
				getFontColor:()=>{}
			}
		}
	})

	const currentValue = ref('')
	const moreCurrent = reactive([])
	
	watch(currentValue,(newValue)=>{
		props.TItem.value = newValue
	})
	
	watch(moreCurrent,(newValue)=>{
		props.TItem.value = toRaw(newValue).join(",")
	})
	
	watch(()=>props.TItem.value,(v)=>{
		if(props.TItem.key=='TRadio'){
			currentValue.value = v
		}else{
			let tmpArray = v.split(',')
			
			if(tmpArray.length > 0){
				if(moreCurrent.length > 0) moreCurrent.splice(0,moreCurrent.length)
				for(let i=0;i<tmpArray.length;i++){
					moreCurrent.push(tmpArray[i])
				}
			}
		}
	})
	
	onMounted(()=>{
		props.TItem.parent = props.TParent
		if(props.TItem.key=='TRadio'){
			currentValue.value = props.TItem.value
		}else{
			let mv = currentValue.value.split(",")
			if(mv && mv.length > 0){
				moreCurrent.splice(0,moreCurrent.length - 1)
				for(let i=0;i<mv.length;i++){
					moreCurrent.push(mv[i])
				}
			}
		}
	})
	
	function onSelectButton(key,value){
		if(!props.TBase.checkEnabled()) return
		if(key=='TRadio'){
		   currentValue.value = value
		}else if(key=='TCheckBox'){
			if(moreCurrent.includes(value)){
				for(let n=0;n<moreCurrent.length;n++){
					if(moreCurrent[n]==value){
						moreCurrent.splice(n,1)
						return
					}
				}
			}else{
				moreCurrent.push(value)
			}
		}
	}
	
</script>

<style lang="scss" scoped>
	@import "../../../common/effect.css";
	
	.content {
		width:calc(100% - 10px);
		height:calc(100% - 10px);
		display: flex;
		justify-content: space-around;
		align-items: center;
		padding:3px;
		
		
		
		.group-box {
			width:100%;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			
			.item-box{
				display: flex;
				justify-content: center;
				align-items: center;
				margin-right: 5px;
				flex:1;
				padding:4px 6px;
				.btn-out {
					 width:16px;
					 height:16px;
					 border:2px solid #121212;
					 display: flex;
					 justify-content: center;
					 align-items: center;
					 border-radius: 10px;
					 margin-right: 4px;
					.btn-in {
						width:12px;
						height:12px;
						background-color: #121212;
						border-radius: 8px;
					}
				}
				.btn-text{
					margin-left:2px;
					flex:1;
				}
				.edit-input {
					border:1px dotted #d4d4d4;
					text-align: center;
				}
				.edit-input:focus{
					outline: none;
					box-shadow: 0 0 0 2px transparent; 
				}
			}
		}
	}
</style>