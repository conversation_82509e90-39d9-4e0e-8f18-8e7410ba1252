<template>
	<!-- <div class="content" :style="{border: getBorder(),backgroundColor:getBackgroundColor()}"> -->
	<div class="content basicStyle" :style="props.TBase.getStyle()">
		<div :style="{width:'100%'}" @dblclick="onDbClick()" v-if="editStatus==0">{{props.TItem.value}}</div>
		<input type="text" v-model="props.TItem.value" class="edit-input" v-if="editStatus==1" @keyup.enter="editStatus=0" @blur="editStatus=0"
		    :style="{width:(props.TItem.w - 30)+'px'}"  ref="inputRef"/>
	</div>
</template>

<script lang="ts" setup>
	import { reactive,ref ,onMounted,inject, nextTick,watch} from 'vue';
	const ProjectRef = inject("ProjectRef")
	
	const props = defineProps({
		TItem:{
			type:Object,
			default:{}
		},
		TParent:{
			type:Object,
			default:{id:'10001'}
		},
		TBase:{
			type:Object,
			default:{
				getStyle:(e)=>{},
				checkEnabled:(e)=>{},
			}
		}
	})
	const inputRef = ref(null)
	const editStatus = ref(0)
	
	function onDbClick(){
		if(!props.TBase.checkEnabled()) return
		if(editStatus.value == 0){
			editStatus.value = 1
			nextTick(()=>{
				inputRef.value.focus()
				inputRef.value.select()
			})
			
		}else{
			editStatus.value = 0
		}
	}
	
	onMounted(()=>{
		props.TItem.parent = props.TParent
	})
	
</script>

<style lang="scss" scoped>
	@import "../../../common/effect.css";
	
	.content {
		width:calc(100% - 5px);
		height:calc(100% - 5px);
		display: flex;
		justify-content: center;
		align-items: center;
		padding:3px;
		border-radius: 5px;
		background-color: white;
		
		.edit-input {
			border:1px dotted #d4d4d4;
			text-align: center;
			padding:4px 0px;
		}
		.edit-input:focus{
			outline: none;
			box-shadow: 0 0 0 2px transparent; 
		}
	}
</style>