<template>
	<div class="content">
		<div v-if="!props.TItem.children || props.TItem.children.length == 0">{{props.TItem.value}}</div>
		<TComponents v-for="(childItem,r) in props.TItem.children" :key= "r" :TItem="childItem" :TParent="props.TItem" :TIndex="r" />
	</div>
</template>

<script lang="ts" setup>
	import {reactive,ref,onMounted,watch} from 'vue'
	
	const props = defineProps({
		TItem:{
			type:Object,
			default:{}
		},
		TParent:{
			type:Object,
			default:{id:'10001'}
		},
		TBase:{
			type:Object,
			default:{}
		}
	})

	onMounted(()=>{
		props.TItem.parent = props.TParent
	})
</script>

<style lang="scss" scoped>
	.content {
		height:calc(100% - 3px);
		width:calc(100% - 3px);
	}
</style>