<template>
	<div class="content basicStyle" :style="props.TBase.getStyle()" >
		<!-- <img :src="getSrc()" class="img-box" draggable="false" /> -->
	</div>
</template>

<script lang="ts" setup>
	import { reactive,ref ,onMounted,inject,watch} from 'vue';
	const ProjectRef = inject("ProjectRef")
	const pjResouceMap = inject("pjResouceMap")
	
	const props = defineProps({
		TItem:{
			type:Object,
			default:{}
		},
		TParent:{
			type:Object,
			default:{id:'10001'}
		},
		TBase:{
			type:Object,
			default:{
				getStyle:()=>{}
			}
		}
	})


	function getSrc(){
		if(props.TItem.hasOwnProperty('src') && props.TItem.src){
			if(props.TItem.src.trim().split('.')[0] =='resource'){
				return pjResouceMap.getResourceUrl(props.TItem.src.trim())
			}else{
				return props.TItem.src
			}
		}
		return 'https://mobileproducts.cdn.sohu.com/q_70,c_zoom,w_512/prod/img/aigc/simple_ai/2025/03/31/bwmsOMcZiMn.png'
	}
	
	onMounted(()=>{
		props.TItem.parent = props.TParent
	})
	
</script>

<style lang="scss" scoped>
	@import '../../../common/effect.css';
	
	.content {
		width:calc(100% - 2px);
		height:calc(100% - 2px);
		background-color: transparent;
		overflow: hidden;
		display: flex;
		justify-content: center;
		align-items: center;
	
		.img-box{
			width:100%;
			height:100%;
		}
	}
</style>