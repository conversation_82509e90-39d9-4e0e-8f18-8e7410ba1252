<template>
  <!-- 顶部菜单工具栏 -->
  <div class="flex_box top_tools">
    <div class="flex_box menu">
      <div class="flex_box title">
        <img src="@/assets/images/list_logo.png" />
        <div>蜂创设计平台</div>
      </div>
      <div class="flex_box dropdown">
        <el-dropdown trigger="click" style="cursor: pointer">
          <div>
            <span class="name">工具</span>
            <el-icon> <arrow-down /> </el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item icon="ShoppingCartFull"
                >组件市场</el-dropdown-item
              >
              <el-dropdown-item icon="Coin">组件仓库</el-dropdown-item>
              <el-dropdown-item icon="PictureFilled">图片仓库</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-dropdown style="margin: 0 24px; cursor: pointer" trigger="click">
          <div>
            <span class="name">显示</span>
            <el-icon> <arrow-down /> </el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                :icon="checkBlockShow('left') ? 'Select' : 'SemiSelect'"
                @click.stop="setBlockVisible('left')"
                >左则栏区</el-dropdown-item
              >
              <el-dropdown-item
                :icon="checkBlockShow('right') ? 'Select' : 'SemiSelect'"
                @click.stop="setBlockVisible('right')"
                v-if="showRightMenu"
                >右则栏区</el-dropdown-item
              >
              <el-dropdown-item
                :icon="checkBlockShow('bottom') ? 'Select' : 'SemiSelect'"
                @click.stop="setBlockVisible('bottom')"
                >底则栏区</el-dropdown-item
              >
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-dropdown trigger="click" style="cursor: pointer">
          <div>
            <span class="name">帮助</span>
            <el-icon> <arrow-down /> </el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item icon="VideoCamera">视频教程</el-dropdown-item>
              <el-dropdown-item icon="Reading">帮助文档</el-dropdown-item>
              <el-dropdown-item icon="Memo">关于我们</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    <div class="flex_box tools">
      <el-button-group>
        <el-button style="width: 140px" :disabled="true" icon="Upload" text>
          上传至组件库
        </el-button>
        <el-button text @click.stop="saveFileData(null)">
          保存
          <template #icon>
            <img class="btn_icon" src="@/assets/images/nav_icon_baocun.png" />
          </template>
        </el-button>
        <el-button text @click.stop = "compiler.handleCompile()">
          编译
          <template #icon>
            <img class="btn_icon" src="@/assets/images/nav_icon_bianyi.png" />
          </template>
        </el-button>
        <el-button text @click="handlePriview">
          预览
          <template #icon>
            <img class="btn_icon" src="@/assets/images/nav_icon_yulan.png" />
          </template>
        </el-button>
        <el-button text @click.stop="onPublish()">
          发布
          <template #icon>
            <img class="btn_icon" src="@/assets/images/pz_icon_fabu.png" />
          </template>
        </el-button>
      </el-button-group>

      <el-button
        style="
          margin: 0 24px 0 16px !important;
          font-weight: 600;
          font-size: 14px;
          color: #212121;
        "
        text
        bg
        @click="handleExit"
        >退出
        <template #icon
          ><img class="btn_icon" src="@/assets/images/nav_icon_tuichu.png"
        /></template>
      </el-button>
    </div>
  </div>

  <!-- 尺寸缩放工具 -->
  <div
    class="flex_box size_tools"
    style="justify-content: flex-start; padding-left: 25px"
  >
    <!-- <el-dropdown trigger="click" style="cursor:pointer" @command="onSelectScreen">
      <div>
        <span class="name">{{sizeControl.screenType.title}}</span>
        <el-icon> <arrow-down /> </el-icon>
      </div>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item v-for="(p,i) in setupData.screenTypes" :command="i">{{p.title}}</el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown> -->
    <div
      style="
        margin-left: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
      "
    >
      <el-icon :size="17" v-if="ProjectRef.BasicData.clientType == 0">
        <Iphone />
      </el-icon>
      <el-icon :size="17" v-else>
        <Monitor />
      </el-icon>
      <span style="margin-left: 5px">{{
        ProjectRef.BasicData.clientType == 0 ? "手机应用" : "PC应用"
      }}</span>
    </div>

    <div class="flex_box section">
      <el-input-number
        :min="1"
        :precision="0"
        :controls="false"
        v-model="sizeControl.wh"
        style="width: 60px; height: 24px"
        @change="onCustomScreen()"
      />
      <span>x</span>
      <el-input-number
        :min="1"
        :precision="0"
        :controls="false"
        v-model="sizeControl.hg"
        style="width: 60px; height: 24px"
        @change="onCustomScreen()"
      />
    </div>

    <div style="font-size: 14px; color: #999999">缩放：</div>
    <el-dropdown
      trigger="click"
      style="cursor: pointer"
      @command="onSelectScale"
    >
      <div>
        <span class="name" style="font-weight: 400">{{
          sizeControl.scale.title
        }}</span>
        <el-icon> <arrow-down /> </el-icon>
      </div>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item
            v-for="(c, index) in setupData.screenScales"
            :command="index"
            >{{ c.title }}</el-dropdown-item
          >
        </el-dropdown-menu>
      </template>
    </el-dropdown>
    <FileTab
      class="flex_box"
      :dataList="OpenFileMgr.listFiles"
      :currentFilePath="OpenFileMgr.currentFilePath"
    >
    </FileTab>
  </div>

  <!-- layout -->
  <div class="container">
    <!-- 上半部分（左右选项卡+中间预览div+左右拖动line） -->
    <div
      class="flex_box top_content"
      :style="{
        height: 'calc(100vh - 96px - ' + layoutParam.bottomTabHeight + 'px)',
      }"
    >
      <LeftBox
        class="tab"
        :style="{ width: layoutParam.leftTabWidth + 'px' }"
      />
      <!-- 左拖动line -->
      <div class="drag_line" @mousedown="handleMouseDown('left')"></div>

      <div class="preview_box">
        <fwPage></fwPage>
      </div>
      <!-- 右拖动line -->
      <!-- <div class="drag_line" @mousedown="handleMouseDown('right')"></div>

      <RightBox class="tab" :style="{ width: 'calc('+layoutParam.rightTabWidth + 'px)' }"/> -->
    </div>

    <!-- 底部拖动line -->
    <div
      class="drag_line_bottom"
      @mousedown="handleMouseDown('bottom')"
      :style="{ bottom: 'calc(' + layoutParam.bottomTabHeight + 'px)' }"
    ></div>

    <!-- 下半部分console -->
    <BottomBox
      class="console_box"
      :style="{ height: 'calc(' + layoutParam.bottomTabHeight + 'px)' }"
    />
  </div>

  <!-- 预览弹窗 -->
  <div
    v-if="isPriview"
    class="priview"
    :style="{
      right: fixedCoordinate.right + 'px',
      top: fixedCoordinate.top + 'px',
    }"
    @mousedown="handleDragStart"
    @mousemove="handleMouseMove"
    @mouseup="handleMouseUp"
  >
    <img class="phone" src="@/assets/images/priviewPhone.png" />
    <div class="status-bar">
      <img class="status-bar-img" src="@/assets/images/dianchi.png" />
    </div>
    <iframe :src="priviewUrl" :key="iframeKey" frameborder="0" style="overflow: hidden;"></iframe>
    <div class="control">
      <img src="@/assets/images/yulan.png" @click="openDialog" />
      <img src="@/assets/images/shuaxin.png" @click="iframeKey += 1" />
      <img src="@/assets/images/guanbi.png" @click="isPriview = false" />
    </div>
  </div>

  <!-- 二维码弹窗 -->
  <el-dialog v-model="dialogVisible" title="扫码预览" width="300">
    <!-- 二维码显示区域 -->
    <div class="flex_box" style="justify-content: center">
      <img v-if="qrCodeDataUrl" :src="qrCodeDataUrl" alt="二维码" />
      <p v-else>生成二维码中...</p>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import QRCode from "qrcode";
import {
  reactive,
  ref,
  onMounted,
  onUnmounted,
  provide,
  computed,
  watch,
  getCurrentInstance,
  nextTick,
} from "vue";
import { ArrowDown, Close, Refresh, Warning } from "@element-plus/icons-vue";
import { useRouter, useRoute } from "vue-router";
const { emitter } = getCurrentInstance()!.appContext.config.globalProperties;
import {
  apiLoadProject,
  apiReadAllGrammar,
  // apiReadSetupData,
  apiLoadProjectFiles,
  apiAddFile,
  apiLockFile,
  apiUpdateFile,
  apiDeleteFile,
  apiBatchUpdateFile,
  apiCloneFile,
  apiReadImageResources,
  apiSendPublish,
} from "../../apis/designer.js";
import { ElMessage, ElMessageBox } from "element-plus";
import fwPage from "./childPage/fwPage.vue";

const router = useRouter();
const route = useRoute();
const setupData = reactive({
  screenTypes: [
    { title: "iPhone6/7/8", w: 375, h: 667 },
    { title: "iPhone SE", w: 320, h: 568 },
    { title: "iPhone XR", w: 414, h: 896 },
    { title: "iPhone 12 Pro", w: 390, h: 844 },
    { title: "iPhone 14 Pro Max", w: 428, h: 926 },
    { title: "Pixel", w: 480, h: 800 },
    { title: "iPad Mini", w: 768, h: 1024 },
    { title: "Surface Pro 7", w: 834, h: 1112 },
  ],
  screenScales: [
    { title: "180%", value: 180 },
    { title: "120%", value: 120 },
    { title: "100%", value: 100 },
    { title: "90%", value: 90 },
    { title: "80%", value: 80 },
    { title: "70%", value: 70 },
    { title: "60%", value: 60 },
    { title: "50%", value: 50 },
    { title: "40%", value: 40 },
    { title: "30%", value: 30 },
  ],
});
const sizeControl = reactive({
  wh: 390,
  hg: 844,
  scale: {
    title: "100%",
    value: 100,
  },
  screenType: {
    title: "iPhone6/7/8",
    w: 375,
    h: 667,
  },
});
const fixedCoordinate = reactive({
  right: 60,
  top: 100,
});
const dialogVisible = ref(false);
const qrCodeDataUrl = ref(""); // 存储生成的二维码数据URL

provide("sizeControl", sizeControl);

const pjResouceMap = reactive({
	resourceMap:new Map,
})

pjResouceMap.getResourceUrl = (path:string)=>{
	if(path && path.length > 0 && pjResouceMap.resourceMap.has(path)){
		return pjResouceMap.resourceMap.get(path)
	}
	return ""
}
pjResouceMap.setResourceUrl = (path:string,url:string)=>{
	if(path && path.length > 0 && url.length > 0){
		return pjResouceMap.resourceMap.set(path,url)
	}
}

provide("pjResouceMap",pjResouceMap)

const layoutParam = reactive({
  isDragging: false,
  dragType: "left", //当前哪条line在拖动
  leftTabWidth: 300, //左tab宽度
  // rightTabWidth:0,//右tab宽度
  showRightPanel: false,
  bottomTabHeight: 260, //底部tab高度
});

provide("layoutParam", layoutParam);

const ProjectRef = reactive({
  BasicData: {
    ProjectId: 34,
    clientType: 0,
    userInfo: {
      currentUserId: "2345565",
    },
    fileData: {
      setupFile: {
        fileId: 23199,
        title: "项目配置",
        type: "setup",
        activie: 0,
        lock: 0,
        lockUser: "",
        filePath: "setup.top.setup",
      },
      pageFiles: [],
      componentFiles: [],
      eventFiles: [],
      methrodFiles: [],
      serviceFiles: [],
      sourceFiles: [],
    },
    globalData: {
      version: 0,
      varText: "",
    },
    theme: {
      currentTheme: {
        fileId: 9011,
        title: "天蓝色样式方案",
        type: "theme",
        activie: 0,
        lock: 0,
        lockUser: "",
        filePath: "theme.top.current",
        color: "#2B6BFF",
        configs: [],
        keyMap: new Map(),
      },
    },
  },
  DataBase: {
    databaseName: "",
    databaseId: 0,
    validDB: [],
  },
  funcObj: {
    otherFunc: {},
  },
});

const OpenFileMgr = reactive({
  currentFilePath: "",
  listFiles: [],
});
OpenFileMgr.setCurrentPath = (path) => {
  emitter.emit("onChangeFile", {
    newPath: path,
    oldPath: OpenFileMgr.currentFilePath,
  });
  OpenFileMgr.currentFilePath = path;
};
provide("OpenFileMgr", OpenFileMgr);

const GrammerList = reactive({
  list: [],
});
provide("GrammerList", GrammerList);

const showRightMenu = computed(() => {
  if (OpenFileMgr.listFiles.length > 0) {
    for (let r = 0; r < OpenFileMgr.listFiles.length; r++) {
      if (
        OpenFileMgr.listFiles[r].fileItem.filePath ==
        OpenFileMgr.currentFilePath
      ) {
        if (OpenFileMgr.currentFilePath.split(".")[0] == "page") {
          return true;
        } else {
          break;
        }
      }
    }
  }
  return false;
});

const isPriview = ref(false);
const iframeKey = ref(0);
const startX = ref(0);
const startY = ref(0);
const startRight = ref(0);
const startTop = ref(0);
const handlePriview = () => {
	
	// priviewUrl.value = "http://zeno-web2.0-appbox.xwkj.local/?projectId="+ProjectRef.BasicData.ProjectId
	
  if (ProjectRef.BasicData.clientType == 0) {
    isPriview.value = !isPriview.value;
  } else {
    window.open(pcPriviewUrl.value);
  }
};

// 打开对话框并生成二维码
const openDialog = async () => {
  console.log("sd");
  dialogVisible.value = true;
  try {
    console.log("sd");
    // 生成二维码
    qrCodeDataUrl.value = await QRCode.toDataURL(priviewUrl.value, {
      width: 200, // 二维码宽度
      margin: 2, // 二维码边距
      color: {
        dark: "#000000", // 二维码颜色
        light: "#ffffff", // 背景颜色
      },
    });
    console.log("sd");
  } catch (err) {
    console.error("生成二维码失败:", err);
    qrCodeDataUrl.value = "";
  }
};

// 关闭对话框
const closeDialog = () => {
  dialogVisible.value = false;
};

const handleDragStart = (event: MouseEvent) => {
  startX.value = event.clientX;
  startY.value = event.clientY;
  startRight.value = fixedCoordinate.right;
  startTop.value = fixedCoordinate.top;
  handleMouseDown("preview")
};

// const handleDragMove = (event: MouseEvent) => {
//   if (isDragging.value) {
//     const deltaX = event.clientX - startX.value;
//     const deltaY = event.clientY - startY.value;
//     fixedCoordinate.right = startRight.value - deltaX;
//     fixedCoordinate.top = startTop.value + deltaY;
//   }
// };

// const handleDragEnd = () => {
//   isDragging.value = false;
// };

ProjectRef.funcObj.getThemeList = (type: string) => {
  let resList = [];
  if (ProjectRef.BasicData.theme.currentTheme.configs.length > 0) {
    for (
      let n = 0;
      n < ProjectRef.BasicData.theme.currentTheme.configs.length;
      n++
    ) {
      if (ProjectRef.BasicData.theme.currentTheme.configs[n].type == type) {
        resList.push({
          keyName: ProjectRef.BasicData.theme.currentTheme.configs[n].keyName,
          value: ProjectRef.BasicData.theme.currentTheme.configs[n].value,
        });
      }
    }
    if (type == "color") {
      resList.push({
        keyName: "themeMainColor",
        value: ProjectRef.BasicData.theme.currentTheme.color,
      });
    }
  }

  return resList;
};

ProjectRef.funcObj.initThemeKeyMap = () => {
  if (!ProjectRef.BasicData.theme.currentTheme.keyMap) {
    ProjectRef.BasicData.theme.currentTheme.keyMap = new Map();
  }
  ProjectRef.BasicData.theme.currentTheme.keyMap.clear();
  ProjectRef.BasicData.theme.currentTheme.keyMap.set(
    "themeMainColor",
    ProjectRef.BasicData.theme.currentTheme.color
  );
  if (ProjectRef.BasicData.theme.currentTheme.configs.length > 0) {
    for (
      let n = 0;
      n < ProjectRef.BasicData.theme.currentTheme.configs.length;
      n++
    ) {
      ProjectRef.BasicData.theme.currentTheme.keyMap.set(
        ProjectRef.BasicData.theme.currentTheme.configs[n].keyName,
        ProjectRef.BasicData.theme.currentTheme.configs[n].value
      );
    }
  }
};

ProjectRef.funcObj.getValueByThemeKey = (key: string) => {
  if (ProjectRef.BasicData.theme.currentTheme.keyMap.has(key)) {
    return ProjectRef.BasicData.theme.currentTheme.keyMap.get(key);
  } else {
    return "";
  }
};

ProjectRef.funcObj.sendLog = (msg: string) => {
  if (ProjectRef.funcObj.otherFunc.writeLog) {
    ProjectRef.funcObj.otherFunc.writeLog(msg);
  }
};

ProjectRef.funcObj.clearLog = () => {
  if (ProjectRef.funcObj.otherFunc.clearLog) {
    ProjectRef.funcObj.otherFunc.clearLog();
  }
};

ProjectRef.funcObj.getComponentEvents = (key: string) => {
  if (ProjectRef.funcObj.otherFunc.getComponentEvents) {
    return ProjectRef.funcObj.otherFunc.getComponentEvents(key);
  }
  return "";
};

ProjectRef.funcObj.getComponentAttributes = (key: string) => {
  if (ProjectRef.funcObj.otherFunc.getComponentAttributes) {
    return ProjectRef.funcObj.otherFunc.getComponentAttributes(key);
  }
  return "";
};

ProjectRef.funcObj.getComponentOthers = (key:string)=>{
	if(ProjectRef.funcObj.otherFunc.getComponentOthers){
		return ProjectRef.funcObj.otherFunc.getComponentOthers(key)
	}
	return null
}

ProjectRef.funcObj.initTree = () => {
  if (ProjectRef.funcObj.otherFunc.initTree) {
    ProjectRef.funcObj.otherFunc.initTree();
  }
};

ProjectRef.funcObj.refreshTree = () => {
  if (ProjectRef.funcObj.otherFunc.refreshTree) {
    ProjectRef.funcObj.otherFunc.refreshTree();
  }
};

ProjectRef.funcObj.getSourceArray = (path: string) => {
  if (path.length > 0) {
    let oldArray = path.split(".");
    if (oldArray[0] == "page") {
      return ProjectRef.BasicData.fileData.pageFiles;
    } else if (oldArray[0] == "component") {
      return ProjectRef.BasicData.fileData.componentFiles;
    } else if (oldArray[0] == "event") {
      return ProjectRef.BasicData.fileData.eventFiles;
    } else if (oldArray[0] == "methrod") {
      return ProjectRef.BasicData.fileData.methrodFiles;
    } else if (oldArray[0] == "service") {
      return ProjectRef.BasicData.fileData.serviceFiles;
    } else if (oldArray[0] == "resource") {
      return ProjectRef.BasicData.fileData.sourceFiles;
    }
  }
  return [];
};

ProjectRef.funcObj.getSourceItem = (path: string) => {
  if (path.length > 0) {
    if (path == "setup.top.setup") {
      return ProjectRef.BasicData.fileData.setupFile;
    } else if (path == "theme.top.current") {
      return ProjectRef.BasicData.theme.currentTheme;
    }
    let targetArray = ProjectRef.funcObj.getSourceArray(path);
    if (targetArray && targetArray.length > 0) {
      for (let i = 0; i < targetArray.length; i++) {
        if (targetArray[i].filePath == path) {
          return targetArray[i];
        }
      }
    }
  }
  return null;
};

ProjectRef.funcObj.removeSourceItem = (path: string) => {
  let targetArray = ProjectRef.funcObj.getSourceArray(path);
  let pathArray = path.split(".");
  if (
    ".vpg.cpt.evt.mhd.sve.res".indexOf("." + pathArray[pathArray.length - 1]) >=
    0
  ) {
    for (let i = 0; i < targetArray.length; i++) {
      if (targetArray[i].filePath == path) {
        targetArray.splice(i, 1);
        return;
      }
    }
  } else {
    for (let i = targetArray.length - 1; i >= 0; i--) {
      if (targetArray[i].filePath.substring(0, path.length) == path) {
        targetArray.splice(i, 1);
      }
    }
  }
};

ProjectRef.funcObj.loadProjectFiles = (data: object, success) => {
  loadProjectFiles(data, success);
};

ProjectRef.funcObj.openFile = (path: string) => {
  let xItem = ProjectRef.funcObj.getSourceItem(path);
  if (OpenFileMgr.listFiles.length > 0) {
    for (let i = 0; i < OpenFileMgr.listFiles.length; i++) {
      if (path == OpenFileMgr.listFiles[i].fileItem.filePath) {
        OpenFileMgr.setCurrentPath(path);
        return;
      }
    }
  }
  OpenFileMgr.listFiles.push({
    fileItem: xItem,
    isModify: false,
    content: {},
    func: {},
  });
  OpenFileMgr.setCurrentPath(xItem.filePath);
};

ProjectRef.funcObj.closeFile = (path: string) => {
  if (OpenFileMgr.listFiles.length > 0) {
    for (let i = 0; i < OpenFileMgr.listFiles.length; i++) {
      if (path == OpenFileMgr.listFiles[i].fileItem.filePath) {
        if (ProjectRef.funcObj.otherFunc.onClosePage) {
          ProjectRef.funcObj.otherFunc.onClosePage(path, i);
        } else {
          OpenFileMgr.listFiles.splice(i, 1);
        }
      }
    }
  }
};

ProjectRef.funcObj.createFile = (path: string, success) => {
  addFile(path, success);
};

ProjectRef.funcObj.insertCoder = (xText) => {
  if (ProjectRef.funcObj.otherFunc.insertCoder) {
    ProjectRef.funcObj.otherFunc.insertCoder(xText);
  }
};

ProjectRef.funcObj.saveFileData = saveFileData;

function saveFileData(success) {
  if (
    OpenFileMgr.currentFilePath.length > 0 &&
    OpenFileMgr.listFiles.length > 0
  ) {
    for (let i = 0; i < OpenFileMgr.listFiles.length; i++) {
      if (
        OpenFileMgr.listFiles[i].fileItem.filePath ==
        OpenFileMgr.currentFilePath
      ) {
        if (OpenFileMgr.listFiles[i].func.saveData) {
          OpenFileMgr.listFiles[i].func.saveData(success);
        }
      }
    }
  }
}

//手机预览地址
const priviewUrl = ref("");
//PC预览地址
const pcPriviewUrl = ref("")
//编译ws地址
const compileWsUrl = ref("")

function createFileNode(path: string) {
  return {
    fileId: 23199,
    title: "第二个组件.cpt",
    type: "component",
    activie: 0,
    lock: 0,
    lockUser: "",
    filePath: path,
  };
}

function onSelectScale(index: number) {
  sizeControl.scale.title = setupData.screenScales[index].title;
  sizeControl.scale.value = setupData.screenScales[index].value;
  emitter.emit("refreshSizeControl", 2);
}

function onSelectScreen(index: number) {
  sizeControl.screenType = setupData.screenTypes[index];
  sizeControl.wh = sizeControl.screenType.w;
  sizeControl.hg = sizeControl.screenType.h;
  emitter.emit("refreshSizeControl", 1);
}

function onCustomScreen() {
  emitter.emit("refreshSizeControl", 1);
}

const handleMouseDown = (type: string) => {
  layoutParam.dragType = type;
  layoutParam.isDragging = true;
};

const handleMouseMove = (event) => {
  if (layoutParam.isDragging) {
    if (layoutParam.dragType === "left") {
      //左tab宽度
      const maxWidth = window.innerWidth - 8;
      const oldWidth = layoutParam.leftTabWidth;
      layoutParam.leftTabWidth =
        event.clientX < 5
          ? 5
          : event.clientX < maxWidth || event.clientX < oldWidth
          ? event.clientX
          : maxWidth;
    } else if (layoutParam.dragType === "right") {
      //右tab宽度
      //    const width = window.innerWidth - event.clientX;
      // const maxWidth = window.innerWidth - layoutParam.leftTabWidth - 8
      // const oldWidth = layoutParam.rightTabWidth
      //    layoutParam.rightTabWidth = width < 5 ? 5 : width ;
    } else if (layoutParam.dragType === "bottom") {
      //底部tab高度
      const height = window.innerHeight - event.clientY - 4;
      const maxHeight = window.innerHeight - 160;
      const oldHeight = layoutParam.bottomTabHeight;
      layoutParam.bottomTabHeight =
        height < 5
          ? 5
          : height < maxHeight || height < oldHeight
          ? height
          : maxHeight;
    }else if(layoutParam.dragType=="preview"){
		const deltaX = event.clientX - startX.value;
		const deltaY = event.clientY - startY.value;
		fixedCoordinate.right = startRight.value - deltaX;
		fixedCoordinate.top = startTop.value + deltaY;
	}
  }
};

const handleMouseUp = () => {
  if (layoutParam.isDragging) {
    layoutParam.isDragging = false;
  }
};

const handleExit = () => {
  router.back();
};

function checkBlockShow(pos) {
  if (pos == "left") {
    return layoutParam.leftTabWidth > 5;
  } else if (pos == "right") {
    return layoutParam.showRightPanel;
  } else if (pos == "bottom") {
    return layoutParam.bottomTabHeight > 5;
  }
  return false;
}

function setBlockVisible(pos: string) {
  if (pos == "left") {
    if (layoutParam.leftTabWidth > 5) {
      layoutParam.leftTabWidth = 1;
    } else {
      layoutParam.leftTabWidth = 300;
    }
  } else if (pos == "right") {
    layoutParam.showRightPanel = !layoutParam.showRightPanel;
    // if(layoutParam.rightTabWidth > 5){
    // 	layoutParam.rightTabWidth = 1
    // }else{
    // 	layoutParam.rightTabWidth = 300
    // }
  } else if (pos == "bottom") {
    if (layoutParam.bottomTabHeight > 5) {
      layoutParam.bottomTabHeight = 1;
    } else {
      layoutParam.bottomTabHeight = 300;
    }
  }
}

onMounted(() => {
  document.addEventListener("mousemove", handleMouseMove);
  document.addEventListener("mouseup", handleMouseUp);
  ProjectRef.BasicData.ProjectId =
    typeof route.query.projectId == "string"
      ? parseInt(route.query.projectId)
      : route.query.projectId;
  // console.log("读取到项目Id参数=" + ProjectRef.BasicData.ProjectId)
  ProjectRef.funcObj.setBlockVisible = setBlockVisible;
  loadProject({ projectId: ProjectRef.BasicData.ProjectId });
  readAllGrammar();
  
	//手机预览地址
	priviewUrl.value = "http://zeno-web2.0-appbox.xwkj.local/?projectId="+ProjectRef.BasicData.ProjectId
	// priviewUrl.value = "http://192.168.100.65:5174/?projectId="+ProjectRef.BasicData.ProjectId
	//PC预览地址
	pcPriviewUrl.value = "http://zeno-web2.0-pcbox.xwkj.local/?projectId="+ProjectRef.BasicData.ProjectId
	// pcPriviewUrl.value = "http://192.168.100，65:3000/?projectId="+ProjectRef.BasicData.ProjectId
	//编译ws地址
	compileWsUrl.value = "ws://zeno-web2.0-wss.xwkj.local/api/doCompile/"+ProjectRef.BasicData.ProjectId
	// compileWsUrl.value = "ws://192.168.1.212:8112/api/doCompile/"+ProjectRef.BasicData.ProjectId

});

onUnmounted(() => {
  window.removeEventListener("mousemove", handleMouseMove);
  window.removeEventListener("mouseup", handleMouseUp);
});

provide("ProjectRef", ProjectRef);

async function loadProject(data) {
  let res = await apiLoadProject(data);
  if (res.code == 200) {
    ProjectRef.BasicData.clientType = res.data.clientType;
    if (res.data.clientType == 0) {
      sizeControl.wh = 390;
      sizeControl.hg = 844;
    } else {
      sizeControl.wh = 1152;
      sizeControl.hg = 768;
    }
    ProjectRef.BasicData.fileData.setupFile.fileId = res.data.id;
    ProjectRef.DataBase.databaseName = res.data.dataBase.name;
    ProjectRef.DataBase.dataBaseId = res.data.dataBase.id;

    ProjectRef.BasicData.globalData.varText = res.data.globalFile?.content
      ? res.data.globalFile?.content
      : "";
    ProjectRef.BasicData.globalData.version = res.data.globalFile.version;

    ProjectRef.BasicData.theme.currentTheme.fileId = res.data.defaultTheme.id;
    ProjectRef.BasicData.theme.currentTheme.title =
      res.data.defaultTheme.fullname;
    ProjectRef.BasicData.theme.currentTheme.color =
      res.data.defaultTheme.mainColor;

    if (res.data.defaultTheme.configs.length > 0) {
      ProjectRef.BasicData.theme.currentTheme.configs = JSON.parse(
        res.data.defaultTheme.configs
      );
    }

    ProjectRef.funcObj.initThemeKeyMap();

    loadProjectFiles({ path: "page.top" }, () => {
      ProjectRef.funcObj.otherFunc.refreshTree();
    });
	
	readImageResources()  //把当前项目的所有资源装载进来

    ProjectRef.funcObj.sendLog("[ "+data.projectId + " ] 项目基础数据加载成功..");
  }
}

async function loadProjectFiles(data: Object, success) {
  data.projectId = parseInt(ProjectRef.BasicData.ProjectId);
  let res = await apiLoadProjectFiles(data);
  // console.log(data,res)
  if (res.code == 200) {
    if (data.path == "page.top") {
      ProjectRef.BasicData.fileData.pageFiles = res.data;
    } else if (data.path == "component.top") {
      ProjectRef.BasicData.fileData.componentFiles = res.data;
    } else if (data.path == "event.top") {
      ProjectRef.BasicData.fileData.eventFiles = res.data;
    } else if (data.path == "methrod.top") {
      ProjectRef.BasicData.fileData.methrodFiles = res.data;
    } else if (data.path == "service.top") {
      ProjectRef.BasicData.fileData.serviceFiles = res.data;
    } else if (data.path == "resource.top") {
      ProjectRef.BasicData.fileData.sourceFiles = res.data;
    }
	ProjectRef.funcObj.sendLog("加载文件树数据："+data.path)
    if (success) success();
  }
}

async function readAllGrammar() {
  let res = await apiReadAllGrammar();
  if (res.code == 200) {
    GrammerList.list = res.data;
    ProjectRef.funcObj.sendLog("脚本语法库加载成功..");
  }
}

ProjectRef.funcObj.cloneFile = (path, newPath) => {
  cloneFile(path, newPath);
};

async function cloneFile(path: string, newPath: string) {
  let res = await apiCloneFile({
    projectId: ProjectRef.BasicData.ProjectId,
    path: path,
    newPath: newPath,
  });
  if (res.code == 200) {
    let fileNode = {
      fileId: res.data.fileId,
      title: res.data.title,
      type: res.data.type,
      activie: 0,
      lock: res.data.lock,
      lockUser: "",
      filePath: res.data.filePath,
    };
    updateNewFile(fileNode);
  }
}

async function addFile(path: string, success) {
  let res = await apiAddFile({
    projectId: ProjectRef.BasicData.ProjectId,
    path: path,
  });
  if (res.code == 200) {
    let fileNode = {
      fileId: res.data.fileId,
      title: res.data.title,
      type: res.data.type,
      activie: 0,
      lock: res.data.lock,
      lockUser: "",
      filePath: res.data.filePath,
    };
    updateNewFile(fileNode);
    if (success) success(res);
  } else {
    if (success) success(res);
  }
}

ProjectRef.funcObj.checkFileInList = (path) => {
  let pathArray = path.split(".");
  let doArray = [];
  if (pathArray[0] == "page") {
    doArray = ProjectRef.BasicData.fileData.pageFiles;
  } else if (pathArray[0] == "component") {
    doArray = ProjectRef.BasicData.fileData.componentFiles;
  } else if (pathArray[0] == "event") {
    doArray = ProjectRef.BasicData.fileData.eventFiles;
  } else if (pathArray[0] == "methrod") {
    doArray = ProjectRef.BasicData.fileData.methrodFiles;
  } else if (pathArray[0] == "service") {
    doArray = ProjectRef.BasicData.fileData.serviceFiles;
  } else if (pathArray[0] == "resource") {
    doArray = ProjectRef.BasicData.fileData.sourceFiles;
  }
  if (doArray.length > 0) {
    for (let n = 0; n < doArray.length; n++) {
      if (path == doArray[n].filePath) {
        return true;
      }
    }
  }
  return false;
};

ProjectRef.funcObj.updateNewFile = (item) => {
  updateNewFile(item);
};

function updateNewFile(item) {
  let pathArray = item.filePath.split(".");
  if (pathArray.length > 3) {
    if (item.type == "page") {
      if (pathArray.slice(-1) != "vpg") {
        pathArray[pathArray.length] = "vpg";
      }
      ProjectRef.BasicData.fileData.pageFiles.push(item);
    } else if (item.type == "component") {
      if (pathArray.slice(-1) != "cpt") {
        pathArray[pathArray.length] = "cpt";
      }
      ProjectRef.BasicData.fileData.componentFiles.push(item);
    } else if (item.type == "event") {
      if (pathArray.slice(-1) != "evt") {
        pathArray[pathArray.length] = "evt";
      }
      ProjectRef.BasicData.fileData.eventFiles.push(item);
    } else if (item.type == "methrod") {
      if (pathArray.slice(-1) != "mhd") {
        pathArray[pathArray.length] = "mhd";
      }
      ProjectRef.BasicData.fileData.methrodFiles.push(item);
    } else if (item.type == "service") {
      if (pathArray.slice(-1) != "sve") {
        pathArray[pathArray.length] = "sve";
      }
      ProjectRef.BasicData.fileData.serviceFiles.push(item);
    } else if (item.type == "resource") {
      if (pathArray.slice(-1) != "res") {
        pathArray[pathArray.length] = "res";
      }
      ProjectRef.BasicData.fileData.sourceFiles.push(item);
    }
    ProjectRef.funcObj.refreshTree();
  }
}

ProjectRef.funcObj.lockFile = (path: string, lock: number, success) => {
  lockFile(path, lock, success);
};

async function lockFile(path, lock, success) {
  // console.log("lockFile,path="+path)
  let res = await apiLockFile({
    projectId: parseInt(ProjectRef.BasicData.ProjectId),
    path: path,
    isLock: lock,
  });
  if (res.code == 200) {
    if (success) success(res.data);
  }
}

ProjectRef.funcObj.updateFile = (data, success) => {
  updateFile(data, success);
};

async function updateFile(data, success) {
  data.projectId = parseInt(ProjectRef.BasicData.ProjectId);
  let res = await apiUpdateFile(data);
  if (res.code == 200) {
    if (success) success(res.data);
  }
}

ProjectRef.funcObj.deleteFile = (path, success) => {
  deleteFile(path, success);
};

async function deleteFile(path, success) {
  let res = await apiDeleteFile({
    projectId: parseInt(ProjectRef.BasicData.ProjectId),
    path: path,
  });
  if (res.code == 200) {
    if (success) success();
  }
}

ProjectRef.funcObj.batchUpdateFile = (data, success) => {
  batchUpdateFile(data, success);
};

const compiler = reactive({
    ws:null,
	connectStatus:false
})

ProjectRef.funcObj.refreshCompileStatus = ()=>{
	if(ProjectRef.funcObj.otherFunc.updateCompileStatus){
		ProjectRef.funcObj.otherFunc.updateCompileStatus(compiler.ws && compiler.ws.readyState === WebSocket.OPEN)
	}
	// return compiler.ws && compiler.ws.readyState === WebSocket.OPEN
	// return compiler.ws && compiler.ws.connectStatus
}

ProjectRef.funcObj.stopCompile = ()=>{
	compiler.ws.close()
}

compiler.handleCompile = ()=>{
	// compiler.ws.connectStatus = (compiler.ws && compiler.ws.readyState == WebSocket.OPEN)
	if(compiler.ws && compiler.ws.readyState === WebSocket.OPEN){
		 ElMessageBox.confirm(
		     '正在编译中，是否要停止当前编译..?',
		     '提示',
		     {
		       confirmButtonText: '立即停止',
		       cancelButtonText: '保持继续',
		       type: 'warning',
		     }
		   )
		     .then(() => {
		        compiler.ws.close()
		     })
		     .catch(() => {
		       
		     })
	}else{
		const token = localStorage.getItem("token");
		if (token) { 
		  // compiler.ws = new WebSocket("ws://192.168.1.212:8112/api/doCompile/"+ProjectRef.BasicData.ProjectId);
		  compiler.ws = new WebSocket(compileWsUrl.value);
		  compiler.ws.onopen = () => {
			// 发送认证消息
			compiler.ws.send("Bearer "+token);
			ProjectRef.funcObj.refreshCompileStatus()
			// compiler.ws.connectStatus = true
			ProjectRef.funcObj.sendLog("编译 开始...")
		  };
		  
		  compiler.ws.onmessage = (event) => {
			  ProjectRef.funcObj.sendLog("编译: "+event.data)
		  };
		  
		  compiler.ws.onerror = (event) => {
			  // compiler.ws.connectStatus = false
			  ProjectRef.funcObj.refreshCompileStatus()
			  ProjectRef.funcObj.sendLog("编译错误: "+event)
		  }
		  
		  compiler.ws.onclose = (event)=>{
			  // compiler.ws.connectStatus = false
			  ProjectRef.funcObj.refreshCompileStatus()
			  ProjectRef.funcObj.sendLog("编译关闭 "+event.reason)
			  nextTick(()=>{
				  ProjectRef.funcObj.sendLog("")
			  })
		  }
		  
		}else{
			ProjectRef.funcObj.sendLog("编译 Token不能为空")
		}
	}
}

function onPublish(){
	ElMessageBox.confirm("确定要发布当前项目吗？",'发布',
		{
		confirmButtonText: '发布',
		cancelButtonText: '取消',
		type: 'warning',
		}
		)
		.then(() => {
		   sendPublish(parseInt(ProjectRef.BasicData.ProjectId))
		})
		.catch(() => {

		})
}

async function sendPublish(projectId){
	ProjectRef.funcObj.sendLog("开始发布项目...")
	let res = await apiSendPublish({
		projectId:projectId
	})
	if(res.code ==200){
		ElMessage({
		   message:"发布成功",
		   type:"success"
	    })
		ProjectRef.funcObj.sendLog("项目发布成功 ...")
	}
}

async function batchUpdateFile(data, success) {
  data.projectId = parseInt(ProjectRef.BasicData.ProjectId);
  let res = await apiBatchUpdateFile(data);
  if (res.code == 200) {
    let fileType = data.newPath.split(".")[0];
    if (fileType == "page") {
      ProjectRef.BasicData.fileData.pageFiles = res.data;
    } else if (fileType == "component") {
      ProjectRef.BasicData.fileData.componentFiles = res.data;
    } else if (fileType == "event") {
      ProjectRef.BasicData.fileData.eventFiles = res.data;
    } else if (fileType == "methrod") {
      ProjectRef.BasicData.fileData.methrodFiles = res.data;
    } else if (fileType == "service") {
      ProjectRef.BasicData.fileData.serviceFiles = res.data;
    } else if (fileType == "resource") {
      ProjectRef.BasicData.fileData.sourceFiles = res.data;
    }
    if (success) success();
  }
}

async function readImageResources(){
	let res = await apiReadImageResources(ProjectRef.BasicData.ProjectId)
	if(res.code==200 && res.data && res.data.length > 0){
		pjResouceMap.resourceMap.clear()
		for(let i=0;i<res.data.length;i++){
			pjResouceMap.setResourceUrl(res.data[i].path,res.data[i].urlPath)
		}
		console.log(res.data,pjResouceMap)
	}
}
</script>

<style lang="scss" scoped>
.top_tools {
  height: 60px;
  background-color: #f5f5f5;
  min-width: 1000px;

  .menu {
    .title {
      padding-left: 24px;

      img {
        width: 40px;
        height: 40px;
      }

      div {
        margin: 0 52px 0 16px;
        font-weight: 600;
        font-size: 20px;
        color: #212121;
        line-height: 24px;
      }
    }

    .dropdown {
      border: none;

      .name {
        font-weight: 500;
        font-size: 15px;
        color: #212121;
        line-height: 24px;
        margin-right: 4px;
      }
    }
  }

  .tools {
    :deep(.el-button) {
      border: none;
      width: 71px;
      height: 36px;
      background: #e5ecfa !important;
      font-weight: 600;
      font-size: 14px;
      color: #2b6bff;
      margin-right: 2px !important;
    }

    :deep(.el-button:hover) {
      background: #bfc5d0 !important;
    }
  }
}

.size_tools {
  height: 32px;
  background: #fafafa;
  box-shadow: 0px 1px 0px 0px #e5e5e5;
  min-width: 500px;

  .name {
    font-weight: 600;
    font-size: 12px;
    color: #212121;
    line-height: 24px;
    margin-right: 10px;
  }

  .section {
    margin: 0 12px;

    span {
      margin: 0 8px;
      font-size: 12px;
      color: #212121;
      line-height: 16px;
    }
  }
}

.container {
  max-width: 100vw;
  height: calc(100vh - 95px);
  max-height: calc(100vh - 95px);
  position: relative;
  background-color: #f5f5f5;

  .top_content {
    position: absolute;
    top: 0px;
    right: 0px;
    left: 0px;
    background-color: #f5f5f5;

    .tab {
      height: 100%;
      flex-shrink: 0;
      overflow-x: auto;
    }

    .drag_line {
      width: 3px;
      height: 100%;
      cursor: col-resize;
      background-color: transparent;
      z-index: 998;
    }

    .preview_box {
      height: 100%;
      flex-grow: 1;
      text-align: center;
      overflow: auto;
      background-color: #f5f5f5;
      user-select: none;
    }
  }

  .drag_line_bottom {
    position: absolute;
    left: 0px;
    right: 0px;
    height: 3px;
    background-color: transparent;
    cursor: row-resize;
    z-index: 998;
  }

  .console_box {
    position: absolute;
    bottom: 0px;
    left: 0px;
    right: 0px;
    flex-shrink: 0;
    overflow-y: hidden;
    background-color: #f5f5f5;
  }
}

:deep(.el-dropdown:hover) {
  border: none !important;
}

.btn_icon {
  width: 20px;
  height: 20px;
}

.priview {
  position: fixed;
  width: 375px;
  height: 762px;
  box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.1);
  border-radius: 60px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: move;
  user-select:none;

  .phone {
    width: 375px;
    height: 762px;
    position: absolute;
    top: 0;
    left: 0;
    pointer-events: none;
  }

  .status-bar {
    width: 100%;
    box-sizing: border-box;
    height: 49px;
    padding: 16px 20px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #efefef;
    border-radius: 60px 60px 0 0;
    overflow: hidden;
    .status-bar-img {
      display: block;
      width: 100%;
      height: 100%;
      transform: translateY(4px);
    }
  }

  iframe {
    width: 330px;
    height: 690px;
    background-color: #ffffff;
  }

  .control {
    position: absolute;
    top: -40px;
    right: 40px;
    box-sizing: border-box;
    padding: 0 16px;
    width: 120px;
    height: 36px;
    background: #ffffff;
    box-shadow: 0px 4px 15px 0px rgba(0, 0, 0, 0.1);
    border-radius: 12px 12px 12px 12px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    img {
      width: 20px;
      height: 20px;
      margin-left: 8px;
      cursor: pointer;
    }
  }
}
</style>
