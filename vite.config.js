import { defineConfig } from "vite";
import path from "path";
import vue from "@vitejs/plugin-vue";
import AutoImport from "unplugin-auto-import/vite";
import Components from "unplugin-vue-components/vite";
import { ElementPlusResolver } from "unplugin-vue-components/resolvers";

// https://vitejs.dev/config/
export default defineConfig({
  resolve: {
    api: "modern-compiler",
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  plugins: [
    vue(),
    AutoImport({
      resolvers: [ElementPlusResolver()],
    }),
    Components({
      resolvers: [
        ElementPlusResolver({
          importStyle: "css", // 如果需要自定义主题，可以设置为 'sass'
          icons: true, // 自动引入图标
        }),
      ],
    }),
  ],
  define: {
      // 关键配置
      __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: JSON.stringify(false),
      __VUE_PROD_DEVTOOLS__: JSON.stringify(false)
  },
  server: {
    proxy: {
      "/devApi": {
        target: "http://*************:8112",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/devApi/, ""),
      },
	  "/yhyApi": {
	    target: "http://localhost:8700",
	    changeOrigin: true,
	    rewrite: (path) => path.replace(/^\/yhyApi/, ""),
	  },
    },
  },
});
