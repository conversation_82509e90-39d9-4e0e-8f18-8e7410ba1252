<template>
	<div class="demo-collapse content">
	    <el-collapse v-model="activeNames">
	      <el-collapse-item v-for="(item,index) in componentMgr.groupComponentList" :key="index" :name="item.value">
			<template #title>
			  <span class="collapse-item-title">{{item.title}}</span>
			</template>
	        <ComponentList :dataList="componentFilterByGroup(item.value)"/>
	      </el-collapse-item>
	    </el-collapse>
	  </div>
</template>
<script lang="ts" setup>
	import {reactive,ref ,onMounted,inject} from 'vue'
	import ComponentList from './ComponentList.vue'
	import {readSystemComponents } from '../../../apis/designer.js'
	const ProjectRef = inject("ProjectRef")
	
	const activeNames = ref([])
	
	const eventMap = new Map
	const attriMap = new Map
	const othersMap = new Map
	
	const componentMgr = reactive({
		groupComponentList:[
			{
				title:"基本组件",
				value:'basic'
			},
			{
				title:"布局组件",
				value:'layout'
			},
			{
				title:"列表组件",
				value:'list'
			},
			{
				title:"高级组件",
				value:'expand'
			},
			{
				title:"自定义组件",
				value:'custom'
			}
		],
		libraryData:[]
	})
	
	function componentFilterByGroup(groupName:string){
		var app = 0
		if(ProjectRef.BasicData.clientType == 0) {
			// app
			app = 1
		} else{
			// pc
			app = 2
		}

		return componentMgr.libraryData.filter((item)=>{
			return item.group == groupName && (item.scopeType == 0 || item.scopeType == app)
		})
	}

	onMounted(()=>{
		componentLibrary()
	})
	
	ProjectRef.funcObj.otherFunc.getComponentEvents = (key)=>{
		if(eventMap.has(key)){
			return eventMap.get(key)
		}else if(key=="TCustomCmpt"){
			return "onClicked,onDbClicked"
		}
		return ""
	}
	
	ProjectRef.funcObj.otherFunc.getComponentAttributes = (key)=>{
		if(attriMap.has(key)){
			return attriMap.get(key)
		}else if(key=="TCustomCmpt"){
			return "basic,background,box,position,css"
		}
		return ""
	}
	
	ProjectRef.funcObj.otherFunc.getComponentOthers = (key)=>{
		if(othersMap.has(key)){
			return othersMap.get(key)
		}
		return null
	}
	
	async function componentLibrary(){
		// let res = await apiComponentLibrary({});
		let res = await readSystemComponents()
		if(res.code == 200){ 
			// componentMgr.libraryData = res.data
			eventMap.clear()
			attriMap.clear()
			componentMgr.libraryData = []
			if(res.data.length > 0){
				for(let i=0;i<res.data.length;i++){
					let itemJson = res.data[i].jsonStr
					itemJson['scopeType'] = res.data[i].scopeType
					componentMgr.libraryData.push(itemJson)
					let eventList = ""
					if(itemJson.hasOwnProperty("events")){
						eventList = itemJson.events
					}
					eventMap.set(itemJson.key,eventList)
					
					let attriList = ""
					if(itemJson.hasOwnProperty("attributes")){
						attriList = itemJson.attributes
					}
					attriMap.set(itemJson.key,attriList)
					
					if(itemJson.hasOwnProperty("others")){
						othersMap.set(itemJson.key,itemJson.others)
					}
					// let m = componentMgr.libraryData[i].group
					// let type = (m=='basic' ? 0 : (m=='layout' ? 1 : (m=='list' ? 2 : 5)))
					// addComponents(componentMgr.libraryData[i],type)
				}
			}
		}
	}
	
	// async function addComponents(json,type){
	// 	let data = {
	// 		    title: json.key, // 组件名称
	// 		    icon: json.icon, //图标地址
	// 		    jsonStr: json,
	// 			type:type
	// 	}
	// 	let res = await apiTestAddComponent(data)
	// 	if(res.code==200){
	// 		console.log("保存了:",json)
	// 	}
		
	// }
	
</script>
<style lang="scss" scoped>
	 .content {
		 
	 }
	 .collapse-item-title{
		 font-size: 16px;
	 }
</style>