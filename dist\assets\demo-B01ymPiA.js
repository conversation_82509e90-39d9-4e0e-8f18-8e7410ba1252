import{E as n}from"./el-button-B8TCQS4u.js";import{c as r}from"./utils-C6-AL1wg.js";import{g as a,f as c,o as m,k as p,E as t}from"./index-Dgb0NZ1J.js";import"./index-DzgKufUD.js";import"./index-C-aKrRv3.js";import"./index-CINbulG0.js";import"./vnode-CV7pw3rS.js";const k={__name:"demo",setup(_){const e=()=>{r("自定义提示内容。。。").then(()=>{t.success("点击了保存按钮")}).catch(()=>{console.log("不保存"),t.warning("点击了不保存按钮")})};return(i,o)=>{const s=n;return m(),a(s,{onClick:e},{default:c(()=>o[0]||(o[0]=[p("显示自定义消息框")])),_:1,__:[0]})}}};export{k as default};
