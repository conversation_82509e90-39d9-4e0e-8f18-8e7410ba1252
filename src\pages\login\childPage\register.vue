<template>
  <div class="login_title">欢迎注册</div>
  <div class="reset_desc">
    欢迎注册平台会员，如果您已拥有账户，则可以在此
    <el-button
      type="primary"
      link
      style="font-size: 14px; color: #2b6bff; padding: 0; border: null"
      @click="handleToLogin"
    >
      登录
    </el-button>
  </div>
  <el-form ref="formRef" :model="formData" :rules="rules" status-icon>
    <el-form-item prop="mobile">
      <el-input
        v-model="formData.mobile"
        placeholder="请输入手机号码"
        class="input"
        :maxlength="11"
      />
    </el-form-item>
    <el-form-item prop="vcode" class="form_item">
      <el-input
        v-model="formData.vcode"
        placeholder="请输入验证码"
        class="input"
        :maxlength="6"
      />
      <div class="code_container">
        <div v-if="isSend">{{ count }}s</div>
        <div
          v-else
          :style="{
            color: formData.mobile.length === 11 ? '#2B6BFF' : '#999999',
          }"
          v-debounce="handleSendCode"
        >
          发送验证码
        </div>
      </div>
    </el-form-item>
    <el-form-item prop="name">
      <el-input
        v-model="formData.name"
        placeholder="请输入昵称"
        class="input"
      />
    </el-form-item>
    <el-form-item prop="password">
      <el-input
        v-model="formData.password"
        placeholder="请输入密码"
        class="input"
        type="password"
        show-password
      />
    </el-form-item>
    <el-form-item prop="agreement" style="margin: 0">
      <div class="read_forget">
        <div class="read_text">
          <el-checkbox v-model="formData.agreement" />
          阅读并接受
          <a href="https://baidu.com" target="_blank" rel="noopener noreferrer">
            用户协议
          </a>
          和
          <a href="https://baidu.com" target="_blank" rel="noopener noreferrer">
            隐私政策
          </a>
        </div>
      </div>
    </el-form-item>
  </el-form>

  <el-button
    type="primary"
    class="submit_btn"
    v-debounce="handleSubmit"
    :loading="loading"
  >
    注册
  </el-button>
</template>

<script setup>
import { useRouter } from "vue-router";
import { reactive, ref } from "vue";
import { register, fetchPhoneCode } from "@/apis/loginApi";
import { ElMessage } from "element-plus";

const router = useRouter();

const rules = reactive({
  mobile: [
    { required: true, message: "请输入手机号", trigger: "blur" },
    {
      pattern: /^1[3-9]\d{9}$/,
      message: "请检查手机号码是否有误",
      trigger: "change",
    },
  ],
  password: [
    {
      required: true,
      message: "请输入密码",
      trigger: "change",
    },
  ],
  vcode: [
    {
      required: true,
      message: "请输入验证码",
      trigger: "change",
    },
  ],
  name: [
    {
      required: true,
      message: "请输入昵称",
      trigger: "change",
    },
  ],
  agreement: [
    {
      validator: (rule, value, callback) => {
        if (!value) {
          callback(new Error("请阅读并接受用户协议和隐私政策"));
        } else {
          callback();
        }
      },
      trigger: "change",
    },
  ],
});

const formRef = ref();

const isSend = ref(false);
const count = ref(60);
const timer = ref(null);
const loading = ref(false);
const formData = reactive({
  mobile: "",
  password: "",
  vcode: "",
  name: "",
  agreement: false,
  workcity: 4403,
});

const handleToLogin = () => {
  router.push("/login/index");
};

//倒计时
const handleCountDown = () => {
  isSend.value = true;
  count.value = 60;

  timer.value = setInterval(() => {
    if (count.value === 0) {
      isSend.value = false;
      clearInterval(timer.value);
    } else {
      count.value--;
    }
  }, 1000);
};

const handleSendCode = async () => {
  if (/^1[3-9]\d{9}$/.test(formData.mobile)) {
    try {
      await fetchPhoneCode(formData.mobile);
      ElMessage({
        message: "发送成功！",
        type: "success",
      });
      handleCountDown();
    } catch {}
  }
};

const handleSubmit = () => {
  formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        loading.value = true;
        await register(formData);
        ElMessage({
          message: "注册成功！",
          type: "success",
        });
        loading.value = false;
        router.push("/login/index");
      } catch {
        loading.value = false;
      }
    }
  });
};
</script>

<style>
.reset_desc {
  font-size: 14px;
  color: #212121;
  line-height: 16px;
  margin: 24px 0 16px;
}
</style>
