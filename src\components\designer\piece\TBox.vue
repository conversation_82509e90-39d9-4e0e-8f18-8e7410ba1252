<template>
	<div class="content"  @dragover="props.TBase.onDragover" @drop.stop="props.TBase.onDrop" 
	     @dragenter.stop="props.TBase.onMouseEnter" @dragleave.stop="props.TBase.onMouseLeave" 
		 :style="props.TBase.getLayoutStyle()">
		<TComponents v-for="(childItem,r) in props.TItem.children" :key= "r" :TItem="childItem" :TParent="props.TItem" :TIndex="r"></TComponents>
	</div>
</template>

<script lang="ts" setup>
	import {reactive,ref,onMounted,inject,watch} from 'vue'
	
	const props = defineProps({
		TItem:{
			type:Object,
			default:{}
		},
		TParent:{
			type:Object,
			default:{id:'10001'}
		},
		TBase:{
			type:Object,
			default:{
				onDragover:(e)=>{},
				onDrop:(e)=>{},
				onMouseEnter:(e)=>{},
				onMouseLeave:(e)=>{},
				getLayoutStyle:()=>{return{}}
			}
		}
	})
	
	onMounted(()=>{
		props.TItem.parent = props.TParent
	})
</script>

<style lang="scss" scoped>
	.content {
		height:calc(100% - 3px);
		width:calc(100% - 3px);
		background-color: transparent;
		border:0px;
	}
</style>