<template>
	<div class="content file-box">methrodFile</div>
</template>

<script lang="ts" setup>
	import {reactive,ref,onMounted} from 'vue'
	import { useFileBase } from '../../../common/fileBasic';
	const fileBase = useFileBase()
	const props = defineProps({
		fileItem:{
			type:Object,
			default:{}
		}
	})
	onMounted(()=>{
		// console.log(fileBase.testStr)
		
	})
</script>

<style lang="scss" scoped>
	 .content {
		 border:1px solid green;
	 }
</style>