import{E as I}from"./el-button-B8TCQS4u.js";import{E as x}from"./el-overlay-CF-bF7w3.js";import{b as D}from"./el-progress-BWXbzDlP.js";import{e as g,ag as M,c as n,o as r,b as a,f as s,a as _,g as N,D as y,y as j,v,G as C,t as G,a8 as b,am as L,al as $,E as c}from"./index-Dgb0NZ1J.js";import{_ as A}from"./plugin-vue_export-helper-DlAUqK2U.js";const P={class:"image-uploader"},O=["src"],F={key:2,class:"upload-text"},J={key:3,class:"action-overlay"},R={class:"action-buttons"},q=["src"],H={__name:"UploadImg",props:{modelValue:String,uploadUrl:{type:String,default:"/devApi/file/imageUpload"},headers:{type:Object,default:()=>({Authorization:`Bearer ${localStorage.getItem("token")}`})},uploadData:{type:Object,default:()=>({source:0})},disabled:{type:Boolean,default:!1},placeholder:String,accept:{type:String,default:"image/jpeg,image/png,image/gif"},sizeLimit:{type:Number,default:2}},emits:["update:modelValue","success","error"],setup(o,{emit:V}){const m=o,i=V,t=g(""),p=g(!1),u=g(!1);M(()=>m.modelValue,e=>{t.value=e},{immediate:!0});const k=e=>{const l=["image/jpeg","image/png","image/gif"].includes(e.type),d=e.size/1024/1024<m.sizeLimit;return l?d?!0:(c.error(`图片大小不能超过 ${m.sizeLimit}MB!`),!1):(c.error("只能上传 JPG/PNG/GIF 格式的图片!"),!1)},E=e=>{e.code===200?(t.value=e.data,i("update:modelValue",e.data),i("success",e),c.success(e.message||"上传成功")):(c.error(e.message||"上传失败"),i("error",e))},w=e=>{c.error("上传失败: "+(e.message||"未知错误")),i("error",e)},B=()=>{p.value=!0},S=()=>{t.value="",i("update:modelValue",""),u.value=!1};return(e,l)=>{const d=j,h=I,U=D,z=x;return r(),n("div",P,[a(U,{action:o.uploadUrl,headers:o.headers,data:o.uploadData,name:"file","show-file-list":!1,"on-success":E,"on-error":w,"before-upload":k,accept:o.accept,disabled:o.disabled},{trigger:s(()=>[_("div",{class:"upload-area",onMouseenter:l[0]||(l[0]=f=>u.value=!0),onMouseleave:l[1]||(l[1]=f=>u.value=!1)},[t.value?(r(),n("img",{key:0,src:t.value,class:"upload-image"},null,8,O)):(r(),N(d,{key:1,class:"upload-icon"},{default:s(()=>[a(v(C))]),_:1})),t.value?y("",!0):(r(),n("div",F,G(o.placeholder||"点击上传图片"),1)),t.value&&u.value?(r(),n("div",J,[_("div",R,[a(h,{type:"primary",circle:"",onClick:b(B,["stop"])},{default:s(()=>[a(d,null,{default:s(()=>[a(v(L))]),_:1})]),_:1}),a(h,{type:"danger",circle:"",onClick:b(S,["stop"])},{default:s(()=>[a(d,null,{default:s(()=>[a(v($))]),_:1})]),_:1})])])):y("",!0)],32)]),_:1},8,["action","headers","data","accept","disabled"]),a(z,{modelValue:p.value,"onUpdate:modelValue":l[2]||(l[2]=f=>p.value=f),title:"图片预览",width:"50%"},{default:s(()=>[_("img",{src:t.value,class:"preview-image"},null,8,q)]),_:1},8,["modelValue"])])}}},Y=A(H,[["__scopeId","data-v-758be7ce"]]);export{Y as _};
