import {reactive,ref,inject,onMounted,toRaw} from 'vue'

export function useAttributeCore(currentCptAttribute){
	
	const ProjectRef = inject("ProjectRef")
	const FileScript = inject("FileScript")
	
	currentCptAttribute.func.initBackground = ()=>{
		return {
			backgroundImageOpen:false,
			backgroundImageUrl:"",
			backgroundColor:"#ffffff",
			backgroundExpression:"",
			theme:false,
			themeKey:"",
			gradientOpen:false,
			gradientColor2:"",
			gradientAngle:90,
		}
	}
	currentCptAttribute.func.initFont = ()=>{
		let fontStyle =  {
			fontFamilyTheme:false,
			fontFamilyThemeKey:"",
			fontFamily:"Microsoft YaHei",
			fontSizeTheme:false,
			fontSizeThemeKey:"",
			fontSize:14,
			fontSizeExpression:"",
			fontColorTheme:false,
			fontColorThemeKey:"",
			fontColor:ProjectRef.BasicData.theme.currentTheme.color,
			fontColorExpression:"",
			textDecoration:"none",
			textAlign:"center",
			fontWeight:400,
		}
		return fontStyle
	}
	
	currentCptAttribute.func.initPlaceHolder = ()=>{
		return {
			placeHolderText:""
		}
		
	}
	
	currentCptAttribute.func.initBox = ()=>{
		let boxStyle =  {
			paddingLeft:0,
			paddingRight:0,
			paddingTop:0,
			paddingBottom:0,
			marginLeft:0,
			marginRight:0,
			marginTop:0,
			marginBottom:0,
			radiusTheme:false,
			radiusThemeKey:"",
			radiusLeftTop:0,
			radiusRightTop:0,
			radiusLeftBottom:0,
			radiusRightBottom:0,
			opacity:100,
			scale:100,
			cursorType:'default',
			borderTheme:false,
			borderThemeKey:"",
			borderSize:1,
			borderStyle:'solid',
			borderColor:ProjectRef.BasicData.theme.currentTheme.color,
			borderExpression:"",
			borderLeft:true,
			borderRight:true,
			borderTop:true,
			borderBottom:true,
			shadowType:"none",
			shadowColor:"#e2e2e2",
			shadowOffsetX:0,
			shadowOffsetY:0,
			shadowBlur:0,
			shadowSpread:0
		}
	
		return boxStyle
	}
	currentCptAttribute.func.initLayout = ()=>{
		return {
			layoutDirection:"absolute",
			hAlign:"align-center",
			vAlign:"align-center",
			scrollHOpen:false,
			scrollVOpen:false,
		}
	}
	currentCptAttribute.func.initLine = ()=>{
		return {
			direct:"horizontal",
			borderSize:1,
			borderStyle:'solid',
			borderColor:ProjectRef.BasicData.theme.currentTheme.color,
			borderExpression:"",
		}
	}
	
	currentCptAttribute.func.initCss = ()=>{
		return {
			str:""
		}
	}
	
	currentCptAttribute.func.initChoose = (type)=>{
		let res = {
			direct:'horizontal',
			dataList:[
				{
					label:"item1",
					value:'value1'
				},
				{
					label:"item2",
					value:'value2'
				}
			],
			dataExpression:""
		}
		if(type=='TRadio' || type=="TCheckBox"){
		   res.currentValue =  (type=='TRadio' ? 'value1' : ['value1'])
		}else if(type=='TSelect'){
			res.valueLabel = ""
		}
		
		return res
	}
	
	currentCptAttribute.func.initIcon = ()=>{
		return {
			iconPath:"PictureRounded",
			iconPathExpression:"",
			iconSize:20,
			iconColor:ProjectRef.BasicData.theme.currentTheme.color
		}
	}
	
	currentCptAttribute.func.initProper = (key)=>{
		let nAttr = {}
		if(key=="TSwitch"){
			nAttr.activeColor = ProjectRef.BasicData.theme.currentTheme.color
			nAttr.disActiveColor = "#d0d0d0"
		}
		return nAttr
	}
	
	function addPageItems(){
		if(currentCptAttribute.attributeSetup.tabs.setup.pages.items.length >= 7) {
			ElMessage({type: 'warning',message: '最多只能添加7个tab页！',})
			return
		}
		currentCptAttribute.attributeSetup.tabs.setup.pages.items.push(createTabPage())
		 
		 if(currentCptAttribute.attributeSetup.tabs.setup.addItem){
			 currentCptAttribute.attributeSetup.tabs.setup.addItem()
		 }
	}
	
	function deletePageItems(index){
		currentCptAttribute.attributeSetup.tabs.setup.pages.items.splice(index,1)
		if(currentCptAttribute.attributeSetup.tabs.setup.removeItem){
					 currentCptAttribute.attributeSetup.tabs.setup.removeItem(index)
		}
	}
	
	currentCptAttribute.func.initTabs = ()=>{
		let nAttr = {
			currentPage:0,
			currentExpression:"",
			openAnim:false, //页面之间切换是否开启动画效果
			navigateBackground:"#eAeaea",
			pages:{
				items:[]
			}
		}
		nAttr.pages.items.push(createTabPage())
		return nAttr
	}
	
	function createTabPage(){
		return {
		 	activePath:"",
		 	activePathExpression:"",
		 	deactivePath:"",
		 	deactivePathExpression:"",
		 	
		 	activeColorThemeOpen:false,
		 	activeColorTheme:"",
		 	activeColor:"#222222",
		 	activeColorExpression:"",
		 	
		 	deactiveColorThemeOpen:false,
		 	deactiveColorTheme:"",
		 	deactiveColor:"#B3B3B3",
		 	deactiveColorExpression:"",
		 	
		 	title:"标题",
		 	titleExpression:"",
		 	
		 	activeFontSize:"14",
		 	activeFontSizeExpression:"",
		 	deactiveFontSize:"14",
		 	deactiveFontSizeExpression:"",
		 	
		 	badgeNum:0,
		 	badgeNumExpression:""
		 }
	}
	
	
	function onLineChange(d){
		console.log("line direct: "+d)
		let w = currentCptAttribute.attributeSetup.basic.setup.w
		let h = currentCptAttribute.attributeSetup.basic.setup.h
		let n = w > h ? w : h
		let k = w <= h ? w : h
		if(d=='horizontal'){
			currentCptAttribute.attributeSetup.basic.setup.w = n
			currentCptAttribute.attributeSetup.basic.setup.h = k
		}else if(d=='vertical'){
			currentCptAttribute.attributeSetup.basic.setup.w = k
			currentCptAttribute.attributeSetup.basic.setup.h = n
		}
	}
	
	
	function openCssSetup(){
		onRefreshCssSetup()
		currentCptAttribute.showCssSetup = !currentCptAttribute.showCssSetup
	}
	
	function doCssToStr(cStyle){
		console.log("csss=",cStyle)
		let resStr = ""
		Object.keys(cStyle).forEach(key => {
		   resStr += key+":"+cStyle[key]+";\n"
		});
		return resStr
	}
	
	function onRefreshCssSetup(){
		if(currentCptAttribute.attributeSetup.basic.setup && currentCptAttribute.attributeSetup.basic.setup.cssFunc){
			if(currentCptAttribute.attributeSetup.css.show){
				currentCptAttribute.attributeSetup.css.setup.str = doCssToStr(toRaw(currentCptAttribute.attributeSetup.basic.setup.cssFunc()))
			}
		}
	}
	
	function openActive(v){
		if(v){
			if(currentCptAttribute.attributeSetup.basic.show){
				if(!currentCptAttribute.attributeSetup.basic.setup.attribute.active.hasOwnProperty("backgroundColorOpen")){
					currentCptAttribute.attributeSetup.basic.setup.attribute.active.backgroundColorOpen = false
				}
				if(!currentCptAttribute.attributeSetup.basic.setup.attribute.active.hasOwnProperty("backgroundColor")){
					currentCptAttribute.attributeSetup.basic.setup.attribute.active.backgroundColor = ""
				}
				if(!currentCptAttribute.attributeSetup.basic.setup.attribute.active.hasOwnProperty("colorOpen")){
					currentCptAttribute.attributeSetup.basic.setup.attribute.active.colorOpen = false
				}
				if(!currentCptAttribute.attributeSetup.basic.setup.attribute.active.hasOwnProperty("color")){
					currentCptAttribute.attributeSetup.basic.setup.attribute.active.color = ""
				}
				if(!currentCptAttribute.attributeSetup.basic.setup.attribute.active.hasOwnProperty("scaleOpen")){
					currentCptAttribute.attributeSetup.basic.setup.attribute.active.scaleOpen = false
				}
				if(!currentCptAttribute.attributeSetup.basic.setup.attribute.active.hasOwnProperty("scale")){
					currentCptAttribute.attributeSetup.basic.setup.attribute.active.scale = 0
				}
				if(!currentCptAttribute.attributeSetup.basic.setup.attribute.active.hasOwnProperty("rotateOpen")){
					currentCptAttribute.attributeSetup.basic.setup.attribute.active.rotateOpen = false
				}
				if(!currentCptAttribute.attributeSetup.basic.setup.attribute.active.hasOwnProperty("rotate")){
					currentCptAttribute.attributeSetup.basic.setup.attribute.active.rotate = 0
				}
				if(!currentCptAttribute.attributeSetup.basic.setup.attribute.active.hasOwnProperty("fontSizeOpen")){
					currentCptAttribute.attributeSetup.basic.setup.attribute.active.fontSizeOpen = false
				}
				if(!currentCptAttribute.attributeSetup.basic.setup.attribute.active.hasOwnProperty("fontSize")){
					currentCptAttribute.attributeSetup.basic.setup.attribute.active.fontSize = 12
				}
				if(!currentCptAttribute.attributeSetup.basic.setup.attribute.active.hasOwnProperty("borderOpen")){   
					currentCptAttribute.attributeSetup.basic.setup.attribute.active.borderOpen = false
				}
				if(!currentCptAttribute.attributeSetup.basic.setup.attribute.active.hasOwnProperty("borderSize")){
					currentCptAttribute.attributeSetup.basic.setup.attribute.active.borderSize = 1
				}
				if(!currentCptAttribute.attributeSetup.basic.setup.attribute.active.hasOwnProperty("borderStyle")){
					currentCptAttribute.attributeSetup.basic.setup.attribute.active.borderStyle = 'solid'
				}
				if(!currentCptAttribute.attributeSetup.basic.setup.attribute.active.hasOwnProperty("borderColor")){
					currentCptAttribute.attributeSetup.basic.setup.attribute.active.borderColor = '#e8e8e8'
				}
				if(!currentCptAttribute.attributeSetup.basic.setup.attribute.active.hasOwnProperty("radiusOpen")){
					currentCptAttribute.attributeSetup.basic.setup.attribute.active.radiusOpen = false
				}
				if(!currentCptAttribute.attributeSetup.basic.setup.attribute.active.hasOwnProperty("radius")){
					currentCptAttribute.attributeSetup.basic.setup.attribute.active.radius = 5
				}
				if(!currentCptAttribute.attributeSetup.basic.setup.attribute.active.hasOwnProperty("shadowOpen")){
					currentCptAttribute.attributeSetup.basic.setup.attribute.active.shadowOpen = false
				}
				if(!currentCptAttribute.attributeSetup.basic.setup.attribute.active.hasOwnProperty("shadowType")){
					currentCptAttribute.attributeSetup.basic.setup.attribute.active.shadowType = false
				}
				if(!currentCptAttribute.attributeSetup.basic.setup.attribute.active.hasOwnProperty("shadowOffsetX")){
					currentCptAttribute.attributeSetup.basic.setup.attribute.active.shadowOffsetX = 0
				}
				if(!currentCptAttribute.attributeSetup.basic.setup.attribute.active.hasOwnProperty("shadowOffsetY")){
					currentCptAttribute.attributeSetup.basic.setup.attribute.active.shadowOffsetY = 0
				}
				if(!currentCptAttribute.attributeSetup.basic.setup.attribute.active.hasOwnProperty("shadowBlur")){
					currentCptAttribute.attributeSetup.basic.setup.attribute.active.shadowBlur = 0
				}
				if(!currentCptAttribute.attributeSetup.basic.setup.attribute.active.hasOwnProperty("shadowSpread")){
					currentCptAttribute.attributeSetup.basic.setup.attribute.active.shadowSpread = 0
				}
				if(!currentCptAttribute.attributeSetup.basic.setup.attribute.active.hasOwnProperty("shadowColor")){
					currentCptAttribute.attributeSetup.basic.setup.attribute.active.shadowColor = '#bfbfbf'
				}
			}
		}
	}
	
	function openHover(v){
		if(v){
			if(currentCptAttribute.attributeSetup.basic.show){
				if(!currentCptAttribute.attributeSetup.basic.setup.attribute.hover.hasOwnProperty("backgroundColorOpen")){
					currentCptAttribute.attributeSetup.basic.setup.attribute.hover.backgroundColorOpen = false
				}
				if(!currentCptAttribute.attributeSetup.basic.setup.attribute.hover.hasOwnProperty("backgroundColor")){
					currentCptAttribute.attributeSetup.basic.setup.attribute.hover.backgroundColor = ""
				}
				if(!currentCptAttribute.attributeSetup.basic.setup.attribute.hover.hasOwnProperty("colorOpen")){
					currentCptAttribute.attributeSetup.basic.setup.attribute.hover.colorOpen = false
				}
				if(!currentCptAttribute.attributeSetup.basic.setup.attribute.hover.hasOwnProperty("color")){
					currentCptAttribute.attributeSetup.basic.setup.attribute.hover.color = ""
				}
				if(!currentCptAttribute.attributeSetup.basic.setup.attribute.hover.hasOwnProperty("scaleOpen")){
					currentCptAttribute.attributeSetup.basic.setup.attribute.hover.scaleOpen = false
				}
				if(!currentCptAttribute.attributeSetup.basic.setup.attribute.hover.hasOwnProperty("scale")){
					currentCptAttribute.attributeSetup.basic.setup.attribute.hover.scale = 0
				}
				if(!currentCptAttribute.attributeSetup.basic.setup.attribute.hover.hasOwnProperty("rotateOpen")){
					currentCptAttribute.attributeSetup.basic.setup.attribute.hover.rotateOpen = false
				}
				if(!currentCptAttribute.attributeSetup.basic.setup.attribute.hover.hasOwnProperty("rotate")){
					currentCptAttribute.attributeSetup.basic.setup.attribute.hover.rotate = 0
				}
				if(!currentCptAttribute.attributeSetup.basic.setup.attribute.hover.hasOwnProperty("fontSizeOpen")){
					currentCptAttribute.attributeSetup.basic.setup.attribute.hover.fontSizeOpen = false
				}
				if(!currentCptAttribute.attributeSetup.basic.setup.attribute.hover.hasOwnProperty("fontSize")){
					currentCptAttribute.attributeSetup.basic.setup.attribute.hover.fontSize = 12
				}
				if(!currentCptAttribute.attributeSetup.basic.setup.attribute.hover.hasOwnProperty("borderOpen")){   
					currentCptAttribute.attributeSetup.basic.setup.attribute.hover.borderOpen = false
				}
				if(!currentCptAttribute.attributeSetup.basic.setup.attribute.hover.hasOwnProperty("borderSize")){
					currentCptAttribute.attributeSetup.basic.setup.attribute.hover.borderSize = 1
				}
				if(!currentCptAttribute.attributeSetup.basic.setup.attribute.hover.hasOwnProperty("borderStyle")){
					currentCptAttribute.attributeSetup.basic.setup.attribute.hover.borderStyle = 'solid'
				}
				if(!currentCptAttribute.attributeSetup.basic.setup.attribute.hover.hasOwnProperty("borderColor")){
					currentCptAttribute.attributeSetup.basic.setup.attribute.hover.borderColor = '#e8e8e8'
				}
				if(!currentCptAttribute.attributeSetup.basic.setup.attribute.hover.hasOwnProperty("radiusOpen")){
					currentCptAttribute.attributeSetup.basic.setup.attribute.hover.radiusOpen = false
				}
				if(!currentCptAttribute.attributeSetup.basic.setup.attribute.hover.hasOwnProperty("radius")){
					currentCptAttribute.attributeSetup.basic.setup.attribute.hover.radius = 5
				}
				if(!currentCptAttribute.attributeSetup.basic.setup.attribute.hover.hasOwnProperty("shadowOpen")){
					currentCptAttribute.attributeSetup.basic.setup.attribute.hover.shadowOpen = false
				}
				if(!currentCptAttribute.attributeSetup.basic.setup.attribute.hover.hasOwnProperty("shadowType")){
					currentCptAttribute.attributeSetup.basic.setup.attribute.hover.shadowType = false
				}
				if(!currentCptAttribute.attributeSetup.basic.setup.attribute.hover.hasOwnProperty("shadowOffsetX")){
					currentCptAttribute.attributeSetup.basic.setup.attribute.hover.shadowOffsetX = 0
				}
				if(!currentCptAttribute.attributeSetup.basic.setup.attribute.hover.hasOwnProperty("shadowOffsetY")){
					currentCptAttribute.attributeSetup.basic.setup.attribute.hover.shadowOffsetY = 0
				}
				if(!currentCptAttribute.attributeSetup.basic.setup.attribute.hover.hasOwnProperty("shadowBlur")){
					currentCptAttribute.attributeSetup.basic.setup.attribute.hover.shadowBlur = 0
				}
				if(!currentCptAttribute.attributeSetup.basic.setup.attribute.hover.hasOwnProperty("shadowSpread")){
					currentCptAttribute.attributeSetup.basic.setup.attribute.hover.shadowSpread = 0
				}
				if(!currentCptAttribute.attributeSetup.basic.setup.attribute.hover.hasOwnProperty("shadowColor")){
					currentCptAttribute.attributeSetup.basic.setup.attribute.hover.shadowColor = '#bfbfbf'
				}
			}
		}
	}
	
	function onDelChooseItem(index){
		currentCptAttribute.attributeSetup.choose.setup.dataList.splice(index,1)
	}
	
	function onAddChooseItem(){
		currentCptAttribute.attributeSetup.choose.setup.dataList.push({
			label:"",
			value:""
		})
	}
	
	
	function initAttributes(){
		currentCptAttribute.currentId = '-100'
		currentCptAttribute.currentName = 'more'
		currentCptAttribute.currentType = "none"
		
		Object.keys(currentCptAttribute.attributeSetup).forEach(key => {
			currentCptAttribute.attributeSetup[key].show = false
			currentCptAttribute.attributeSetup[key].setup = {}
		});

		currentCptAttribute.showCssSetup = false
	}
	
	function chooseSingleCmpt(key,item){
		if(key=='basic'){
			if(!item.hasOwnProperty('visible')){
				item.visible = true
			}
			if(!item.hasOwnProperty('visibleExpression')){
				item.visibleExpression = ""
			}
			if(!item.hasOwnProperty('enabled')){
				item.enabled = true
			}
			if(!item.hasOwnProperty('enableExpression')){
				item.enableExpression = ""
			}
			if(!item.hasOwnProperty('checked')){
				item.checked = false
			}
			if(!item.hasOwnProperty('checkedExpression')){
				item.checkedExpression = ""
			}
			if(!item.hasOwnProperty('value')){
				item.value=""
			}
			if(!item.hasOwnProperty("valueExpression")){
				item.valueExpression=""
			}
			if(!item.hasOwnProperty("whOffsetOpen")){
				item.whOffsetOpen= false
			}
			if(!item.hasOwnProperty("offsetW")){
				item.offsetW= 0
			}
			if(!item.hasOwnProperty("offsetH")){
				item.offsetH= 0
			}
			if(!item.hasOwnProperty("flexOpen")){
				item.flexOpen= false
			}
			if(!item.hasOwnProperty("flex")){
				item.flex= 1
			}
			currentCptAttribute.attributeSetup.basic.setup = item
			currentCptAttribute.attributeSetup.basic.show = true
		}else if(key=='background'){
			if(!item.attribute.hasOwnProperty("background") || !item.attribute.background ){
				item.attribute.background = currentCptAttribute.func.initBackground()
			}
			currentCptAttribute.attributeSetup.background.setup = item.attribute.background
			currentCptAttribute.attributeSetup.background.show = true
		}else if(key=='font'){
			if(!item.attribute.hasOwnProperty("font") || !item.attribute.font){
				item.attribute.font = currentCptAttribute.func.initFont()
			}
			currentCptAttribute.attributeSetup.font.setup = item.attribute.font
			currentCptAttribute.attributeSetup.font.show = true
		}else if(key=='box'){
			if(!item.attribute.hasOwnProperty("box") || !item.attribute.box){
				item.attribute.box = currentCptAttribute.func.initBox()
				if("TText,TImage,TRadio,TCheckBox,TIcon,".includes(item.key+",")){
					item.attribute.box.borderSize = 0
				}
			}
			currentCptAttribute.attributeSetup.box.setup = item.attribute.box
			currentCptAttribute.attributeSetup.box.show = true
		}else if(key=='position'){
			if(!item.hasOwnProperty("wUnit")){
				item.wUnit = 'px'
			}
			if(!item.hasOwnProperty("hUnit")){
				item.hUnit = 'px'
			}
			if(!item.attribute.hasOwnProperty("rotation")){
				item.attribute.rotation = 0
			}
			if(!item.attribute.hasOwnProperty("fixed")){
				item.attribute.fixed = {
				    fixedOpen:false,
					fixedMode:"free", //top,bottom
				}
			}
			currentCptAttribute.attributeSetup.position.setup = item
			currentCptAttribute.attributeSetup.position.show = true
		}else if(key=='value'){
			currentCptAttribute.attributeSetup.value.setup = item
			currentCptAttribute.attributeSetup.value.show = true
		}else if(key=='placeHolder'){
			if(!item.attribute.hasOwnProperty("placeHolder") || !item.attribute.placeHolder){
				item.attribute.placeHolder = currentCptAttribute.func.initPlaceHolder()
			}
			currentCptAttribute.attributeSetup.placeHolder.setup = item.attribute.placeHolder
			currentCptAttribute.attributeSetup.placeHolder.show = true
		}else if(key=='layout'){
			if(!item.attribute.hasOwnProperty("layout") || !item.attribute.layout){
				if(item.key == 'TTab'){
					item.attribute.layout = {
						hAlign:"align-left",
						layoutDirection: "column",
						scrollHOpen: false,
						scrollVOpen: false,
						vAlign: "align-top"
					}
				}else{
					item.attribute.layout = currentCptAttribute.func.initLayout()
				}
			}
			currentCptAttribute.attributeSetup.layout.setup = item.attribute.layout
			currentCptAttribute.attributeSetup.layout.show = true
		}else if(key=='line'){
			if(!item.attribute.hasOwnProperty("line") || !item.attribute.line){
			     item.attribute.line = currentCptAttribute.func.initLine()	
			}
			currentCptAttribute.attributeSetup.line.setup = item.attribute.line
			currentCptAttribute.attributeSetup.line.show = true
		}else if(key=='css'){
			if(!item.attribute.hasOwnProperty("css") || !item.attribute.css){
			     item.attribute.css = currentCptAttribute.func.initCss()	
			}
			currentCptAttribute.attributeSetup.css.setup = item.attribute.css
			currentCptAttribute.attributeSetup.css.show = true
		}else if(key=='choose'){
			if(!item.attribute.hasOwnProperty("choose") || !item.attribute.choose){
			     item.attribute.choose = currentCptAttribute.func.initChoose(item.key)	
			}
			currentCptAttribute.attributeSetup.choose.setup = item.attribute.choose
			currentCptAttribute.attributeSetup.choose.show = true
		}else if(key=='icon'){
			if(!item.attribute.hasOwnProperty("icon") || !item.attribute.icon){
			     item.attribute.icon = currentCptAttribute.func.initIcon(item.key)	
			}
			currentCptAttribute.attributeSetup.icon.setup = item.attribute.icon
			currentCptAttribute.attributeSetup.icon.show = true
		}else if(key=='proper'){
			if(!item.attribute.hasOwnProperty("proper") || !item.attribute.proper){
			     item.attribute.proper = currentCptAttribute.func.initProper(item.key)	
			}
			currentCptAttribute.attributeSetup.proper.setup = item.attribute.proper
			currentCptAttribute.attributeSetup.proper.show = true
		}else if(key=='hover'){
			currentCptAttribute.attributeSetup.hover.show = true
		}else if(key=='active'){
			currentCptAttribute.attributeSetup.active.show = true
		}

	}
	
	function chooseAppBody(){
		if(currentCptAttribute.currentId == '10001') return
		currentCptAttribute.currentId = '10001'
		let pathArray = FileScript.filePath.split(".")
		currentCptAttribute.currentName = pathArray[pathArray.length - 2]
		currentCptAttribute.currentType = 'body'
		initAttributes()
		
		if(!FileScript.attribute.hasOwnProperty("background")){
			FileScript.attribute.background = currentCptAttribute.func.initBackground()
		}
		currentCptAttribute.attributeSetup.background.setup = FileScript.attribute.background
		currentCptAttribute.attributeSetup.background.show = true
		
		if(!FileScript.attribute.hasOwnProperty("layout")){
			FileScript.attribute.layout = currentCptAttribute.func.initLayout()
		}
		currentCptAttribute.attributeSetup.layout.setup = FileScript.attribute.layout
		currentCptAttribute.attributeSetup.layout.show = true
		
		if(!FileScript.attribute.hasOwnProperty("box")){
			FileScript.attribute.box = {
				paddingLeft:0,
				paddingTop:0,
				paddingRight:0,
				paddingBottom:0,
			}
		}
		currentCptAttribute.attributeSetup.box.setup = FileScript.attribute.box
		currentCptAttribute.attributeSetup.box.show = false
		
		
		
		console.log(FileScript.attribute)
	}
	
	// 内边距输入变化
	const handleChangePadding = (e) => {
		if (currentCptAttribute.paddingControl) {
			currentCptAttribute.attributeSetup.box.setup.paddingTop = e;
			currentCptAttribute.attributeSetup.box.setup.paddingBottom = e;
			currentCptAttribute.attributeSetup.box.setup.paddingRight = e;
			currentCptAttribute.attributeSetup.box.setup.paddingLeft = e;
		}
	};
	// 外边距输入变化
	const handleChangeMargin = (e) => {
		if (currentCptAttribute.marginControl) {
			currentCptAttribute.attributeSetup.box.setup.marginTop = e;
			currentCptAttribute.attributeSetup.box.setup.marginBottom = e;
			currentCptAttribute.attributeSetup.box.setup.marginRight = e;
			currentCptAttribute.attributeSetup.box.setup.marginLeft = e;
		}
	};
	// 圆角输入变化
	const handleRadiusChange = (e) => {
		if (currentCptAttribute.radiusControl) {
			currentCptAttribute.attributeSetup.box.setup.radiusLeftTop = e;
			currentCptAttribute.attributeSetup.box.setup.radiusRightTop = e;
			currentCptAttribute.attributeSetup.box.setup.radiusLeftBottom = e;
			currentCptAttribute.attributeSetup.box.setup.radiusRightBottom = e;
		}
	};
	// 封装通用函数，处理宽度和高度的变化
	const handleDimensionChange = (type, value) => {
		if(currentCptAttribute.layoutDisabled){
			let rate = currentCptAttribute.attributeSetup.position.setup.h > 0 ? parseFloat(currentCptAttribute.attributeSetup.position.setup.w)/parseFloat(currentCptAttribute.attributeSetup.position.setup.h) : 1
			if(rate > 0){
				if(type=='height' ){
					currentCptAttribute.attributeSetup.position.setup.w =  parseInt(rate * parseFloat(value))
				}else if(type=='width'){
					currentCptAttribute.attributeSetup.position.setup.h =  parseInt(parseFloat(value) / rate)
				}
			}
			
		}
	}
	
	function checkParentFreeLayout(){
		if(currentCptAttribute.attributeSetup.position.show){
			let parent = currentCptAttribute.attributeSetup.position.setup.parent
			if(parent && parent.id=='10001'){
				 return FileScript.attribute.layout.layoutDirection == 'absolute'
			}else if(parent){
				if(parent.attribute.hasOwnProperty("layout")){
				   return parent.attribute.layout.layoutDirection == 'absolute'
				 }else{
					 return false
				 }
			}
		}
		return true
	}
	
	
	return {chooseSingleCmpt,chooseAppBody,initAttributes,onLineChange,openCssSetup,onRefreshCssSetup,
	        openActive,openHover,onDelChooseItem,onAddChooseItem,handleChangePadding,handleChangeMargin,
			handleRadiusChange,handleDimensionChange,checkParentFreeLayout,addPageItems,deletePageItems}
}