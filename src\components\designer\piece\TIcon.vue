<template>
	<div class="content basicStyle" :style="{cursor:props.TBase.getCursorType()}">
		<el-icon class="iconStyle" :style="getIconStyle()">
			<component :is="getIconPath()" />
		</el-icon>
	</div>
</template>

<script lang="ts" setup>
	import { reactive,ref ,onMounted,inject,watch} from 'vue';
	const ProjectRef = inject("ProjectRef")
	
	const props = defineProps({
		TItem:{
			type:Object,
			default:{}
		},
		TParent:{
			type:Object,
			default:{id:'10001'}
		},
		TBase:{
			type:Object,
			default:{
				getStyle:()=>{}
			}
		}
	})
	
	function getIconPath(){
		return props.TItem.attribute.icon.iconPath
	}
	
	onMounted(()=>{
		props.TItem.parent = props.TParent
	})
	
	function getIconStyle(){
		let nStyle = {}
		if(!props.TBase.checkEnabled()){
			nStyle.color = '#cecece'
		}else{
			let nFontColor = props.TBase.checkEffect("color")
			if(nFontColor.bothUsed){
				let nBasic = props.TItem.attribute.icon.iconColor
				nStyle['--basic-color'] = nBasic 
				nStyle['--active-color'] = nFontColor.activeUsed ? nFontColor.activeValue : nBasic 
				nStyle['--hover-color'] = nFontColor.hoverUsed ? nFontColor.hoverValue : nBasic
			}else{
				nStyle.color = props.TItem.attribute.icon.iconColor
			}
		}
		
		let nFontSize = props.TBase.checkEffect("fontSize")
		if(nFontSize.bothUsed){
			let nBasic = props.TItem.attribute.icon.iconSize 
			nStyle['--basic-fontSize'] = nBasic  + "px"
			nStyle['--active-fontSize'] = (nFontSize.activeUsed ? nFontSize.activeValue : nBasic )  + "px"
			nStyle['--hover-fontSize'] = (nFontSize.hoverUsed ? nFontSize.hoverValue : nBasic)  + "px"
		}else{
			nStyle.fontSize = props.TItem.attribute.icon.iconSize + "px"
		}
		
		return nStyle
	}
	
</script>

<style lang="scss" scoped>
	@import '../../../common/effect.css';
	
	.content {
		width:calc(100% - 2px);
		height:calc(100% - 2px);
		background-color: transparent;
		overflow: hidden;
		display: flex;
		justify-content: center;
		align-items: center;
	
		.img-box{
			width:100%;
			height:100%;
		}
	}
</style>