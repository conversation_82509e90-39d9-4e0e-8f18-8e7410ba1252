<template>
  <div class="flex_box filter_head">
    <el-form :model="queryData" :inline="true">
      <el-form-item label="所属">
        <el-select
          v-model="queryData.isSelf"
          placeholder="请选择"
          style="width: 111px"
          @change="handleGetList"
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <el-button type="primary" class="create_btn" @click="dialogVisible = true">
      创建数据源
      <template #icon>
        <img
          src="@/assets/images/list_icon_add.png"
          style="width: 18px; height: 18px"
        />
      </template>
    </el-button>
  </div>
  <el-table :data="listData" :show-header="false">
    <el-table-column width="112px">
      <template #default="{ row }">
        <img
          style="width: 88px; height: 88px; border-radius: 8px"
          :src="row.type === 0 ? mysqlImg : mongoDbImg"
        />
      </template>
    </el-table-column>
    <el-table-column>
      <template #default="scope">
        <div style="font-weight: 600; font-size: 18px; color: #212121">
          {{ scope.row.fullname }}
        </div>
        <div style="margin-top: 16px; font-size: 16px; color: #666666">
          <span>
            {{ scope.row.code === 1 ? "[正式库]" : "[平台库]" }}
          </span>
        </div>
      </template>
    </el-table-column>
    <el-table-column>
      <template #default="scope">
        <div
          style="
            display: flex;
            align-items: center;
            font-size: 16px;
            color: #999999;
          "
        >
          <span> 数据库：{{ scope.row.name }} </span>
          <div
            :class="
              scope.row.isSelf === 0 ? 'tag_primary' : 'tag_primary tag_success'
            "
            style="margin-left: 8px"
          >
            {{ scope.row.isSelf === 0 ? "直属" : "参与" }}
          </div>
        </div>
        <div style="font-size: 16px; color: #999999">
          类型：{{ typeOptions[scope.row.type] }}
        </div>
      </template>
    </el-table-column>
    <el-table-column width="300px">
      <template #default="scope">
        <el-button type="primary" text @click="handleToDetail(scope.row)">
          打开
          <template #icon>
            <img class="btn_icon" src="@/assets/images/list_icon_open.png" />
          </template>
        </el-button>
        <el-button type="primary" text @click="handleUpdate(scope.row)">
          设置
          <template #icon>
            <img class="btn_icon" src="@/assets/images/list_icon_set.png" />
          </template>
        </el-button>
        <el-button type="danger" text @click="handleDelete(scope.row)">
          删除
          <template #icon>
            <img class="btn_icon" src="@/assets/images/list_icon_del.png" />
          </template>
        </el-button>
      </template>
    </el-table-column>
  </el-table>
  <div style="display: flex; justify-content: flex-end">
    <el-pagination
      v-model:current-page="queryData.page"
      :page-size="queryData.pageSize"
      layout="total, prev, pager, next"
      :total="total"
      @current-change="handleGetList"
    >
      <template #total="{ total }"> 共 {{ total }} 条数据 </template>
    </el-pagination>
  </div>
  <!-- 创建数据源 -->
  <el-dialog
    v-model="dialogVisible"
    destroy-on-close
    width="500"
    @close="handleResetForm"
  >
    <template #header>
      <div style="text-align: center; font-weight: 600; font-size: 18px">
        {{ formData.id ? "编辑数据源" : "新建数据源" }}
      </div>
    </template>
    <el-form
      ref="dialogFormRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
      status-icon
      label-position="left"
    >
      <el-form-item label="数据库类型" prop="type">
        <el-select
          :disabled="formData.id !== 0"
          v-model="formData.type"
          placeholder="请选择"
        >
          <el-option
            v-for="(item, index) in typeOptions"
            :key="index"
            :label="item"
            :value="index"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="数据源类型" prop="code">
        <el-select
          :disabled="formData.id !== 0"
          v-model="formData.code"
          placeholder="请选择"
        >
          <el-option label="平台库" :value="0"></el-option>
          <el-option label="正式库" :value="1"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="数据库中文名" prop="fullname">
        <el-input
          v-model="formData.fullname"
          placeholder="请输入名称,此为当前数据源名称"
        />
      </el-form-item>
      <el-form-item label="数据库英文名" prop="name">
        <el-input
          :disabled="formData.id !== 0"
          v-model="formData.name"
          placeholder="请输入内容"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div style="display: flex; justify-content: center">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" v-debounce="handleSubmit" style="color: #fff">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { onMounted, reactive, ref } from "vue";
import { useRouter } from "vue-router";
import {
  databaseList,
  removeDatabase,
  createDatabase,
  updateDatabase,
} from "@/apis/homeApi";
import mysqlImg from "@/assets/images/mysql.jpg";
import mongoDbImg from "@/assets/images/mongodb.jpg";

const router = useRouter();

const options = ref([
  {
    label: "全部",
    value: "-1",
  },
  {
    label: "直属",
    value: "0",
  },
  {
    label: "参与",
    value: "1",
  },
]);
const typeOptions = ["MySQL", "MongoDB"];
const queryData = reactive({
  isSelf: "-1",
  page: 1,
  pageSize: 10,
});
const listData = ref([]);
const total = ref(0);
const dialogVisible = ref(false);
const dialogFormRef = ref();
const rules = reactive({
  type: [{ required: true, message: "请选择数据库类型", trigger: "change" }],
  code: [{ required: true, message: "请选择数据源类型", trigger: "change" }],
  name: [
    { required: true, message: "请输入", trigger: "change" },
    {
      pattern: /^[a-z]([a-z0-9_]*[a-z0-9])?$/,
      message:
        "只能包含小写字母、数字和下划线，且首位不能是数字或下划线，末尾不能是下划线",
      trigger: "change",
    },
  ],
  fullname: [{ required: true, message: "请输入", trigger: "change" }],
});
const formData = ref({
  id: 0,
  type: "",
  code: "",
  name: "",
  fullname: "",
});

const handleResetForm = () => {
  formData.value = {
    id: 0,
    type: "",
    code: "",
    name: "",
    fullname: "",
  };
};

const handleUpdate = (record) => {
  formData.value = { ...record };
  dialogVisible.value = true;
};

const handleSubmit = () => {
  dialogFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (formData.value.id) {
          await updateDatabase({
            id: formData.value.id,
            fullname: formData.value.fullname,
          });
        } else {
          await createDatabase({
            type: formData.value.type,
            code: formData.value.code,
            fullname: formData.value.fullname,
            name: formData.value.name,
          });
        }
        ElMessage({
          type: "success",
          message: formData.value.id ? "编辑成功" : "创建成功",
        });
        handleGetList();
        dialogVisible.value = false;
      } catch (error) {
        console.log(error);
      }
    }
  });
};

const handleGetList = async () => {
  try {
    const res = await databaseList(queryData);
    listData.value = res.data.items;
    total.value = res.data.meta.total;
  } catch (error) {}
};

const handleToDetail = (e) => {
  router.push(`/home/<USER>
};

const handleDelete = (record) => {
  ElMessageBox.confirm(
    `数据源删除后不可恢复，请谨慎～～确定删除吗？`,
    `删除-${record.fullname}`,
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      center: true,
    }
  ).then(async () => {
    try {
      await removeDatabase(record.id);
      ElMessage({
        type: "success",
        message: "删除成功",
      });
      handleGetList();
    } catch {}
  });
};

onMounted(() => {
  handleGetList();
});
</script>

<style lang="scss" scoped>
.el-button {
  font-size: 16px;
}
.el-button--primary {
  color: #2b6bff;
}
.el-button--danger {
  color: #ff4444;
}
.filter_head {
  padding: 0 24px;
  height: 64px;
  background: #ffffff;
  box-shadow: inset 0px -1px 0px 0px #ededed;
  .el-form-item {
    margin-bottom: 0;
  }
  .create_btn {
    font-weight: 600;
    font-size: 14px;
    color: #ffffff;
    background: #2b6bff;
  }
}
.tag_primary {
  width: 36px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  background: rgba(43, 107, 255, 0.1);
  border-radius: 3px;
  font-size: 14px;
  color: #2b6bff;
}
.tag_success {
  background: rgba(44, 219, 102, 0.1);
  color: #2cdb66;
}
.btn_icon {
  width: 20px;
  height: 20px;
}
</style>
