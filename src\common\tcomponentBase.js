
import { reactive,ref,inject} from "vue"

export function useTComponentBase(pItem){
	
	const appScreen = inject("appScreen")
	
	let xItem = pItem
	function setXItem(mItem){
		xItem = mItem
	}
	
	const ProjectRef = inject("ProjectRef")
	const FileScript = inject("FileScript")
	const pjResouceMap = inject("pjResouceMap")
	
	
	//Basic
	function getFontColor(){
		if(!checkEnabled()) return '#cecece'
		if(xItem.hasOwnProperty('attribute') && xItem.attribute.hasOwnProperty('font')){
			if(xItem.attribute.font.fontColorTheme && xItem.attribute.font.fontColorTheme==true){
				let themeColor = ProjectRef.funcObj.getValueByThemeKey(xItem.attribute.font.fontColorThemeKey)
				if(themeColor) return themeColor
			}
			return xItem.attribute.font.fontColor
		}else{
			return ProjectRef.BasicData.theme.currentTheme.color
		}
	}
	
	function getBorder(){
		if(checkEnter.value==1 && (xItem.model=='TBox' || xItem.model=='TList')){
			return '1px dashed '+ ProjectRef.BasicData.theme.currentTheme.color
		}
		if(xItem.hasOwnProperty('attribute') && xItem.attribute.hasOwnProperty('box')){
			if(xItem.attribute.box.borderTheme && xItem.attribute.box.borderTheme==true){
				let borderTheme = ProjectRef.funcObj.getValueByThemeKey(xItem.attribute.box.borderThemeKey)
				if(borderTheme) return borderTheme
			}
			return xItem.attribute.box.borderSize+"px "+xItem.attribute.box.borderStyle+" " + (checkEnabled() ? xItem.attribute.box.borderColor : '#cecece')
		}else{
			return '1px solid '+ (checkEnabled() ? ProjectRef.BasicData.theme.currentTheme.color : '#cecece')
		}
	}
	
	function getBackgroundImageUrl(){
		if(xItem.hasOwnProperty('attribute') && xItem.attribute.hasOwnProperty('background')
		    && xItem.attribute.background.backgroundImageOpen==true
		    && xItem.attribute.background.backgroundImageUrl.length > 0){
				if(xItem.attribute.background.backgroundImageUrl.split('.')[0] =='resource'){
					return pjResouceMap.getResourceUrl(xItem.attribute.background.backgroundImageUrl)
				}else{
					return xItem.attribute.background.backgroundImageUrl
				}
				console.log("bkimage",xItem.attribute.background.backgroundImageUrl)
		}
		return ""
	}
	
	function getBackgroundColor(){
		if(xItem.hasOwnProperty('attribute') && xItem.attribute.hasOwnProperty('background')){
			if(xItem.attribute.background.theme && xItem.attribute.background.theme==true){
				let themeColor = ProjectRef.funcObj.getValueByThemeKey(xItem.attribute.background.themeKey)
				if(themeColor) return themeColor
			}
			return xItem.attribute.background.backgroundColor ? xItem.attribute.background.backgroundColor : 'transparent'
		}else{
			return '#fff'
		}
	}
	
	function getRadius(){
		if(xItem.hasOwnProperty('attribute') && xItem.attribute.hasOwnProperty('box')){
			return xItem.attribute.box.radiusLeftTop + 'px ' + xItem.attribute.box.radiusRightTop+"px " +
			       xItem.attribute.box.radiusRightBottom + 'px ' + xItem.attribute.box.radiusLeftBottom+"px "
		}else{
			return '0px'
		}
	}
	
	function getPadding(){
		if(xItem.hasOwnProperty('attribute') && xItem.attribute.hasOwnProperty('box')){
			return xItem.attribute.box.paddingTop + 'px ' + xItem.attribute.box.paddingRight+"px " +
			       xItem.attribute.box.paddingBottom + 'px ' + xItem.attribute.box.paddingLeft+"px "
		}else{
			return '0px'
		}
	}
	
	function getMargin(){
		if(xItem.hasOwnProperty('attribute') && xItem.attribute.hasOwnProperty('box')){
			return xItem.attribute.box.marginTop + 'px ' + xItem.attribute.box.marginRight+"px " +
			       xItem.attribute.box.marginBottom + 'px ' + xItem.attribute.box.marginLeft+"px "
		}else{
			return '0px'
		}
	}
	
	function getFontSize(){
		if(xItem.hasOwnProperty('attribute') && xItem.attribute.hasOwnProperty('font')
		    && xItem.attribute.font.hasOwnProperty('fontSize')){
				if(xItem.attribute.font.fontSizeTheme && xItem.attribute.font.fontSizeTheme==true){
					let themeFontSize = ProjectRef.funcObj.getValueByThemeKey(xItem.attribute.font.fontSizeThemeKey)
					if(themeFontSize) return themeFontSize+'px'
				}
				return xItem.attribute.font.fontSize +"px"
		}else{
			return "13px"
		}
	}
	
	function getFontFamily(){
		if(xItem.hasOwnProperty('attribute') && xItem.attribute.hasOwnProperty('font')
		    && xItem.attribute.font.hasOwnProperty('fontFamily')){
				if(xItem.attribute.font.fontFamilyTheme && xItem.attribute.font.fontFamilyTheme==true){
					let themeFamily = ProjectRef.funcObj.getValueByThemeKey(xItem.attribute.font.fontFamilyThemeKey)
					// console.log("themeFamily",themeFamily)
					if(themeFamily) return themeFamily
				}
				return xItem.attribute.font.fontFamily
		}else{
			return "Microsoft YaHei"
		}
	}
	
	function getFontWeight(){
		if(xItem.hasOwnProperty('attribute') && xItem.attribute.hasOwnProperty('font')
		    && xItem.attribute.font.hasOwnProperty('fontWeight')){
				return xItem.attribute.font.fontWeight
		}else{
			return 300
		}
	}
	
	function getTextDecoration(){
		if(xItem.hasOwnProperty('attribute') && xItem.attribute.hasOwnProperty('font')
		    && xItem.attribute.font.hasOwnProperty('textDecoration')){
				return xItem.attribute.font.textDecoration
		}else{
			return 'none'
		}
	}
	
	function getCursorType(){
		if(xItem.hasOwnProperty('attribute') && xItem.attribute.hasOwnProperty('box')
		    && xItem.attribute.box.hasOwnProperty('cursorType')){
				return xItem.attribute.box.cursorType
		}else{
			return "default"
		}
	}
	
	function getTextAlign(){
		if(xItem.hasOwnProperty('attribute') && xItem.attribute.hasOwnProperty('font')
		    && xItem.attribute.font.hasOwnProperty('textAlign')){
				return xItem.attribute.font.textAlign
		}else{
			return 'center'
		}
	}
	
	function getOpacity(){
		if(xItem.hasOwnProperty('attribute') && xItem.attribute.hasOwnProperty('box')
		    && xItem.attribute.box.hasOwnProperty('opacity')){
				return  xItem.attribute.box.opacity/100.0
		}else{
			return 1
		}
	}

	
	function getShadow(){
		if(xItem.hasOwnProperty('attribute') && xItem.attribute.hasOwnProperty('box') && 
		 xItem.attribute.box.hasOwnProperty("shadowType") && xItem.attribute.box.shadowType == 'outset'){
			return xItem.attribute.box.shadowOffsetX +"px "+ xItem.attribute.box.shadowOffsetY+"px " + 
			       xItem.attribute.box.shadowBlur +"px "+ xItem.attribute.box.shadowSpread+"px " + xItem.attribute.box.shadowColor
		} else if(xItem.hasOwnProperty('attribute') && xItem.attribute.hasOwnProperty('box') &&
		      xItem.attribute.box.hasOwnProperty("shadowType") && xItem.attribute.box.shadowType == 'inset'){
			return "inset "+xItem.attribute.box.shadowOffsetX +"px "+ xItem.attribute.box.shadowOffsetY+"px " +
			       xItem.attribute.box.shadowBlur +"px "+ xItem.attribute.box.shadowSpread+"px " + xItem.attribute.box.shadowColor
		}else{
			return "0px 0px 0px 0px #fff"
		}
	}
	
	function mergeObjs(src,tar){
		if(tar){
			Object.keys(tar).forEach(key=>{
				if(!src.hasOwnProperty(key)){
					src[key] = tar[key]
				}
			})
		}
		return src
	}
	
	function cutIntValue(s){
		if(s.trim().slice(-2)=='px'){
			return parseInt(s.trim().slice(0,-2))
		}else if(s.trim().slice(-1)=='%'){
			return parseInt(s.trim().slice(0,-1))
		}
		return s
	}
	
	function isNumeric(value){
		return /^[+-]?(?:\d+(\.\d*)?|\.\d+)([eE][+-]?\d+)?$/.test(value);
	}
	
	xItem.cssFunc = ()=>{
		let nStyle = getBoxStyle()
		if(xItem.key=='TBox' || xItem.key=='TList' || xItem.key=='TListPC'){
			let nLayout = getLayoutStyle()
			nStyle = mergeObjs(nStyle,nLayout)
		}
		if(xItem.attribute.hasOwnProperty("css") && xItem.attribute.css.hasOwnProperty("otherCss")){
			Object.keys(xItem.attribute.css.otherCss).forEach(key=>{
				nStyle[key] = xItem.attribute.css.otherCss[key]
			})
		}
		
		return nStyle
	}
	
	function syncBorderCss(src,pos){
		let borderArray = src.trim().split(" ")
		if(borderArray && borderArray.length > 0){
			let n = 0
			for(let m=0;m<borderArray.length;m++){
				if(borderArray[m].trim().length==0) continue
				if(n==0 && borderArray[m].trim().slice(-2)=='px'){
					xItem.attribute.box.borderSize = cutIntValue(borderArray[m])
					n++
					continue
				}
				if(n==1 && (borderArray[m].trim()=="solid" || borderArray[m].trim()=="dashed" || borderArray[m].trim()=="dotted")){
					xItem.attribute.box.borderStyle = borderArray[m].trim()
					n++
					continue
				}
				if(n==2 && borderArray[m].trim() != ""){
					xItem.attribute.box.borderColor = borderArray[m].trim()
					n++
					continue
				}
			}
			if(n==3){
				if(pos=='all' || pos=="left") xItem.attribute.box.borderLeft = true
				if(pos=='all' || pos=="top") xItem.attribute.box.borderTop = true
				if(pos=='all' || pos=="right") xItem.attribute.box.borderRight = true
				if(pos=='all' || pos=="bottom") xItem.attribute.box.borderBottom = true
				xItem.attribute.box.borderTheme = true
			}
		}
	}

	xItem.cssSyncFunc = (cssStr)=>{
		if(cssStr && cssStr.length > 0){
			let cssArray = cssStr.replaceAll("\n","").replaceAll("\t","").split(";")
			if(cssArray && cssArray.length > 0){
				let otherCss = {}
				for(let i=0;i<cssArray.length;i++){
					if(cssArray[i].trim()=="") continue
					let attrArray = cssArray[i].trim().split(":")
					if(attrArray && attrArray.length==2){
						if(attrArray[0]=="backgroundColor" || attrArray[0]=="--basic-backgroundColor" || attrArray[0]=="background-color"){
							xItem.attribute.background.backgroundColor = attrArray[1]
							xItem.attribute.background.backgroundImageOpen = false
							xItem.attribute.background.theme = false
						}else if(attrArray[0]=='color'){
							if(!xItem.attribute.font) continue
							xItem.attribute.font.fontColor = attrArray[1]
						}else if(attrArray[0]=='left'){
							xItem.x = cutIntValue(attrArray[1])
						}else if(attrArray[0]=='top'){
							xItem.y = cutIntValue(attrArray[1])
						}else if(attrArray[0]=='width'){
							xItem.w = cutIntValue(attrArray[1])
							if(attrArray[1].trim().slice(-1)=='%'){
								xItem.wUnit = '%'
							}else if(attrArray[1].trim().slice(-2)=='px'){
								xItem.wUnit = 'px'
							}
						}else if(attrArray[0]=='height'){
							xItem.h = cutIntValue(attrArray[1])
							if(attrArray[1].trim().slice(-1)=='%'){
								xItem.hUnit = '%'
							}else if(attrArray[1].trim().slice(-2)=='px'){
								xItem.hUnit = 'px'
							}
						}else if(attrArray[0]=='border'){
							if(!xItem.attribute.box) continue
							syncBorderCss(attrArray[1],'all')
						}else if(attrArray[0]=='borderTop'){
							if(!xItem.attribute.box) continue
							syncBorderCss(attrArray[1],'top')
						}else if(attrArray[0]=='borderRight'){
							if(!xItem.attribute.box) continue
							syncBorderCss(attrArray[1],'right')
						}else if(attrArray[0]=='borderBottom'){
							if(!xItem.attribute.box) continue
							syncBorderCss(attrArray[1],'bottom')
						}else if(attrArray[0]=='borderLeft'){
							if(!xItem.attribute.box) continue
							syncBorderCss(attrArray[1],'left')
						}else if(attrArray[0]=='opacity' ){
							if(!xItem.attribute.box) continue
							if(isNumeric(attrArray[1].trim())){
								let v = parseInt(attrArray[1].trim())
								if(v >= 1 ){
									xItem.attribute.box.opacity = 100
								}else if(v > 0 && v < 1){
									xItem.attribute.box.opacity = v*100
								}
							}
						}else if(attrArray[0]=='borderRadius' || attrArray[0]=='border-radius'){
							if(!xItem.attribute.box) continue
							let radVal = attrArray[1].trim().split(" ")
							xItem.attribute.box.radiusLeftTop = cutIntValue(radVal[0])
							xItem.attribute.box.radiusRightTop = radVal && radVal.length > 1 ? cutIntValue(radVal[1]) : cutIntValue(radVal[0])
							xItem.attribute.box.radiusRightBottom = radVal && radVal.length > 1 ? cutIntValue(radVal[2]) : cutIntValue(radVal[0])
							xItem.attribute.box.radiusLeftBottom = radVal && radVal.length > 1 ? cutIntValue(radVal[3]) : cutIntValue(radVal[0])
							xItem.attribute.box.radiusTheme = false
						}else if(attrArray[0]=='margin'){
							if(!xItem.attribute.box) continue
							let radVal = attrArray[1].trim().split(" ")
							xItem.attribute.box.marginTop = cutIntValue(radVal[0])
							xItem.attribute.box.marginRight = radVal && radVal.length > 1 ? cutIntValue(radVal[1]) : cutIntValue(radVal[0])
							xItem.attribute.box.marginBottom = radVal && radVal.length > 1 ? cutIntValue(radVal[2]) : cutIntValue(radVal[0])
							xItem.attribute.box.marginLeft = radVal && radVal.length > 1 ? cutIntValue(radVal[3]) : cutIntValue(radVal[0])
						}else if(attrArray[0]=='padding'){
							if(!xItem.attribute.box) continue
							let radVal = attrArray[1].trim().split(" ")
							xItem.attribute.box.paddingTop = cutIntValue(radVal[0])
							xItem.attribute.box.paddingRight = radVal && radVal.length > 1 ? cutIntValue(radVal[1]) : cutIntValue(radVal[0])
							xItem.attribute.box.paddingBottom = radVal && radVal.length > 1 ? cutIntValue(radVal[2]) : cutIntValue(radVal[0])
							xItem.attribute.box.paddingLeft = radVal && radVal.length > 1 ? cutIntValue(radVal[3]) : cutIntValue(radVal[0])
						}else if(attrArray[0]=='fontSize' || attrArray[0]=='font-size'){
							if(!xItem.attribute.font) continue
							xItem.attribute.font.fontSize = cutIntValue(attrArray[1].trim())
						}else if(attrArray[0]=='fontFamily' || attrArray[0]=='font-family'){
							if(!xItem.attribute.font) continue
							xItem.attribute.font.fontFamily = attrArray[1].trim()
							xItem.attribute.font.fontFamilyTheme = false
						}else if(attrArray[0]=='fontWeight' || attrArray[0]=='font-weight'){
							if(!xItem.attribute.font) continue
							xItem.attribute.font.fontWeight = attrArray[1].trim()
						}else if(attrArray[0]=='textAlign' || attrArray[0]=='text-align'){
							if(!xItem.attribute.font) continue
							xItem.attribute.font.textAlign = attrArray[1].trim()
						}else if(attrArray[0]=='textDecoration' || attrArray[0]=='text-decoration'){
							if(!xItem.attribute.font) continue
							xItem.attribute.font.textDecoration = attrArray[1].trim()
						}else if( attrArray[0]=='cursor'){
							if(!xItem.attribute.box) continue
							xItem.attribute.box.cursorType = attrArray[1].trim()
						}else if(attrArray[0]=='transform' || attrArray[0]=='--basic-transform'){
							if(attrArray[1].trim().substring(0,5)=='scale'){
								let b = attrArray[1].trim().indexOf("(")
								let e = attrArray[1].trim().indexOf(")")
								if(b > 0 && b < e ){
									let vm = attrArray[1].trim().slice(b+1,e)
									if(isNumeric(vm) && xItem.attribute.box) xItem.attribute.box.scale = parseFloat(vm)*100
								}
							}else if(attrArray[1].trim().substring(0,6)=='rotate'){
								let b = attrArray[1].trim().indexOf("(")
								let e = attrArray[1].trim().indexOf(")")
								if(b > 0 && b < e ){
									let vm = attrArray[1].trim().slice(b+1,e)
									if(vm.slice(-3)=='deg' && isNumeric(vm.slice(0,-3)) && xItem.attribute){
										xItem.attribute.rotation = parseInt(vm.slice(0,-3))
									}
								}
							}
						}else if(attrArray[0]=='boxShadow' || attrArray[0]=='box-shadow'){
							if(!xItem.attribute.box) continue
							let radVal = attrArray[1].trim().split(" ")
							if(radVal.length == 5){
								xItem.attribute.box.shadowType = 'outset'
								xItem.attribute.box.shadowOffsetX = cutIntValue(radVal[0])
								xItem.attribute.box.shadowOffsetY = cutIntValue(radVal[1])
								xItem.attribute.box.shadowBlur= cutIntValue(radVal[2])
								xItem.attribute.box.shadowSpread= cutIntValue(radVal[3])
								xItem.attribute.box.shadowColor= cutIntValue(radVal[4])
							}else if(radVal.length == 6){
								xItem.attribute.box.shadowType = 'inset'
								xItem.attribute.box.shadowOffsetX = cutIntValue(radVal[1])
								xItem.attribute.box.shadowOffsetY = cutIntValue(radVal[2])
								xItem.attribute.box.shadowBlur= cutIntValue(radVal[3])
								xItem.attribute.box.shadowSpread= cutIntValue(radVal[4])
								xItem.attribute.box.shadowColor= cutIntValue(radVal[5])
							}
						}else if(attrArray[0]=='zIndex' || attrArray[0]=='z-index'){
							xItem.zindex = parseInt(attrArray[1].trim())
						}else if(attrArray[0]=='overflowX' || attrArray[0]=='overflow-x'){
							if(!xItem.attribute.layout) continue
							xItem.attribute.layout.scrollHOpen = attrArray[1].trim()=='auto'
						}else if(attrArray[0]=='overflowY' || attrArray[0]=='overflow-y'){
							if(!xItem.attribute.layout) continue
							xItem.attribute.layout.scrollVOpen = attrArray[1].trim()=='auto'
						}else if(attrArray[0]=='position'){
							if(!xItem.attribute.layout) continue
							if(attrArray[1].trim()=='absolute'){
								xItem.attribute.layout.layoutDirection = 'absolute'
								xItem.attribute.fixed.fixedOpen = false
								xItem.attribute.fixed.fixedMode = "free"
							}else if(attrArray[1].trim() == 'fixed'){
								xItem.attribute.fixed.fixedOpen = true
								xItem.attribute.fixed.fixedMode = "free"
								if(xItem.y <= 3) xItem.attribute.fixed.fixedMode="top"
							}
						}else if(attrArray[0].trim()=='display'){
						}else if(attrArray[0].trim()=='justifyContent' || attrArray[0].trim()=='justify-content'){
							if(!xItem.attribute.layout) continue
							if(attrArray[1].trim()=='center'){
								xItem.attribute.layout.vAlign = 'align-center'
							}else if(attrArray[1].trim()=='flex-start'){
								xItem.attribute.layout.vAlign = 'align-left'
							}else if(attrArray[1].trim()=='flex-end'){
								xItem.attribute.layout.vAlign = 'align-right'
							}
						}else if(attrArray[0].trim()=='alignItems' || attrArray[0].trim()=='align-items'){
							if(!xItem.attribute.layout) continue
							if(attrArray[1].trim()=='center'){
								xItem.attribute.layout.hAlign = 'align-center'
							}else if(attrArray[1].trim()=='flex-start'){
								xItem.attribute.layout.hAlign = 'align-top'
							}else if(attrArray[1].trim()=='flex-end'){
								xItem.attribute.layout.hAlign = 'align-bottom'
							}
						}else if(attrArray[0].trim()=='flexDirection' || attrArray[0].trim()=='flex-direction'){	
							if(!xItem.attribute.layout) continue
							if(attrArray[1].trim()=='column'){
								xItem.attribute.layout.layoutDirection = 'column'
							}else if(attrArray[1].trim()=='column'){
								xItem.attribute.layout.layoutDirection = 'row'
							}
						}else if(attrArray[0].trim().substring(0,2)=='--'){
						}else if(",background,backgroundImage,backgroundPosition,backgroundRepeat,backgroundSize,".includes(","+attrArray[0].trim()+",")){
							
						}else{
							otherCss[attrArray[0]] = attrArray[1].trim()
						}
					}
				}
				if(Object.keys(otherCss).length > 0){
					if(!xItem.attribute.hasOwnProperty("css")) xItem.attribute.css = {}
					xItem.attribute.css.otherCss = otherCss
				}else{
					if(xItem.attribute.hasOwnProperty("css") && xItem.attribute.css.hasOwnProperty("otherCss")){
						delete xItem.attribute.css.otherCss
					}
				}
				
			}
		}
	}

	function getStyle(){
		let nStyle = {}
		
		nStyle.backgroundColor = "transparent"
		nStyle.border = "0px"
		nStyle.transform = "none"
		nStyle.boxShadow = 'none'
		
		if(xItem.hasOwnProperty("attribute") && xItem.attribute.hasOwnProperty("box") && xItem.model=='TImage'){
		   if(xItem.attribute.box.paddingLeft > 0 || xItem.attribute.box.paddingRight > 0){
			 nStyle.width = 'calc(100% - '+(xItem.attribute.box.paddingLeft + xItem.attribute.box.paddingRight)+'px)'
			}
			if(xItem.attribute.box.paddingTop > 0 || xItem.attribute.box.paddingBottom > 0){
			 nStyle.height = 'calc(100% - '+(xItem.attribute.box.paddingTop + xItem.attribute.box.paddingBottom)+'px)'
			}
		}
		

		let nBorderRadius = checkEffect("borderRadius")
		if(nBorderRadius.bothUsed){
			let nBasic = getRadius()
			nStyle['--basic-borderRadius'] = nBasic 
			nStyle['--active-borderRadius'] = nBorderRadius.activeUsed ? nBorderRadius.activeValue : nBasic
			nStyle['--hover-borderRadius'] = nBorderRadius.hoverUsed ? nBorderRadius.hoverValue : nBasic
		}else{
			nStyle.borderRadius = getRadius()
		}
		
		nStyle.padding = getPadding()
		nStyle.opacity = getOpacity()
		nStyle.cursor = getCursorType()
		nStyle.textDecoration = getTextDecoration()
		nStyle.textAlign = getTextAlign()
	
		return nStyle
	}
	
	function checkParentFreeLayout(){
		if(xItem.parent && xItem.parent.id=='10001'){
			if(FileScript.hasOwnProperty("attribute") && FileScript.attribute.hasOwnProperty("layout") &&
			    FileScript.attribute.layout.layoutDirection != 'absolute') return false
		}else if(xItem.parent && xItem.parent.id != '10001' && 
		       (xItem.parent.model=='TBox' || xItem.parent.model=='TList' || xItem.parent.model == 'TTab')){
				   
			if(xItem.parent.hasOwnProperty("attribute") && xItem.parent.attribute.hasOwnProperty("layout") &&
			    xItem.parent.attribute.layout.layoutDirection != 'absolute') return false
				
		}
		return true
	}
	
	function getInnerBoxStyle(){
		let nStyle =  {
			position:'relative',
			width: parseInt(xItem.w) + 'px',
			height:parseInt(xItem.h) + 'px'
		}
		if(xItem.wUnit && '%,vh,vw,'.indexOf(xItem.wUnit+',') >= 0 ){
			nStyle.width = '100%'
		}
		if(xItem.hUnit && '%,vh,vw,'.indexOf(xItem.hUnit+',') >= 0 ){
			nStyle.height = '100%'
		}
		return nStyle
	}
	
	function getBoxStyle(){
		let nStyle = {
			left: parseInt(xItem.x)+'px', 
			width: parseInt(xItem.w) + (xItem.wUnit ? xItem.wUnit : 'px'),
			height:parseInt(xItem.h) + (xItem.hUnit ? xItem.hUnit : 'px')
		}
		
		if(xItem.whOffsetOpen){
			if(xItem.wUnit=='%'){
				nStyle.width = 'calc('+parseInt(xItem.w)+'% '+  (xItem.offsetW >=0 ? ' + ' : ' - ') + Math.abs(xItem.offsetW) +'px)'
			}else if(xItem.wUnit=='vw'){
				nStyle.width = 'calc('+parseInt(appScreen.screenWidth * (xItem.w/100.0))+'px '+  (xItem.offsetW >=0 ? ' + ' : ' - ') + Math.abs(xItem.offsetW) +'px)'
			}else if(xItem.wUnit=='vh'){
				nStyle.width = 'calc('+parseInt(appScreen.screenHeight * (xItem.w/100.0))+'px '+  (xItem.offsetW >=0 ? ' + ' : ' - ') + Math.abs(xItem.offsetW) +'px)'
			}
			if(xItem.hUnit=='%'){
				nStyle.height = 'calc('+parseInt(xItem.h)+'% '+  (xItem.offsetH >=0 ? ' + ' : ' - ') + Math.abs(xItem.offsetH) +'px)'
			}else if(xItem.hUnit=='vw'){
				nStyle.height = 'calc('+parseInt(appScreen.screenWidth * (xItem.h/100.0))+'px '+  (xItem.offsetH >=0 ? ' + ' : ' - ') + Math.abs(xItem.offsetH) +'px)'
			}else if(xItem.hUnit=='vh'){
				nStyle.height = 'calc('+parseInt(appScreen.screenHeight * (xItem.h/100.0))+'px '+  (xItem.offsetH >=0 ? ' + ' : ' - ') + Math.abs(xItem.offsetH) +'px)'
			}
		}else{
			if(xItem.wUnit=='%'){
				nStyle.width = 'calc('+parseInt(xItem.w)+'% )'
			}else if(xItem.wUnit=='vw'){
				nStyle.width = 'calc('+parseInt(appScreen.screenWidth * (xItem.w/100.0))+'px )'
			}else if(xItem.wUnit=='vh'){
				nStyle.width = 'calc('+parseInt(appScreen.screenHeight * (xItem.w/100.0))+'px )'
			}
			if(xItem.hUnit=='%'){
				nStyle.height = 'calc('+parseInt(xItem.h)+'% )'
			}else if(xItem.hUnit=='vw'){
				nStyle.height = 'calc('+parseInt(appScreen.screenWidth * (xItem.h/100.0))+'px )'
			}else if(xItem.hUnit=='vh'){
				nStyle.height = 'calc('+parseInt(appScreen.screenHeight * (xItem.h/100.0))+'px )'
			}
		}

		if(checkParentFreeLayout()){
			nStyle.position = "absolute"
		}else{
			nStyle.position = "static"
			if(xItem.flexOpen){
				nStyle.flex = xItem.flex
			}
		}

		if(xItem.hasOwnProperty("attribute") && xItem.attribute.hasOwnProperty("fixed") &&
		    xItem.attribute.fixed.fixedOpen==true){
				if(xItem.attribute.fixed.fixedMode=='top'){
					nStyle.top = '0px'
				}else if(xItem.attribute.fixed.fixedMode=='bottom'){
					nStyle.top = (appScreen.screenHeight - (xItem.hUnit=='%' ? (appScreen.screenHeight * (xItem.h/100.0)) : xItem.h )) + 'px'
				}else{
					nStyle.top = parseInt(xItem.y)+'px'
				}
		}else{
			nStyle.top = parseInt(xItem.y)+'px'
		}

		let nFontColor = checkEffect("color")
		if(nFontColor.bothUsed){
			let nBasic = getFontColor()
			nStyle['--basic-color'] = nBasic 
			nStyle['--active-color'] = nFontColor.activeUsed ? nFontColor.activeValue : nBasic 
			nStyle['--hover-color'] = nFontColor.hoverUsed ? nFontColor.hoverValue : nBasic
		}else{
			nStyle.color = getFontColor()
		}
		

		if(xItem.key !="TLine" && xItem.model !='TChoose' && xItem.attribute.box){
			let nBorder = checkEffect("border")
			if(nBorder.bothUsed){
				if(nBorder.activeUsed) nStyle['--active-border'] = nBorder.activeValue
				if(nBorder.hoverUsed) nStyle['--hover-border'] = nBorder.hoverValue
			}
			if(xItem.attribute.box.borderLeft && xItem.attribute.box.borderRight && xItem.attribute.box.borderTop && xItem.attribute.box.borderBottom){
				if(nBorder.bothUsed){
					let nBasic = getBorder()
					nStyle['--basic-border'] = nBasic
					nStyle['--active-border'] = nBorder.activeUsed ? nBorder.activeValue : nBasic 
					nStyle['--hover-border'] = nBorder.hoverUsed ? nBorder.hoverValue : nBasic
				}else{
					nStyle.border = getBorder()
				}
				
			}else {
				if(xItem.attribute.box.borderLeft) nStyle.borderLeft = getBorder()
				if(xItem.attribute.box.borderRight) nStyle.borderRight = getBorder()
				if(xItem.attribute.box.borderTop) nStyle.borderTop = getBorder()
				if(xItem.attribute.box.borderBottom) nStyle.borderBottom = getBorder()
			}
		}
		nStyle.opacity = getOpacity()
		
		
		if(xItem.model !='TChoose'){
			let bgUrl = getBackgroundImageUrl()
			if(bgUrl.length > 0){
				nStyle.backgroundImage = 'url('+bgUrl+')' //url(require(bgUrl)
				nStyle.backgroundSize = 'cover'
				nStyle.backgroundPosition = 'center'
				nStyle.backgroundRepeat = 'no-repeat'
			}else{
				if(xItem.hasOwnProperty("attribute") && xItem.attribute.hasOwnProperty("background")
				  && xItem.attribute.background.gradientOpen==true){
					nStyle.background='linear-gradient('+xItem.attribute.background.gradientAngle+'deg ,'+
					  getBackgroundColor() + ','+ xItem.attribute.background.gradientColor2+')'
				}else{
					let nBackground = checkEffect("backgroundColor")
					if(nBackground.bothUsed){
						let nBasic = getBackgroundColor()
						nStyle['--basic-backgroundColor'] = nBasic
						nStyle['--active-backgroundColor'] = nBackground.activeUsed ? nBackground.activeValue : nBasic
						nStyle['--hover-backgroundColor'] = nBackground.hoverUsed ? nBackground.hoverValue : nBasic
					}else{
						nStyle.backgroundColor = getBackgroundColor()
					
					}
				}
			}
			
			let nBorderRadius = checkEffect("borderRadius")
			if(nBorderRadius.bothUsed){
				let nBasic = getRadius()
				nStyle['--basic-borderRadius'] = nBasic 
				nStyle['--active-borderRadius'] = nBorderRadius.activeUsed ? nBorderRadius.activeValue : nBasic
				nStyle['--hover-borderRadius'] = nBorderRadius.hoverUsed ? nBorderRadius.hoverValue : nBasic
			}else{
				nStyle.borderRadius = getRadius()
			}
			
			let nShadow = checkEffect("boxShadow")
			if(nShadow.bothUsed){
				if(nShadow.activeUsed) nStyle['--active-boxShadow'] = nShadow.activeValue
				if(nShadow.hoverUsed) nStyle['--hover-boxShadow'] = nShadow.hoverValue
			}
			if(xItem.hasOwnProperty('attribute') && xItem.attribute.hasOwnProperty('box') &&
			 xItem.attribute.box.hasOwnProperty("shadowType") && xItem.attribute.box.shadowType != 'none'){
				 if(nShadow.bothUsed){
					 let nBasic = getShadow()
					 nStyle['--basic-boxShadow'] = nBasic
					 nStyle['--active-boxShadow'] = nShadow.activeUsed ? nShadow.activeValue : nBasic
					 nStyle['--hover-boxShadow'] = nShadow.hoverUsed ? nShadow.hoverValue : nBasic
				 }else{
					 nStyle.boxShadow = getShadow()
				 }
			}
			
			let transformStr = ""
			if(xItem.hasOwnProperty('attribute') && xItem.attribute.hasOwnProperty('rotation') &&
			      xItem.attribute.rotation > 0){
				transformStr += 'rotate('+(xItem.attribute.rotation ? xItem.attribute.rotation : 0) + 'deg)'
			}
			if(xItem.hasOwnProperty('attribute') && xItem.attribute.hasOwnProperty('box') &&
			      xItem.attribute.box.hasOwnProperty('scale') && xItem.attribute.box.scale > 0  && xItem.attribute.box.scale != 100){
				transformStr += ' scale('+(xItem.attribute.box.scale ? xItem.attribute.box.scale/100.0 : 1)+')'
			}
			let nTransForm = checkEffect("transform")
			if(nTransForm.bothUsed){
				if(nTransForm.activeUsed) nStyle['--active-transform'] = nTransForm.activeValue
				if(nTransForm.hoverUsed) nStyle['--hover-transform'] = nTransForm.hoverValue
			}
			if(transformStr.length > 0){
			    if(nTransForm.bothUsed){
					nStyle['--basic-transform'] = transformStr
					nStyle['--active-transform'] = nTransForm.activeUsed ? nTransForm.activeValue : transformStr
					nStyle['--hover-transform'] = nTransForm.hoverUsed ? nTransForm.hoverValue : transformStr
				}else{
					nStyle.transform = transformStr
				}
			}
			
		}
		
		nStyle.margin = getMargin()
		let nFontSize = checkEffect("fontSize")
		if(nFontSize.bothUsed){
			let nBasic = getFontSize()
			nStyle['--basic-fontSize'] = nBasic
			nStyle['--active-fontSize'] = nFontSize.activeUsed ? nFontSize.activeValue : nBasic 
			nStyle['--hover-fontSize'] = nFontSize.hoverUsed ? nFontSize.hoverValue : nBasic
		}else{
			nStyle.fontSize=getFontSize()
		}

		nStyle.fontFamily = getFontFamily()
		nStyle.fontWeight = getFontWeight()
		
		if(xItem.hasOwnProperty("attribute") && xItem.attribute.hasOwnProperty("fixed") &&
		    xItem.attribute.fixed.fixedOpen==true){
				nStyle.zIndex = 990
		}else{
			nStyle.zIndex = xItem.zindex
		}
		
		if(",TSwitch,TLine,".includes(","+xItem.key+",") && nStyle.hasOwnProperty("backgroundColor")){
			delete nStyle.backgroundColor
		}
		
		return nStyle
	}
	
	function getLayoutStyle(){
		let nStyle={}
		if(xItem.hasOwnProperty("attribute") && xItem.attribute.hasOwnProperty("layout")){
		    if(xItem.attribute.layout.layoutDirection == 'column') {
				nStyle.display = 'flex'
				nStyle.flexDirection = 'column'
				if(xItem.attribute.layout.vAlign=='align-top'){
					nStyle.justifyContent = 'flex-start'
				}else if(xItem.attribute.layout.vAlign=='align-bottom'){
					nStyle.justifyContent = 'flex-end'
				}else if(xItem.attribute.layout.vAlign=='align-center'){
					nStyle.justifyContent = 'center'
				}else if(xItem.attribute.layout.vAlign=='align-stretch'){
					nStyle.justifyContent = 'space-between'
				}
				
				if(xItem.attribute.layout.hAlign=='align-left'){
					nStyle.alignItems = 'flex-start'
				}else if(xItem.attribute.layout.hAlign=='align-right'){
					nStyle.alignItems = 'flex-end'
				}else if(xItem.attribute.layout.hAlign=='align-center'){
					nStyle.alignItems = 'center'
				}else if(xItem.attribute.layout.hAlign=='align-stretch'){
					nStyle.alignItems = 'stretch'
				}
			}else if(xItem.attribute.layout.layoutDirection == 'row'){
				nStyle.display = 'flex'
				nStyle.flexDirection = 'row'
				if(xItem.attribute.layout.hAlign=='align-left'){
					nStyle.justifyContent = 'flex-start'
				}else if(xItem.attribute.layout.hAlign=='align-right'){
					nStyle.justifyContent = 'flex-end'
				}else if(xItem.attribute.layout.hAlign=='align-center'){
					nStyle.justifyContent = 'center'
				}else if(xItem.attribute.layout.hAlign=='align-stretch'){
					nStyle.justifyContent = 'space-between'
				}
				
				if(xItem.attribute.layout.vAlign=='align-top'){
					nStyle.alignItems = 'flex-start'
				}else if(xItem.attribute.layout.vAlign=='align-bottom'){
					nStyle.alignItems = 'flex-end'
				}else if(xItem.attribute.layout.vAlign=='align-center'){
					nStyle.alignItems = 'center'
				}else if(xItem.attribute.layout.vAlign=='align-stretch'){
					nStyle.alignItems = 'stretch'
				}
			}
		}
		
		nStyle.overflowY = 'hidden'
		nStyle.overflowX = 'hidden'
		if(xItem.attribute.layout.scrollHOpen && xItem.attribute.layout.scrollHOpen==true){
			nStyle.overflowX = 'auto'
		}
		if(xItem.attribute.layout.scrollVOpen && xItem.attribute.layout.scrollVOpen==true){
			nStyle.overflowY = 'auto'
		}
		return nStyle
	}
	
	function getChooseStyle(){
		let nStyle = {}
		if(xItem.hasOwnProperty("attribute") && xItem.attribute.hasOwnProperty("background")
		  && xItem.attribute.background.gradientOpen==true){
			nStyle.background='linear-gradient('+xItem.attribute.background.gradientAngle+'deg ,'+
			  getBackgroundColor() + ','+ xItem.attribute.background.gradientColor2+')'
		}else{
			let nBackground = checkEffect("backgroundColor")
			if(nBackground.bothUsed){
				let nBasic = getBackgroundColor()
				nStyle['--basic-backgroundColor'] = nBasic
				nStyle['--active-backgroundColor'] = nBackground.activeUsed ? nBackground.activeValue : nBasic
				nStyle['--hover-backgroundColor'] = nBackground.hoverUsed ? nBackground.hoverValue : nBasic
			}else{
				nStyle.backgroundColor = getBackgroundColor()
			}
		}
		
		let nBorderRadius = checkEffect("borderRadius")
		if(nBorderRadius.bothUsed){
			let nBasic = getRadius()
			nStyle['--basic-borderRadius'] = nBasic 
			nStyle['--active-borderRadius'] = nBorderRadius.activeUsed ? nBorderRadius.activeValue : nBasic
			nStyle['--hover-borderRadius'] = nBorderRadius.hoverUsed ? nBorderRadius.hoverValue : nBasic
		}else{
			nStyle.borderRadius = getRadius()
		}
		
		let nBorder = checkEffect("border")
		if(nBorder.bothUsed){
			if(nBorder.activeUsed) nStyle['--active-border'] = nBorder.activeValue
			if(nBorder.hoverUsed) nStyle['--hover-border'] = nBorder.hoverValue
		}
		if(xItem.attribute.box.borderLeft && xItem.attribute.box.borderRight && xItem.attribute.box.borderTop && xItem.attribute.box.borderBottom){
			if(nBorder.bothUsed){
				let nBasic = getBorder()
				nStyle['--basic-border'] = nBasic
				nStyle['--active-border'] = nBorder.activeUsed ? nBorder.activeValue : nBasic 
				nStyle['--hover-border'] = nBorder.hoverUsed ? nBorder.hoverValue : nBasic
			}else{
				nStyle.border = getBorder()
			}
			
		}else {
			if(xItem.attribute.box.borderLeft) nStyle.borderLeft = getBorder()
			if(xItem.attribute.box.borderRight) nStyle.borderRight = getBorder()
			if(xItem.attribute.box.borderTop) nStyle.borderTop = getBorder()
			if(xItem.attribute.box.borderBottom) nStyle.borderBottom = getBorder()
		}
		
		let nShadow = checkEffect("boxShadow")
		if(nShadow.bothUsed){
			if(nShadow.activeUsed) nStyle['--active-boxShadow'] = nShadow.activeValue
			if(nShadow.hoverUsed) nStyle['--hover-boxShadow'] = nShadow.hoverValue
		}
		if(xItem.hasOwnProperty('attribute') && xItem.attribute.hasOwnProperty('box') &&
		 xItem.attribute.box.hasOwnProperty("shadowType") && xItem.attribute.box.shadowType != 'none'){
			 if(nShadow.bothUsed){
				 let nBasic = getShadow()
				 nStyle['--basic-boxShadow'] = nBasic
				 nStyle['--active-boxShadow'] = nShadow.activeUsed ? nShadow.activeValue : nBasic
				 nStyle['--hover-boxShadow'] = nShadow.hoverUsed ? nShadow.hoverValue : nBasic
			 }else{
				 nStyle.boxShadow = getShadow()
			 }
		}
		
		let transformStr = ""
		if(xItem.hasOwnProperty('attribute') && xItem.attribute.hasOwnProperty('rotation') &&
		      xItem.attribute.rotation > 0){
			transformStr += 'rotate('+(xItem.attribute.rotation ? xItem.attribute.rotation : 0) + 'deg)'
		}
		if(xItem.hasOwnProperty('attribute') && xItem.attribute.hasOwnProperty('box') &&
		      xItem.attribute.box.hasOwnProperty('scale') && xItem.attribute.box.scale > 0  && xItem.attribute.box.scale != 100){
			transformStr += ' scale('+(xItem.attribute.box.scale ? xItem.attribute.box.scale/100.0 : 1)+')'
		}
		let nTransForm = checkEffect("transform")
		if(nTransForm.bothUsed){
			if(nTransForm.activeUsed) nStyle['--active-transform'] = nTransForm.activeValue
			if(nTransForm.hoverUsed) nStyle['--hover-transform'] = nTransForm.hoverValue
		}
		if(transformStr.length > 0){
		    if(nTransForm.bothUsed){
				nStyle['--basic-transform'] = transformStr
				nStyle['--active-transform'] = nTransForm.activeUsed ? nTransForm.activeValue : transformStr
				nStyle['--hover-transform'] = nTransForm.hoverUsed ? nTransForm.hoverValue : transformStr
			}else{
				nStyle.transform = transformStr
			}
		}
		nStyle.margin = getMargin()
		nStyle.padding = getPadding()
		nStyle.cursor = getCursorType()
		
		return nStyle
	}
	
	function getInputStyle(){
		let nStyle = {
			
		}
		nStyle.color = getFontColor()
		nStyle.fontSize=getFontSize()
		nStyle.fontFamily = getFontFamily()
		nStyle.fontWeight = getFontWeight()
		nStyle.textDecoration = getTextDecoration()
		nStyle.textAlign = getTextAlign()
		
		return nStyle 
	}
	
	function getLineStyle(){
		let nStyle = {
			borderBottom:'0px',
			borderRight:'0px'
		}
		if(getLineDirect('horizontal')){
			nStyle.borderTop = getLineBorder()
			nStyle.borderLeft = '0px'
		}else{
			nStyle.borderTop = '0px'
			nStyle.borderLeft = getLineBorder()
		}
		
		return nStyle
	}
	
	function getLineBorder(){
		if(xItem.hasOwnProperty('attribute') && xItem.attribute.hasOwnProperty('line')){
			return xItem.attribute.line.borderSize+"px "+xItem.attribute.line.borderStyle+" "+ (checkEnabled() ? xItem.attribute.line.borderColor : '#dadada')
		}else{
			return '1px solid '+ ( checkEnabled() ? ProjectRef.BasicData.theme.currentTheme.color : '#dadada')
		}
	}
	
	function getLineDirect(diret){
		if(xItem.model !='TLine') return true
		if(xItem.hasOwnProperty('attribute') && xItem.attribute.hasOwnProperty('line')
		    && xItem.attribute.line.hasOwnProperty('direct')){
				return xItem.attribute.line.direct == diret
		}else{
			return 'horizontal' == diret
		}
	}
	
	function checkEnabled(){
		if(xItem.hasOwnProperty('enabled') && !xItem.enabled){
			return false
		}else{
			return true
		}
	}
	
	function getThemeColor(){
		return ProjectRef.BasicData.theme.currentTheme.color
	}
	
	function getChooseDirect(){
		if(xItem.hasOwnProperty('attribute') && xItem.attribute.hasOwnProperty("choose")){
			return xItem.attribute.choose.direct
		}
		return 'horizontal'
	}
	
	function getChooseItems(){
		if(xItem.hasOwnProperty('attribute') && xItem.attribute.hasOwnProperty("choose")){
			return xItem.attribute.choose.dataList
		}
		return [
			{
				label:"item1",
				value:'value1'
			},
			{
				label:"item2",
				value:'value2'
			}
		]
	}
	
	
	function checkEffect(gentName){
		let result = {bothUsed:false,hoverUsed:false,activeUsed:false,hoverValue:"",activeValue:""}
		if(!xItem.hasOwnProperty("attribute")) return result
		if(xItem.attribute.hasOwnProperty("active") &&  xItem.attribute.active.used==true){
			if(gentName=='backgroundColor'){
				if(xItem.attribute.active.backgroundColorOpen){
					result.bothUsed = true
					result.activeUsed = true
					result.activeValue = xItem.attribute.active.backgroundColor
				}
			}else if(gentName=='color'){
				if(xItem.attribute.active.colorOpen){
					result.bothUsed = true
					result.activeUsed = true
					result.activeValue = xItem.attribute.active.color
				}
			}else if(gentName == 'fontSize'){
				if(xItem.attribute.active.fontSizeOpen){
					result.bothUsed = true
					result.activeUsed = true
					result.activeValue = xItem.attribute.active.fontSize
				}
			}else if(gentName=='border'){
				if(xItem.attribute.active.borderOpen){
					result.bothUsed = true
					result.activeUsed = true
					result.activeValue = xItem.attribute.active.borderSize+"px "+ 
					     xItem.attribute.active.borderStyle+" "+ xItem.attribute.active.borderColor;
				}
			}else if(gentName=='boxShadow'){
				if(xItem.attribute.active.shadowOpen){
					result.bothUsed = true
					result.activeUsed = true
					result.activeValue = (xItem.attribute.active.shadowType ? "" : 'inset ') + 
					    xItem.attribute.active.shadowOffsetX+"px "+ xItem.attribute.active.shadowOffsetY+"px " +
					     xItem.attribute.active.shadowBlur+"px "+ xItem.attribute.active.shadowSpread+"px " + xItem.attribute.active.shadowColor;
				}
			}else if(gentName=='borderRadius'){
				if(xItem.attribute.active.radiusOpen){
					result.bothUsed = true
					result.activeUsed = true
					result.activeValue = xItem.attribute.active.radius +"px"
				}
			}else if(gentName == 'transform'){
				if(xItem.attribute.active.scaleOpen && xItem.attribute.active.scale > 0 && xItem.attribute.active.scale != 100){
					result.bothUsed = true
					result.activeUsed = true
					result.activeValue = "scale(" + (xItem.attribute.active.scale/100.0) +") "
				}
				if(xItem.attribute.active.rotateOpen && xItem.attribute.active.rotate > 0 ){
					result.bothUsed = true
					result.activeUsed = true
					result.activeValue += " rotate(" + xItem.attribute.active.rotate +"deg) "
				}
			}
		}
		
		if(xItem.attribute.hasOwnProperty("hover") &&  xItem.attribute.hover.used==true){
			if(gentName=='backgroundColor'){
				if(xItem.attribute.hover.backgroundColorOpen){
					result.bothUsed = true
					result.hoverUsed = true
					result.hoverValue = xItem.attribute.hover.backgroundColor
				}
			}else if(gentName=='color'){
				if(xItem.attribute.hover.colorOpen){
					result.bothUsed = true
					result.hoverUsed = true
					result.hoverValue = xItem.attribute.hover.color
				}
			}else if(gentName == 'fontSize'){
				if(xItem.attribute.hover.fontSizeOpen){
					result.bothUsed = true
					result.hoverUsed = true
					result.hoverValue = xItem.attribute.hover.fontSize
				}
			}else if(gentName=='border'){
				if(xItem.attribute.hover.borderOpen){
					result.bothUsed = true
					result.hoverUsed = true
					result.hoverValue = xItem.attribute.hover.borderSize+"px "+ 
					     xItem.attribute.hover.borderStyle+" "+ xItem.attribute.hover.borderColor;
				}
			}else if(gentName=='boxShadow'){
				if(xItem.attribute.hover.shadowOpen){
					result.bothUsed = true
					result.hoverUsed = true
					result.hoverValue = (xItem.attribute.hover.shadowType ? "" : 'inset ') + 
					    xItem.attribute.hover.shadowOffsetX+"px "+ xItem.attribute.hover.shadowOffsetY+"px " +
					     xItem.attribute.hover.shadowBlur+"px "+ xItem.attribute.hover.shadowSpread+"px " + xItem.attribute.hover.shadowColor;
				}
			}else if(gentName=='borderRadius'){
				if(xItem.attribute.hover.radiusOpen){
					result.bothUsed = true
					result.hoverUsed = true
					result.hoverValue = xItem.attribute.hover.radius +"px"
				}
			}else if(gentName == 'transform'){
				if(xItem.attribute.hover.scaleOpen && xItem.attribute.hover.scale > 0 && xItem.attribute.hover.scale != 100){
					result.bothUsed = true
					result.hoverUsed = true
					result.hoverValue = "scale(" + (xItem.attribute.hover.scale/100.0) +") "
				}
				if(xItem.attribute.hover.rotateOpen && xItem.attribute.hover.rotate > 0 ){
					result.bothUsed = true
					result.hoverUsed = true
					result.hoverValue += " rotate(" + xItem.attribute.hover.rotate +"deg) "
				}
			}
		}

        return 	result		
		
	}
   
   //TBox TList
   const ChooseComponentList = inject("ChooseComponentList")
   const checkEnter = ref(0)
   
   function onDragover(e){
   	 e.preventDefault()
	 // checkEnter.value = 1
   }
   
   function onDrop(e){
   	checkEnter.value=0
   	ChooseComponentList.handleDrop(xItem,e)
   }
   
   function onMouseEnter(e){
   	checkEnter.value = 1
   }
   function onMouseLeave(e){
   	checkEnter.value = 0
   }
	
	return {onDragover,onDrop,onMouseEnter,onMouseLeave,checkEnter,getFontColor,getBorder,getThemeColor,getTextDecoration,getChooseStyle,
	          getBackgroundColor,getStyle,getShadow,getRadius,getBoxStyle,getLineStyle,getLineDirect,getTextAlign,getCursorType,getInputStyle,
			  getChooseDirect,getChooseItems,checkEnabled,setXItem,getInnerBoxStyle,checkParentFreeLayout,getLayoutStyle,checkEffect}
}