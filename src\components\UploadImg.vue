<template>
  <div class="image-uploader">
    <el-upload
      :action="uploadUrl"
      :headers="headers"
      :data="uploadData"
      name="file"
      :show-file-list="false"
      :on-success="handleSuccess"
      :on-error="handleError"
      :before-upload="beforeUpload"
      :accept="accept"
      :disabled="disabled"
    >
      <template #trigger>
        <div
          class="upload-area"
          @mouseenter="showActions = true"
          @mouseleave="showActions = false"
        >
          <img v-if="imageUrl" :src="imageUrl" class="upload-image" />
          <el-icon v-else class="upload-icon"><Plus /></el-icon>
          <div v-if="!imageUrl" class="upload-text">
            {{ placeholder || "点击上传图片" }}
          </div>

          <!-- 操作按钮 - 只在有图片且hover时显示 -->
          <div v-if="imageUrl && showActions" class="action-overlay">
            <div class="action-buttons">
              <el-button type="primary" circle @click.stop="handlePreview">
                <el-icon><ZoomIn /></el-icon>
              </el-button>
              <el-button type="danger" circle @click.stop="handleRemove">
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
          </div>
        </div>
      </template>
    </el-upload>

    <el-dialog v-model="dialogVisible" title="图片预览" width="50%">
      <img :src="imageUrl" class="preview-image" />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, watch } from "vue";
import { Plus, ZoomIn, Delete } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";

const props = defineProps({
  modelValue: String,
  uploadUrl: {
    type: String,
    default: "/devApi/file/imageUpload",
  },
  headers: {
    type: Object,
    default: () => ({
      Authorization: `Bearer ${localStorage.getItem("token")}`,
    }),
  },
  uploadData: {
    type: Object,
    default: () => ({
      source: 0, //0项目上传 1用户头像
    }),
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  placeholder: String,
  accept: {
    type: String,
    default: "image/jpeg,image/png,image/gif",
  },
  sizeLimit: {
    type: Number,
    default: 2,
  },
});

const emit = defineEmits(["update:modelValue", "success", "error"]);

const imageUrl = ref("");
const dialogVisible = ref(false);
const showActions = ref(false);

watch(
  () => props.modelValue,
  (val) => {
    imageUrl.value = val;
  },
  { immediate: true }
);

const beforeUpload = (file) => {
  const isImage = ["image/jpeg", "image/png", "image/gif"].includes(file.type);
  const isLtSize = file.size / 1024 / 1024 < props.sizeLimit;

  if (!isImage) {
    ElMessage.error("只能上传 JPG/PNG/GIF 格式的图片!");
    return false;
  }

  if (!isLtSize) {
    ElMessage.error(`图片大小不能超过 ${props.sizeLimit}MB!`);
    return false;
  }

  return true;
};

const handleSuccess = (response) => {
  if (response.code === 200) {
    imageUrl.value = response.data;
    emit("update:modelValue", response.data);
    emit("success", response);
    ElMessage.success(response.message || "上传成功");
  } else {
    ElMessage.error(response.message || "上传失败");
    emit("error", response);
  }
};

const handleError = (error) => {
  ElMessage.error("上传失败: " + (error.message || "未知错误"));
  emit("error", error);
};

const handlePreview = () => {
  dialogVisible.value = true;
};

const handleRemove = () => {
  imageUrl.value = "";
  emit("update:modelValue", "");
  showActions.value = false;
};
</script>

<style scoped>
.image-uploader {
  display: inline-block;
  position: relative;
}

.upload-area {
  width: 104px;
  height: 104px;
  background: #f5f5f5;
  border-radius: 6px 6px 6px 6px;
  border: 1px solid #dedede;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #8c939d;
  transition: border-color 0.3s;
}

.upload-area:hover {
  border-color: #409eff;
}

.upload-icon {
  font-size: 24px;
  margin-bottom: 12px;
  color: #000000;
}

.upload-text {
  font-size: 14px;
  color: #000000;
}

.upload-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 操作按钮遮罩层 */
.action-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}

/* 操作按钮容器 */
.action-buttons {
  display: flex;
  gap: 10px;
}

/* 按钮样式 */
.action-buttons .el-button {
  width: 32px;
  height: 32px;
  padding: 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.preview-image {
  width: 100%;
  display: block;
}
</style>
