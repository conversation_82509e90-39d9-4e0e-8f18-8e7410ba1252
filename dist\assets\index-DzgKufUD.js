import{bs as J,K as Ee,H as ze,N as we,b as L,W as $e,Q as ne,J as ce,B as Be,ap as Ve,bg as Me,V as de,b6 as Re,M as k,aB as ke,aj as fe,ag as oe,cq as De,b5 as He,b4 as Ke,b3 as Ne,ao as Fe,a2 as N,ai as Ue,_ as Ye,r as D,g as E,o as v,f as w,w as Q,a as B,n as c,a8 as j,j as me,c as Z,D as z,R as H,t as K,at as W,k as x,ah as _,aL as Xe,cr as je,y as We,b2 as qe,e as $,d as Ge,ae as se,b0 as ve,cs as pe,$ as Je,bu as te,af as le,bx as Se,bL as Te,aD as ge,P as Ie,aV as he,aX as Qe}from"./index-Dgb0NZ1J.js";import{E as Ze,h as ye}from"./el-button-B8TCQS4u.js";import{E as xe}from"./index-C-aKrRv3.js";import{P as ee,b as _e,o as be,E as en}from"./vnode-CV7pw3rS.js";import{t as nn}from"./index-CINbulG0.js";const Le=e=>{if(!e)return{onClick:J,onMousedown:J,onMouseup:J};let n=!1,a=!1;return{onClick:i=>{n&&a&&e(i),n=a=!1},onMousedown:i=>{n=i.target===i.currentTarget},onMouseup:i=>{a=i.target===i.currentTarget}}},on=ze({mask:{type:Boolean,default:!0},customMaskEvent:Boolean,overlayClass:{type:ce([String,Array,Object])},zIndex:{type:ce([String,Number])}}),sn={click:e=>e instanceof MouseEvent},tn="overlay";var an=Ee({name:"ElOverlay",props:on,emits:sn,setup(e,{slots:n,emit:a}){const l=we(tn),t=f=>{a("click",f)},{onClick:s,onMousedown:i,onMouseup:d}=Le(e.customMaskEvent?void 0:t);return()=>e.mask?L("div",{class:[l.b(),e.overlayClass],style:{zIndex:e.zIndex},onClick:s,onMousedown:i,onMouseup:d},[ne(n,"default")],ee.STYLE|ee.CLASS|ee.PROPS,["onClick","onMouseup","onMousedown"]):$e("div",{class:e.overlayClass,style:{zIndex:e.zIndex,position:"fixed",top:"0px",right:"0px",bottom:"0px",left:"0px"}},[ne(n,"default")])}});const ln=an,rn=(e,n,a,l)=>{const t={offsetX:0,offsetY:0},s=(u,y)=>{if(e.value){const{offsetX:A,offsetY:P}=t,m=e.value.getBoundingClientRect(),M=m.left,p=m.top,C=m.width,T=m.height,R=document.documentElement.clientWidth,U=document.documentElement.clientHeight,q=-M+A,I=-p+P,Y=R-M-C+A,G=U-p-(T<U?T:0)+P;l!=null&&l.value||(u=Math.min(Math.max(u,q),Y),y=Math.min(Math.max(y,I),G)),t.offsetX=u,t.offsetY=y,e.value.style.transform=`translate(${de(u)}, ${de(y)})`}},i=u=>{const y=u.clientX,A=u.clientY,{offsetX:P,offsetY:m}=t,M=C=>{const T=P+C.clientX-y,R=m+C.clientY-A;s(T,R)},p=()=>{document.removeEventListener("mousemove",M),document.removeEventListener("mouseup",p)};document.addEventListener("mousemove",M),document.addEventListener("mouseup",p)},d=()=>{n.value&&e.value&&(n.value.addEventListener("mousedown",i),window.addEventListener("resize",b))},f=()=>{n.value&&e.value&&(n.value.removeEventListener("mousedown",i),window.removeEventListener("resize",b))},o=()=>{t.offsetX=0,t.offsetY=0,e.value&&(e.value.style.transform="")},b=()=>{const{offsetX:u,offsetY:y}=t;s(u,y)};return Be(()=>{Ve(()=>{a.value?d():f()})}),Me(()=>{f()}),{resetPosition:o,updatePosition:b}},un=(e,n={})=>{Re(e)||nn("[useLockscreen]","You need to pass a ref param to this function");const a=n.ns||we("popup"),l=k(()=>a.bm("parent","hidden"));if(!ke||fe(document.body,l.value))return;let t=0,s=!1,i="0";const d=()=>{setTimeout(()=>{typeof document>"u"||s&&document&&(document.body.style.width=i,Ne(document.body,l.value))},200)};oe(e,f=>{if(!f){d();return}s=!fe(document.body,l.value),s&&(i=document.body.style.width,He(document.body,l.value)),t=_e(a.namespace.value);const o=document.documentElement.clientHeight<document.body.scrollHeight,b=Ke(document.body,"overflowY");t>0&&(o||b==="scroll")&&s&&(document.body.style.width=`calc(100% - ${t}px)`)}),De(()=>d())},cn=e=>["",...Fe].includes(e),ae="_trap-focus-children",O=[],Ce=e=>{if(O.length===0)return;const n=O[O.length-1][ae];if(n.length>0&&e.code===Ue.tab){if(n.length===1){e.preventDefault(),document.activeElement!==n[0]&&n[0].focus();return}const a=e.shiftKey,l=e.target===n[0],t=e.target===n[n.length-1];l&&a&&(e.preventDefault(),n[n.length-1].focus()),t&&!a&&(e.preventDefault(),n[0].focus())}},dn={beforeMount(e){e[ae]=be(e),O.push(e),O.length<=1&&document.addEventListener("keydown",Ce)},updated(e){N(()=>{e[ae]=be(e)})},unmounted(){O.shift(),O.length===0&&document.removeEventListener("keydown",Ce)}},fn=Ee({name:"ElMessageBox",directives:{TrapFocus:dn},components:{ElButton:Ze,ElFocusTrap:en,ElInput:xe,ElOverlay:ln,ElIcon:We,...je},inheritAttrs:!1,props:{buttonSize:{type:String,validator:cn},modal:{type:Boolean,default:!0},lockScroll:{type:Boolean,default:!0},showClose:{type:Boolean,default:!0},closeOnClickModal:{type:Boolean,default:!0},closeOnPressEscape:{type:Boolean,default:!0},closeOnHashChange:{type:Boolean,default:!0},center:Boolean,draggable:Boolean,overflow:Boolean,roundButton:{default:!1,type:Boolean},container:{type:String,default:"body"},boxType:{type:String,default:""}},emits:["vanish","action"],setup(e,{emit:n}){const{locale:a,zIndex:l,ns:t,size:s}=qe("message-box",k(()=>e.buttonSize)),{t:i}=a,{nextZIndex:d}=l,f=$(!1),o=Ge({autofocus:!0,beforeClose:null,callback:null,cancelButtonText:"",cancelButtonClass:"",confirmButtonText:"",confirmButtonClass:"",customClass:"",customStyle:{},dangerouslyUseHTMLString:!1,distinguishCancelAndClose:!1,icon:"",closeIcon:"",inputPattern:null,inputPlaceholder:"",inputType:"text",inputValue:"",inputValidator:void 0,inputErrorMessage:"",message:"",modalFade:!0,modalClass:"",showCancelButton:!1,showConfirmButton:!0,type:"",title:void 0,showInput:!1,action:"",confirmButtonLoading:!1,cancelButtonLoading:!1,confirmButtonLoadingIcon:se(ve),cancelButtonLoadingIcon:se(ve),confirmButtonDisabled:!1,editorErrorMessage:"",validateError:!1,zIndex:d()}),b=k(()=>{const r=o.type;return{[t.bm("icon",r)]:r&&pe[r]}}),u=ye(),y=ye(),A=k(()=>{const r=o.type;return o.icon||r&&pe[r]||""}),P=k(()=>!!o.message),m=$(),M=$(),p=$(),C=$(),T=$(),R=k(()=>o.confirmButtonClass);oe(()=>o.inputValue,async r=>{await N(),e.boxType==="prompt"&&r&&ie()},{immediate:!0}),oe(()=>f.value,r=>{var g,h;r&&(e.boxType!=="prompt"&&(o.autofocus?p.value=(h=(g=T.value)==null?void 0:g.$el)!=null?h:m.value:p.value=m.value),o.zIndex=d()),e.boxType==="prompt"&&(r?N().then(()=>{var ue;C.value&&C.value.$el&&(o.autofocus?p.value=(ue=Ae())!=null?ue:m.value:p.value=m.value)}):(o.editorErrorMessage="",o.validateError=!1))});const U=k(()=>e.draggable),q=k(()=>e.overflow);rn(m,M,U,q),Be(async()=>{await N(),e.closeOnHashChange&&window.addEventListener("hashchange",I)}),Me(()=>{e.closeOnHashChange&&window.removeEventListener("hashchange",I)});function I(){f.value&&(f.value=!1,N(()=>{o.action&&n("action",o.action)}))}const Y=()=>{e.closeOnClickModal&&X(o.distinguishCancelAndClose?"close":"cancel")},G=Le(Y),Oe=r=>{if(o.inputType!=="textarea")return r.preventDefault(),X("confirm")},X=r=>{var g;e.boxType==="prompt"&&r==="confirm"&&!ie()||(o.action=r,o.beforeClose?(g=o.beforeClose)==null||g.call(o,r,o,I):I())},ie=()=>{if(e.boxType==="prompt"){const r=o.inputPattern;if(r&&!r.test(o.inputValue||""))return o.editorErrorMessage=o.inputErrorMessage||i("el.messagebox.error"),o.validateError=!0,!1;const g=o.inputValidator;if(te(g)){const h=g(o.inputValue);if(h===!1)return o.editorErrorMessage=o.inputErrorMessage||i("el.messagebox.error"),o.validateError=!0,!1;if(le(h))return o.editorErrorMessage=h,o.validateError=!0,!1}}return o.editorErrorMessage="",o.validateError=!1,!0},Ae=()=>{var r,g;const h=(r=C.value)==null?void 0:r.$refs;return(g=h==null?void 0:h.input)!=null?g:h==null?void 0:h.textarea},re=()=>{X("close")},Pe=()=>{e.closeOnPressEscape&&re()};return e.lockScroll&&un(f),{...Je(o),ns:t,overlayEvent:G,visible:f,hasMessage:P,typeClass:b,contentId:u,inputId:y,btnSize:s,iconComponent:A,confirmButtonClasses:R,rootRef:m,focusStartRef:p,headerRef:M,inputRef:C,confirmRef:T,doClose:I,handleClose:re,onCloseRequested:Pe,handleWrapperClick:Y,handleInputEnter:Oe,handleAction:X,t:i}}});function mn(e,n,a,l,t,s){const i=D("el-icon"),d=D("el-input"),f=D("el-button"),o=D("el-focus-trap"),b=D("el-overlay");return v(),E(Xe,{name:"fade-in-linear",onAfterLeave:u=>e.$emit("vanish"),persisted:""},{default:w(()=>[Q(L(b,{"z-index":e.zIndex,"overlay-class":[e.ns.is("message-box"),e.modalClass],mask:e.modal},{default:w(()=>[B("div",{role:"dialog","aria-label":e.title,"aria-modal":"true","aria-describedby":e.showInput?void 0:e.contentId,class:c(`${e.ns.namespace.value}-overlay-message-box`),onClick:e.overlayEvent.onClick,onMousedown:e.overlayEvent.onMousedown,onMouseup:e.overlayEvent.onMouseup},[L(o,{loop:"",trapped:e.visible,"focus-trap-el":e.rootRef,"focus-start-el":e.focusStartRef,onReleaseRequested:e.onCloseRequested},{default:w(()=>[B("div",{ref:"rootRef",class:c([e.ns.b(),e.customClass,e.ns.is("draggable",e.draggable),{[e.ns.m("center")]:e.center}]),style:me(e.customStyle),tabindex:"-1",onClick:j(()=>{},["stop"])},[e.title!==null&&e.title!==void 0?(v(),Z("div",{key:0,ref:"headerRef",class:c([e.ns.e("header"),{"show-close":e.showClose}])},[B("div",{class:c(e.ns.e("title"))},[e.iconComponent&&e.center?(v(),E(i,{key:0,class:c([e.ns.e("status"),e.typeClass])},{default:w(()=>[(v(),E(H(e.iconComponent)))]),_:1},8,["class"])):z("v-if",!0),B("span",null,K(e.title),1)],2),e.showClose?(v(),Z("button",{key:0,type:"button",class:c(e.ns.e("headerbtn")),"aria-label":e.t("el.messagebox.close"),onClick:u=>e.handleAction(e.distinguishCancelAndClose?"close":"cancel"),onKeydown:W(j(u=>e.handleAction(e.distinguishCancelAndClose?"close":"cancel"),["prevent"]),["enter"])},[L(i,{class:c(e.ns.e("close"))},{default:w(()=>[(v(),E(H(e.closeIcon||"close")))]),_:1},8,["class"])],42,["aria-label","onClick","onKeydown"])):z("v-if",!0)],2)):z("v-if",!0),B("div",{id:e.contentId,class:c(e.ns.e("content"))},[B("div",{class:c(e.ns.e("container"))},[e.iconComponent&&!e.center&&e.hasMessage?(v(),E(i,{key:0,class:c([e.ns.e("status"),e.typeClass])},{default:w(()=>[(v(),E(H(e.iconComponent)))]),_:1},8,["class"])):z("v-if",!0),e.hasMessage?(v(),Z("div",{key:1,class:c(e.ns.e("message"))},[ne(e.$slots,"default",{},()=>[e.dangerouslyUseHTMLString?(v(),E(H(e.showInput?"label":"p"),{key:1,for:e.showInput?e.inputId:void 0,innerHTML:e.message},null,8,["for","innerHTML"])):(v(),E(H(e.showInput?"label":"p"),{key:0,for:e.showInput?e.inputId:void 0},{default:w(()=>[x(K(e.dangerouslyUseHTMLString?"":e.message),1)]),_:1},8,["for"]))])],2)):z("v-if",!0)],2),Q(B("div",{class:c(e.ns.e("input"))},[L(d,{id:e.inputId,ref:"inputRef",modelValue:e.inputValue,"onUpdate:modelValue":u=>e.inputValue=u,type:e.inputType,placeholder:e.inputPlaceholder,"aria-invalid":e.validateError,class:c({invalid:e.validateError}),onKeydown:W(e.handleInputEnter,["enter"])},null,8,["id","modelValue","onUpdate:modelValue","type","placeholder","aria-invalid","class","onKeydown"]),B("div",{class:c(e.ns.e("errormsg")),style:me({visibility:e.editorErrorMessage?"visible":"hidden"})},K(e.editorErrorMessage),7)],2),[[_,e.showInput]])],10,["id"]),B("div",{class:c(e.ns.e("btns"))},[e.showCancelButton?(v(),E(f,{key:0,loading:e.cancelButtonLoading,"loading-icon":e.cancelButtonLoadingIcon,class:c([e.cancelButtonClass]),round:e.roundButton,size:e.btnSize,onClick:u=>e.handleAction("cancel"),onKeydown:W(j(u=>e.handleAction("cancel"),["prevent"]),["enter"])},{default:w(()=>[x(K(e.cancelButtonText||e.t("el.messagebox.cancel")),1)]),_:1},8,["loading","loading-icon","class","round","size","onClick","onKeydown"])):z("v-if",!0),Q(L(f,{ref:"confirmRef",type:"primary",loading:e.confirmButtonLoading,"loading-icon":e.confirmButtonLoadingIcon,class:c([e.confirmButtonClasses]),round:e.roundButton,disabled:e.confirmButtonDisabled,size:e.btnSize,onClick:u=>e.handleAction("confirm"),onKeydown:W(j(u=>e.handleAction("confirm"),["prevent"]),["enter"])},{default:w(()=>[x(K(e.confirmButtonText||e.t("el.messagebox.confirm")),1)]),_:1},8,["loading","loading-icon","class","round","disabled","size","onClick","onKeydown"]),[[_,e.showConfirmButton]])],2)],14,["onClick"])]),_:3},8,["trapped","focus-trap-el","focus-start-el","onReleaseRequested"])],42,["aria-label","aria-describedby","onClick","onMousedown","onMouseup"])]),_:3},8,["z-index","overlay-class","mask"]),[[_,e.visible]])]),_:3},8,["onAfterLeave"])}var vn=Ye(fn,[["render",mn],["__file","index.vue"]]);const F=new Map,pn=e=>{let n=document.body;return e.appendTo&&(le(e.appendTo)&&(n=document.querySelector(e.appendTo)),he(e.appendTo)&&(n=e.appendTo),he(n)||(n=document.body)),n},gn=(e,n,a=null)=>{const l=L(vn,e,te(e.message)||Se(e.message)?{default:te(e.message)?e.message:()=>e.message}:null);return l.appContext=a,Te(l,n),pn(e).appendChild(n.firstElementChild),l.component},hn=()=>document.createElement("div"),yn=(e,n)=>{const a=hn();e.onVanish=()=>{Te(null,a),F.delete(t)},e.onAction=s=>{const i=F.get(t);let d;e.showInput?d={value:t.inputValue,action:s}:d=s,e.callback?e.callback(d,l.proxy):s==="cancel"||s==="close"?e.distinguishCancelAndClose&&s!=="cancel"?i.reject("close"):i.reject("cancel"):i.resolve(d)};const l=gn(e,a,n),t=l.proxy;for(const s in e)ge(e,s)&&!ge(t.$props,s)&&(s==="closeIcon"&&Ie(e[s])?t[s]=se(e[s]):t[s]=e[s]);return t.visible=!0,t};function V(e,n=null){if(!ke)return Promise.reject();let a;return le(e)||Se(e)?e={message:e}:a=e.callback,new Promise((l,t)=>{const s=yn(e,n??V._context);F.set(s,{options:e,callback:a,resolve:l,reject:t})})}const bn=["alert","confirm","prompt"],Cn={alert:{closeOnPressEscape:!1,closeOnClickModal:!1},confirm:{showCancelButton:!0},prompt:{showCancelButton:!0,showInput:!0}};bn.forEach(e=>{V[e]=En(e)});function En(e){return(n,a,l,t)=>{let s="";return Ie(a)?(l=a,s=""):Qe(a)?s="":s=a,V(Object.assign({title:s,message:n,type:"",...Cn[e]},l,{boxType:e}),t)}}V.close=()=>{F.forEach((e,n)=>{n.doClose()}),F.clear()};V._context=null;const S=V;S.install=e=>{S._context=e._context,e.config.globalProperties.$msgbox=S,e.config.globalProperties.$messageBox=S,e.config.globalProperties.$alert=S.alert,e.config.globalProperties.$confirm=S.confirm,e.config.globalProperties.$prompt=S.prompt};const Tn=S;export{Tn as E,un as a,ln as b,Le as c,cn as i,rn as u};
