<template>
  <div class="login_title">重置密码</div>
  <div v-if="!isSuccess">
    <div class="reset_desc">
      请输入新密码进行重新设置，
      <el-button
        type="primary"
        link
        style="font-size: 14px; color: #2b6bff; padding: 0; border: null"
        @click="handleToLogin"
      >
        返回登录
      </el-button>
    </div>
    <el-form ref="formRef" :model="formData" :rules="rules" status-icon>
      <el-form-item prop="mobile">
        <el-input
          v-model="formData.mobile"
          placeholder="请输入手机号码"
          class="input"
          :maxlength="11"
        />
      </el-form-item>
      <el-form-item prop="vcode" class="form_item">
        <el-input
          v-model="formData.vcode"
          placeholder="请输入验证码"
          class="input"
          :maxlength="6"
        />
        <div class="code_container">
          <div v-if="isSend">{{ count }}s</div>
          <div
            v-else
            :style="{
              color: formData.mobile.length === 11 ? '#2B6BFF' : '#999999',
            }"
            v-debounce="handleSendCode"
          >
            发送验证码
          </div>
        </div>
      </el-form-item>
      <el-form-item prop="newPassword1">
        <el-input
          v-model="formData.newPassword1"
          placeholder="请输入新密码"
          class="input"
          type="password"
          show-password
        />
      </el-form-item>
      <el-form-item prop="newPassword2">
        <el-input
          v-model="formData.newPassword2"
          placeholder="再次确认新密码"
          class="input"
          type="password"
          show-password
        />
      </el-form-item>
    </el-form>
    <el-button
      type="primary"
      class="submit_btn"
      v-debounce="handleSubmit"
      :loading="loading"
    >
      设置
    </el-button>
  </div>
  <div v-else>
    <div class="success_tip">设置成功!</div>
    <div class="success_tip">请保管好您的新密码，切勿泄露，</div>
    <div class="success_tip">
      点此
      <el-button
        type="primary"
        link
        style="font-size: 16px; color: #2b6bff"
        @click="handleToLogin"
      >
        登录
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from "vue-router";
import { reactive, ref } from "vue";
import { resetPwd, fetchPhoneCode } from "@/apis/loginApi";
import { ElMessage } from "element-plus";

const router = useRouter();

const rules = reactive({
  mobile: [
    { required: true, message: "请输入手机号", trigger: "blur" },
    {
      pattern: /^1[3-9]\d{9}$/,
      message: "请检查手机号码是否有误",
      trigger: "change",
    },
  ],
  newPassword1: [
    {
      required: true,
      message: "请输入新密码",
      trigger: "blur",
    },
  ],
  newPassword2: [
    {
      required: true,
      message: "请再次确认新密码",
      trigger: "blur",
    },
  ],
  vcode: [
    {
      required: true,
      message: "请输入验证码",
      trigger: "blur",
    },
  ],
});

const formRef = ref();

const isSend = ref(false);
const isSuccess = ref(false);
const count = ref(60);
const timer = ref(null);
const loading = ref(false);
const formData = reactive({
  mobile: "",
  vcode: "",
  newPassword1: "",
  newPassword2: "",
});

const handleToLogin = () => {
  router.push("/login/index");
};

//倒计时
const handleCountDown = () => {
  isSend.value = true;
  count.value = 60;

  timer.value = setInterval(() => {
    if (count.value === 0) {
      isSend.value = false;
      clearInterval(timer.value);
    } else {
      count.value--;
    }
  }, 1000);
};

const handleSendCode = async () => {
  if (/^1[3-9]\d{9}$/.test(formData.mobile)) {
    try {
      await fetchPhoneCode(formData.mobile);
      ElMessage({
        message: "发送成功！",
        type: "success",
      });
      handleCountDown();
    } catch {}
  }
};

const handleSubmit = () => {
  formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        loading.value = true;
        await resetPwd(formData);
        loading.value = false;
        isSuccess.value = true;
      } catch {
        loading.value = false;
      }
    }
  });
};
</script>

<style>
.reset_desc {
  font-size: 14px;
  color: #212121;
  line-height: 16px;
  margin: 24px 0 16px;
}
.success_tip {
  font-size: 18px;
  color: #999999;
  line-height: 21px;
}
.success_tip:nth-child(1) {
  margin-top: 40px;
}
</style>
