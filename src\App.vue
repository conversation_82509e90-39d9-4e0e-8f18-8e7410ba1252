<template>
  <router-view></router-view>
</template>

<script setup>
import { onMounted } from "vue";
import { useRouter } from "vue-router";
import { useUserStore } from "@/pinia/user";
import { ElLoading } from "element-plus";

const router = useRouter();
const userStore = useUserStore();

onMounted(() => {
  // 判断用户是否登录
  if (localStorage.getItem("token")) {
    userStore.getUserInfo().then((res) => {
      // router.replace(location.hash.split("#")[1]);
    });
  } else {
    router.replace("/login");
  }
});
</script>

<style>
	body {
	  margin: 0;
	  width: 100vw;
	  height: 100vh;
	  background: #f5f5f5;
	}
	.flex_box {
	  display: flex;
	  align-items: center;
	  justify-content: space-between;
	}
	.file-box {
		width:calc(100% - 5px);
		height:calc(100% - 3px);
	}
	
	/* WebKit/Blink 内核滚动条样式 */
	::-webkit-scrollbar {
	  width: 3px;
	  height: 3px;
	}
	
	::-webkit-scrollbar-thumb {
	  background-color: rgba(144, 147, 153, 0.5);
	  border-radius: 3px;
	  min-height: 20px;
	}
	
	::-webkit-scrollbar-thumb:hover {
	  background-color: rgba(144, 147, 153, 0.8);
	}
	
	/* ::-webkit-scrollbar-track {
	  background-color: transparent;
	}
	
	::-webkit-scrollbar-corner {
	  background-color: transparent;
	} */
</style>
