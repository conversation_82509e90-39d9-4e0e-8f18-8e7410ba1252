<template>
  <div class="flex_box header">
    <div class="flex_box">
      <img class="list_logo" src="@/assets/images/list_logo.png" />
      <div class="tabpanel">
        <router-link
          to="/home/<USER>"
          class="normalTab"
          active-class="selectTab"
        >
          项目设计
        </router-link>
        <router-link
          to="/home/<USER>"
          class="normalTab"
          active-class="selectTab"
        >
          数据设计
        </router-link>
        <router-link
          to="/home/<USER>"
          class="normalTab"
          active-class="selectTab"
        >
          项目发布
        </router-link>
        <router-link
          to="/home/<USER>"
          class="normalTab"
          active-class="selectTab"
        >
          组件市场
        </router-link>
      </div>
    </div>
    <div class="flex_box">
      <img class="avator" :src="userStore.userInfo.avatar || defaultAvatar" />
      <el-dropdown @command="handleCommand">
        <div style="cursor: pointer">
          <span class="name"> {{ userStore.userInfo.nickname }} </span>
          <el-icon color="#212121">
            <ArrowDownBold />
          </el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="user">
              <div class="custom_item">
                <img src="@/assets/images/pop_icon_geren.png" />
                个人中心
              </div>
            </el-dropdown-item>
            <el-dropdown-item command="update">
              <div class="custom_item">
                <img src="@/assets/images/pop_icon_xiugai.png" />
                修改密码
              </div>
            </el-dropdown-item>
            <el-dropdown-item command="exit">
              <div class="custom_item">
                <img src="@/assets/images/pop_icon_tuichu.png" />
                退出登录
              </div>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
  <div
    :class="
      noMarginPath.includes(route.path)
        ? 'container_nomargin'
        : 'container_margin'
    "
  >
    <router-view></router-view>
  </div>
</template>

<script setup>
import { ArrowDownBold } from "@element-plus/icons-vue";
import { useUserStore } from "@/pinia/user";
import { useRouter, useRoute } from "vue-router";
import { ElMessageBox } from "element-plus";
import defaultAvatar from "@/assets/images/avator_test.jpg";
import { logout } from "@/apis/loginApi";

const userStore = useUserStore();
const router = useRouter();
const route = useRoute();

const noMarginPath = ["/home/<USER>", "/home/<USER>"];

const handleLogout = () => {
  ElMessageBox.confirm("确定要退出登录吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      try {
        await logout();
        localStorage.clear();
        router.replace({
          path: "/login",
        });
      } catch {}
    })
    .catch(() => {});
};

const handleCommand = (e) => {
  if (e === "exit") {
    handleLogout();
  } else if (e === "update") {
    router.push("/login/resetPassword");
  }
};
</script>

<style lang="scss" scoped>
.header {
  height: 60px;
  background: #ffffff;
  padding: 0 24px;
  box-shadow: 0px 1px 0px 0px #e5e5e5;

  .list_logo {
    width: 40px;
    height: 40px;
    margin-right: 48px;
  }

  .normalTab {
    font-weight: 600;
    font-size: 18px;
    color: #212121;
    line-height: 24px;
    text-decoration: none;
    margin-right: 40px;
  }

  .selectTab {
    color: #2b6bff;
  }

  .avator {
    width: 32px;
    height: 32px;
    border-radius: 50%;
  }

  .name {
    font-weight: 600;
    font-size: 16px;
    color: #212121;
    line-height: 20px;
    margin: 0 4px 0 8px;
  }
}

.container_margin {
  height: calc(100vh - 92px);
  background: #ffffff;
  border-radius: 8px;
  margin: 16px;
}

.container_nomargin {
  height: calc(100vh - 60px);
}
.custom_item {
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #000000;
  line-height: 16px;

  img {
    width: 16px;
    height: 16px;
    margin-right: 8px;
  }
}
</style>
