<template>
  <img src="@/assets/images/icon_logo.png" class="logo" />
  <div class="container">
    <div class="welcome">
      <div>欢迎进入蜂喔低代码平台</div>
      <div>带您体验无限惊喜</div>
    </div>
    <div class="form">
      <router-view></router-view>
    </div>
  </div>
</template>

<style lang="scss">
.logo {
  width: 76px;
  height: 25px;
  position: fixed;
  top: 40px;
  left: 40px;
}
.container {
  min-width: 1440px;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #eff3fc;
  background-image: url(@/assets/images/login_bg_img.png);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  .welcome {
    font-weight: 600;
    font-size: 36px;
    color: #000000;
    line-height: 42px;
    letter-spacing: 7px;
    div:nth-child(2) {
      font-weight: 400;
      font-size: 18px;
      color: #666666;
      line-height: 21px;
      letter-spacing: 18px;
      margin-top: 24px;
    }
  }
  .form {
    box-sizing: border-box;
    width: 460px;
    height: 587px;
    background: #ffffff;
    border-radius: 16px;
    margin-left: 18vw;
    padding: 60px 50px;

    .login_title {
      font-weight: 600;
      font-size: 32px;
      color: #212121;
      line-height: 44px;
    }
    .tab {
      justify-content: flex-start;
      margin: 40px 0;
      .tab_item {
        font-weight: 600;
        font-size: 18px;
        color: #999999;
        line-height: 21px;
        margin-right: 40px;
        cursor: pointer;
      }
      .item_active {
        position: relative;
        color: #2b6bff;
      }
      .item_active::after {
        content: "";
        position: absolute;
        width: 36px;
        height: 2px;
        background: #2b6bff;
        left: 50%;
        transform: translateX(-50%);
        bottom: -6px;
      }
    }
    .input {
      height: 50px;
      //margin-bottom: 16px;
    }
    .read_forget {
      width: 100%;
      .read_text {
        display: flex;
        align-items: center;
        font-size: 14px;
        color: #000000;
        line-height: 16px;
        a {
          color: #2b6bff;
          text-decoration: none;
        }
        .el-checkbox {
          margin-right: 4px;
        }
      }
      .forget {
        font-size: 14px;
        color: #666666;
        line-height: 16px;
        cursor: pointer;
      }
    }
    .submit_btn {
      width: 360px;
      height: 50px;
      margin: 40px 0 16px;
      font-weight: 600;
      font-size: 18px;
      color: #ffffff;
      line-height: 21px;
    }
    .other_account {
      font-size: 14px;
      color: #666666;
      line-height: 16px;
      img {
        width: 40px;
        height: 40px;
        cursor: pointer;
      }
      img:nth-child(2) {
        margin: 0 8px 0 12px;
      }
    }
    .register {
      font-size: 14px;
      color: #2b6bff;
      line-height: 16px;
      cursor: pointer;
    }
    .form_item {
      position: relative;
      .code_container {
        width: 100px;
        height: 48px;
        position: absolute;
        top: 1px;
        right: 2px;
        font-size: 14px;
        color: #2b6bff;
        line-height: 48px;
        text-align: center;
        cursor: pointer;
        background-color: #fff;
      }
    }
  }
}
</style>
