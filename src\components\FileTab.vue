<template>
	<div class="flex_box tab-box">
		<div style="cursor: pointer;position: absolute;top:5px;left:0px;" 
		     @click="onToLeft()" v-show="isShowLeft()" >
			<el-icon :size="14"> <DArrowLeft /> </el-icon>
		</div>
		<div class="tab-plan" ref="xLineScrollBar" >
			<div class="tab-plan-scroll" ref="xScrollPlan" >
				<el-tag v-for="(tag,index) in props.dataList" :key="tag.fileItem.filePath" class="mx-1" effect="dark" closable 
				    @close="onClosePage(tag.fileItem.filePath,index)" @click.stop = "onSelectTab(tag.fileItem.filePath)" 
					:type="props.currentFilePath == tag.fileItem.filePath ? 'warning' : 'info'" >
					{{tag.isModify ?  (tag.fileItem.title + ' *') : tag.fileItem.title }}
				</el-tag>
			</div>
		 </div>
		 <div style="cursor: pointer;position: absolute;top:5px;right:0px;" 
		      @click="onToRight()" v-show="isShowRight()">
			 <el-icon :size="14"> <DArrowRight /> </el-icon>
		 </div>
	</div>
</template>

<script lang="ts" setup>
	import { reactive,onMounted,ref, nextTick,inject,computed} from 'vue';
	import { ElMessage } from "element-plus";
	import { customMessageBox } from "../common/utils.js";
	
	const emits = defineEmits(['select-tab'])
	const props = defineProps({
		dataList:{
			type:Array,
			default:[]
		},
		currentFilePath:{
			type:String,
			default:""
		}
	})
	
	const ProjectRef = inject('ProjectRef')
	const OpenFileMgr = inject('OpenFileMgr')
	
	let scrollCurrentPos = ref(0)
	let scrollEnd = ref(1000)
	const xLineScrollBar = ref<HTMLElement>()
	const xScrollPlan = ref<HTMLElement>()
	
	onMounted(()=>{
		scrollEnd.value = xLineScrollBar.value.scrollWidth
		onToLeft()
		ProjectRef.funcObj.otherFunc.onClosePage = onClosePage
		
	})

	function isShowLeft(){
		return props.dataList && props.dataList.length > 0 && scrollCurrentPos.value > 0 
	}
	
	function isShowRight(){
		return props.dataList && props.dataList.length > 0 && scrollCurrentPos.value < scrollEnd.value 
	}
	
	function onClosePage(path:string,index:number){
		if(props.dataList && props.dataList.length > 0){
			if(props.dataList[index].isModify){
				customMessageBox("是否何存修改内容?")
				  .then(() => {
					  ProjectRef.funcObj.saveFileData(()=>{
						  doClosePage(path,index)
					  })
				  })
				  .catch(() => {
				     doClosePage(path,index)
				  });
				
			}else{
				doClosePage(path,index)
			}
		}
	}
	
	function doClosePage(path:string,index:number){
		if(props.dataList && props.dataList.length > 0){
			props.dataList.splice(index,1)
			nextTick(()=>{
				if(props.currentFilePath == path && props.dataList.length > 0){
					if(index < props.dataList.length){
						onSelectTab(props.dataList[index].fileItem.filePath)
					}else if(index > 0 && index - 1 < props.dataList.length){
						onSelectTab(props.dataList[index - 1].fileItem.filePath)
					}
				}
			})
		}
	}
	
	function onSelectTab(path:string){
		OpenFileMgr.setCurrentPath(path)
	}
	
	function onToLeft(){
		if (xLineScrollBar.value) {
		  if(scrollCurrentPos.value > 0){
			  if(scrollCurrentPos.value > 200){
				  scrollCurrentPos.value = scrollCurrentPos.value - 200
			  }else{
				  scrollCurrentPos.value = 0
			  }
			  xLineScrollBar.value.scrollTo({
				  left:scrollCurrentPos.value,
				  behavior:'smooth',
			  })
		  }
		}
	}
	function onToRight(){
		if (xLineScrollBar.value) {
		  if(scrollCurrentPos.value < scrollEnd.value){
			  if(scrollCurrentPos.value < scrollEnd.value - 200){
				  scrollCurrentPos.value = scrollCurrentPos.value + 200
			  }else{
				  scrollCurrentPos.value = scrollEnd.value
			  }
			  xLineScrollBar.value.scrollTo({
				  left:scrollCurrentPos.value,
				  behavior:'smooth',
			  })
		  }
		}
	}
</script>

<style lang="scss" scoped>
	.tab-box{
		  margin-left:10px;
		  flex:1;
		  height:32px;
		  overflow: hidden;
		  position: relative;
		  
		  .tab-plan {
			  // border:1px solid red;
			  flex:1;
			  overflow-x: scroll;
			  white-space: nowrap;
			  height:60px;
			  position: absolute;
			  left:20px;
			  right:20px;
			  top:0px;
			  
			  .tab-plan-scroll {
				  display: flex;
				  justify-content: start;
				  align-items: start;
				  scroll-snap-type: x mandatory;
			  }
		  }
	}
	:deep(.el-tag) {
		margin-right:2px;
		border-radius: 2px;
		cursor: pointer;
		height:30px;
		font-size: 12px;
		scroll-snap-align: start;
		flex-shrink: 0;
		user-select: none;
	}
</style>