const debounce = {
  mounted(el, binding) {
    const { value, arg } = binding;
    let delay = 500; // 默认延迟时间

    if (arg) {
      delay = parseInt(arg);
    }

    if (typeof value !== "function") {
      console.warn("v-debounce expects a function");
      return;
    }

    let debounceTimer = null;
    el._debounceCallback = value;

    el.addEventListener("click", () => {
      clearTimeout(debounceTimer);
      debounceTimer = setTimeout(() => {
        el._debounceCallback();
      }, delay);
    });
  },
  unmounted(el) {
    el.removeEventListener("click", el._debounceCallback);
    delete el._debounceCallback;
  },
};

export default debounce;
