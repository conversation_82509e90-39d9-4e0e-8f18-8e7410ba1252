import axios from "axios";
import { ElMessage } from "element-plus";
import router from "../router/router";

const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;

const overCodes = [6501]

// 创建 Axios 实例
const http = axios.create({
  baseURL: apiBaseUrl,
  timeout: 5000, // 请求超时时间
  headers: {
    "Content-Type": "application/json",
  },
});

// 请求拦截器
http.interceptors.request.use(
  (config) => {
    // 在发送请求之前做些什么，例如添加 token
    const token = localStorage.getItem("token");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // if (config.url.indexOf("/yhyApi/") >= 0) {
    //   config.baseURL = "";
    // }
    return config;
  },
  (error) => {
    // 对请求错误做些什么
    return Promise.reject(error);
  }
);

// 响应拦截器
http.interceptors.response.use(
  (response) => {
    if (response.data.code === 200) {
      return Promise.resolve(response.data);
    } else {
      if (response.data.code === 1002) {
        // token 失效
        localStorage.clear();
        router.replace("/login");
	  }else if(overCodes.includes(response.data.code)){
		  return Promise.resolve(response.data);
      } else {
        // 弹出message
        ElMessage.error(response.data.message);
      }
      return Promise.reject(response.data);
    }
  },
  (error) => {
    // 对响应错误做些什么
    ElMessage.error(error.response ? error.response.data.message : "请求失败");
    return Promise.reject(error);
  }
);

export default http;
