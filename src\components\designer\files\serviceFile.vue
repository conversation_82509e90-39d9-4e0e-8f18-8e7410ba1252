<template>
	<div class="content file-box">
		<div class="title-box">
			<el-icon :size="18"> <Timer /></el-icon>
			<div style="margin-left:10px;">创建一个定时执行的任务</div>
		</div>
		<div class="service-content" :style="{height: 'calc(100vh - 140px - ' + layoutParam.bottomTabHeight+'px)'}">
			<div class="service-row">
				<div class="label-column">服务执行周期:</div>
				<el-select
				      v-model="currentService.cycleValue"
				      placeholder="Select"
				      size="large"
				      style="width: 200px"
				    >
				      <el-option key="year" label="以年为周期(year)" value="year" />
					  <el-option key="month" label="以月为周期(month)" value="month" />
					  <el-option key="day" label="以天为周期(day)" value="day" />
					  <el-option key="hour" label="以小时为周期(hour)" value="hour" />
					  <el-option key="time" label="指定一次执行(time)" value="time" />
				    </el-select>
					
					<div style="margin-left:40px;" v-if="currentService.cycleValue != 'time'">按每 </div>
					<el-input v-model="currentService.cycleQty" placeholder="隔长" type="number" v-if="currentService.cycleValue != 'time'"
					    style="margin:0px 10px;width:60px;"/>
					<div v-if="currentService.cycleValue != 'time'">
					  {{currentService.cycleValue=='year' ? '年' : (currentService.cycleValue=='month' ? '月' : (currentService.cycleValue=='day' ? '天' : '小时'))}} 间隔执行
					</div>
					<div style="margin-left:40px;margin-right:20px;">指定开始执行时间:</div>
					<el-date-picker v-model="currentService.runTime" type="datetime" placeholder="请选择执行时间点"/>
			</div>
			<div class="service-row" v-if="currentService.cycleValue != 'time'">
				<div class="label-column">不包含时间有:</div>
				<el-switch v-model="currentService.week0" inline-prompt active-text="周日" inactive-text="周日" style="margin: 3px 8px;"/>
				<el-switch v-model="currentService.week1" inline-prompt active-text="周一" inactive-text="周一" style="margin: 3px 8px;"/>
				<el-switch v-model="currentService.week2" inline-prompt active-text="周二" inactive-text="周二" style="margin: 3px 8px;"/>
				<el-switch v-model="currentService.week3" inline-prompt active-text="周三" inactive-text="周三" style="margin: 3px 8px;"/>
				<el-switch v-model="currentService.week4" inline-prompt active-text="周四" inactive-text="周四" style="margin: 3px 8px;"/>
				<el-switch v-model="currentService.week5" inline-prompt active-text="周五" inactive-text="周五" style="margin: 3px 8px;"/>
				<el-switch v-model="currentService.week6" inline-prompt active-text="周六" inactive-text="周六" style="margin: 3px 8px;"/>
			</div>
			<div class="service-row" @dragover="onDragOver" @drop="onFunDrop(0,$event)">
				<div class="label-column">执行函数文件:</div>
				<el-input v-model="currentService.filePath" placeholder="输入后台函数文件路径"  style="margin:0px 10px;flex:1;" clearable/>
			</div>
			<div style="margin-left:160px;font-size: 12px;color:#a1a1a1;text-align: left;">* 可从左边函数文件列表中拖拽目标文件到上面输入框中</div>
			<div class="service-row" >
				<div class="label-column">是否关闭执行:</div>
				<el-switch v-model="currentService.closeable" inline-prompt active-text="开启" inactive-text="关闭" style="margin: 3px 8px;" size="large"/>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
	import {reactive,ref,onMounted,inject,toRaw,watch, nextTick} from 'vue'
	import { useFileBase } from '../../../common/fileBasic';
	import {apiLoadFileJson,apiUpdateFile} from '../../../apis/designer.js'
	import {  ElMessage, ElMessageBox } from "element-plus";
	const fileBase = useFileBase()
	const props = defineProps({
		fileItem:{
			type:Object,
			default:{}
		}
	})
	const layoutParam = inject("layoutParam")
	const ProjectRef = inject("ProjectRef")
	
	const currentVersion = ref(0)
	
	const currentService = reactive({
		 fileId:0,
		 cycleValue:"day", //year,month,day,hour
		 cycleQty:1,
		 runTime:'',
		 week0:false,
		 week1:false,
		 week2:false,
		 week3:false,
		 week4:false,
		 week5:false,
		 week6:false,
		 filePath:"",
		 closeable:true,
	})
	
	watch(currentService,(newValue)=>{
		props.fileItem.isModify = true
	},{deep:true})
	
	onMounted(()=>{
	     props.fileItem.func.saveData = saveData
		 loadFileJson()
	})
	
	
	function saveData(success){
		let json = toRaw(currentService)
		updateFile(props.fileItem.fileItem.filePath,json,(data)=>{
			ElMessage({
				message:"保存成功",
				type:"success"
			})
			props.fileItem.isModify = false
			if(success) success()
		})
	}
	
	function onDragOver(e){
		e.preventDefault();
	}
	
	function onFunDrop(index,e){
		const res = e.dataTransfer.getData("treeItem")
		if(res.length > 0){
			let pathArray = res.split(".")
			if(pathArray[0]=='methrod'){
				currentService.filePath = res
			}
		}
	}
	
	async function updateFile(path,json,success){
		let res = await apiUpdateFile({
			projectId:ProjectRef.BasicData.ProjectId,
			version:currentVersion.value,
			path:path,
			_json:json
		})
		if(res.code==200){
			if(res.data.hasOwnProperty("success") && res.data.success==false){
				ElMessage({
					message:res.message,
					type:"error"
				})
			}else{
				currentVersion.value++
			  if(success) success(res)
			}
		}
	}
	
	async function loadFileJson(){
		let param = {
			projectId:ProjectRef.BasicData.ProjectId,
			path:props.fileItem.fileItem.filePath
		}
		let res = await apiLoadFileJson(param)
		if(res.code == 200){
			if(res.data.content){
				currentVersion.value = res.data.version
				let tmpJson = JSON.parse(res.data.content)
				console.log("tmpJson.cycleQty",tmpJson)
				if(tmpJson){
					currentService.fileId = tmpJson.fileId
					currentService.filePath = tmpJson.filePath
					currentService.cycleValue = tmpJson.cycleValue
					currentService.cycleQty = tmpJson.cycleQty
					currentService.runTime = tmpJson.runTime
					currentService.week0 = tmpJson.week0
					currentService.week1 = tmpJson.week1
					currentService.week2 = tmpJson.week2
					currentService.week3 = tmpJson.week3
					currentService.week4 = tmpJson.week4
					currentService.week5 = tmpJson.week5
					currentService.week6 = tmpJson.week6
					currentService.closeable = tmpJson.closeable
					nextTick(()=>{
						 props.fileItem.isModify = false
					})
				}
			}else{
				currentVersion.value = res.data.version
			}
			
		}
	}
	
	// async function loadFileJson(){
	// 	let param = {
	// 		projectId:ProjectRef.BasicData.ProjectId,
	// 		filePath:props.fileItem.fileItem.filePath
	// 	}
	// 	let res = await apiLoadFileJson(param)
	// 	if(res.code == 200){
	// 		currentService.fileId = props.fileItem.content.fileId 
	// 		currentService.filePath = props.fileItem.content.filePath
	// 		currentService.cycleValue = props.fileItem.content.cycleValue
	// 		currentService.cycleQty = props.fileItem.content.cycleQty
	// 		currentService.runTime = props.fileItem.content.cycleQty
	// 		currentService.week0 = props.fileItem.content.week0
	// 		currentService.week1 = props.fileItem.content.week1
	// 		currentService.week2 = props.fileItem.content.week2
	// 		currentService.week3 = props.fileItem.content.week3
	// 		currentService.week4 = props.fileItem.content.week4
	// 		currentService.week5 = props.fileItem.content.week5
	// 		currentService.week6 = props.fileItem.content.week6
	// 		currentService.closeable = props.fileItem.content.closeable
	// 	}
	// }
</script>

<style lang="scss" scoped>
	 .content {
		 overflow: hidden;
		 white-space: nowrap;
		 
		 .title-box{
			 display: flex;
			 justify-content: flex-start;
			 align-items: center;
			 background-color: white;
			 padding: 8px 20px;
			 font-size: 14px;
			 border-bottom: 1px solid #e7e7e7;
			 
		 }
		 
		 .service-content{
			 // height:100%;
			 background-color: white;
			 overflow-y:auto;
			 padding-top:30px;
			 
			 .service-row{
				 display: flex;
				 justify-content: flex-start;
				 align-items: center;
				 padding:15px 30px;
				 
				 
				 .label-column{
					 width:120px;
					 // border:1px solid red;
					 text-align: left;
				 }
			 }
		 }
	 }
</style>