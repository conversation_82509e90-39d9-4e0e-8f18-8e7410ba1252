import { toRaw } from 'vue';
import {xSysFunc} from '../frame/common.js'
import {runServerJson } from '../axios/dao/api.js'
class Interrupt extends Error {
	constructor(category, result) {
		super("");
		this.category = category
		this.result = result;
	}
}
	
export const scpt = {
	
	isKeywords(k){
		let kwds = [ 'for','{','}','if','else','elseif','let','var','const','list','array',
				 'int','integer','long','string','bool','boolean','object','float','double',',','&&','||',
				 '=','==','>','>=','<=','<','(',')',':','*','-','+','/','true','false','call','error',
				  '定义','令','如果','假如','或者如果','或者假如','字符串','整数','实数','逻辑值','布尔值',
				  '数组','列表','对象','循环','返回','中断','退出','终止','跳转下次循环','退出循环','终止循环',
				  '中断循环','等于','是','赋值','设','调用','错误']
				 
		if(kwds.includes(k)) return true
		if(k[0]=='@' && k[k.length - 1]=='@') return true 
		if(k[0]=="'" && k[k.length - 1]=="'") return true 
		// if(k[0]=="[" && k[k.length - 1]=="]") return true 
		if(k[0]==':') return true 
		if(xSysFunc.isNumeric(k)) return true
		
		return false
	},
	
	getNewContext(parentContext){
		parentContext.currentRow++
		return {
			floor: parentContext.floor+1,
			PageRef:parentContext.PageRef,
			blockMap:parentContext.blockMap,
			strMap:parentContext.strMap,
			param:parentContext.param,
			doServer:parentContext.doServer,
			parent:parentContext,
			forBody:parentContext.forBody,
			localVar:{},
			scriptList:[],
			currentRow:0,
			reverseRun:false
		}
	},
	
	parse(context,scriptContent){ //解释脚本的入口函数
		xSysFunc.sumFuncTime("scptParse")
		// .replaceAll('"',"'") 将脚本里面的双引号统一转为单引号，兼容 单双引号混写脚本 modify by Jason He 7.16
		let nScript = this.replaceNoteScript(scriptContent.trim());
		nScript = nScript.trim().replaceAll('\n',' ').replaceAll('\t',' ').replaceAll('"',"'")
		
		nScript = this.doBlack(nScript,"'","'",context.strMap,"@@","@@","",context.floor) //提取字符串
		nScript = this.doBlack(nScript,"{","}",context.blockMap,"@#","#@",";",context.floor) //提取脚本块
		nScript = this.optimizeArray(nScript.replaceAll(',',' , '))
		context.scriptList = this.putScriptToArray(nScript) //把脚本分解成语句数组
		
		this.runScript(context)
	},
	runScript(context){ //执行脚本指令集
	    xSysFunc.sumFuncTime("scptRunScript")
		if(context.scriptList.length > 0 && context.currentRow < context.scriptList.length){
			let ifRun = false 
			for(let row = context.currentRow;row<context.scriptList.length;row++){
				context.currentRow = row
				let statement = context.scriptList[row]
				if(statement.trim() == '') continue;
				let stateArray = this.clearNullArray(statement.trim().split(' '))
				
				if(stateArray[0] == 'let' || stateArray[0] == 'var' || stateArray[0] == 'const'
				     || stateArray[0] == '定义' || stateArray[0] == '令' || stateArray[0] == '设'){
					  stateArray[0] = 'let'	 
					this.doDefineVar(context,stateArray)
					
				}else if(stateArray[0] == 'if' || stateArray[0] == '如果' || stateArray[0] == '假如' ){
					stateArray[0] = 'if'
					if(stateArray[1] != '(' && stateArray[stateArray.length - 2] != ')'){
						stateArray.splice(1,0,'(')
						stateArray.splice(stateArray.length - 1,0,')')
					}
					ifRun = this.doIf(context,stateArray)
					
				}else if(stateArray[0] == 'elseif' || stateArray[0] == '或者如果'  || stateArray[0] == '或者假如' ){
					stateArray[0] = 'elseif'
					if(stateArray[1] != '(' && stateArray[stateArray.length - 2] != ')'){
						stateArray.splice(1,0,'(')
						stateArray.splice(stateArray.length - 1,0,')')
					}
					if(row - 1 >=0){
						let stateArray1 = this.clearNullArray(context.scriptList[row - 1].trim().split(' '))
						if(stateArray1[0]=='if' && stateArray1[1]=='('){
							if(ifRun) continue
							ifRun = this.doIf(context,stateArray)
							continue
						}
					}
					throw new Error(statement + " 语法错误")
				}else if(stateArray[0] == 'else' || stateArray[0] == '那么'){
					if(row - 1 >=0){
						let stateArray1 = this.clearNullArray(context.scriptList[row - 1].trim().split(' '))
						if((stateArray1[0]=='if' || stateArray1[0]=='elseif') && stateArray1[1]=='('){
							if(ifRun) continue
							let stateArray2 = ['else','(','true',')',...stateArray.slice(1)]
							this.doIf(context,stateArray2)
							ifRun = false
							continue
						}
					}
					throw new Error(statement + " 语法错误")
				}else if(stateArray[0] == 'for' || stateArray[0] == '循环'){
					stateArray[0] = 'for'
					if(stateArray[1] != '(' && stateArray[stateArray.length - 2] != ')'){
						stateArray.splice(1,0,'(')
						stateArray.splice(stateArray.length - 1,0,')')
					}
					this.doFor(context,stateArray)
					
				}else if((stateArray.length > 2 && (stateArray[1]== '=' || stateArray[1]== '等于' || stateArray[1]== '是' || stateArray[1]== '赋值') )
				|| (stateArray[0] == 'set' && stateArray.length > 3 && ( stateArray[2]== '=' || stateArray[2]== '等于' || stateArray[2]== '是' || stateArray[2]== '赋值' ))){
					if(stateArray[0] == 'set') stateArray = stateArray.slice(1)
					stateArray[1]= '='
					this.doAssign(context,stateArray)
					
				}else if(stateArray[0].substring(0,1)==':' && stateArray[0].trim().length > 1){
					if(stateArray[1] != '(' && stateArray[stateArray.length - 1] != ')'){
						stateArray.splice(1,0,'(')
						stateArray.splice(stateArray.length ,0,')')
					}
					this.doFunc(context,stateArray)
				}else if(stateArray[0]=='call' || stateArray[0]=='调用'){
					if(stateArray[1] != '(' && stateArray[stateArray.length - 2] != ')'){
						stateArray.splice(1,0,'(')
						stateArray.splice(stateArray.length - 1,0,')')
					}
					let errorArray = []
					let nRow = row + 1
					if(nRow < context.scriptList.length){
						let tmpStatement = context.scriptList[nRow]
						if(tmpStatement.trim().length > 0){
							tmpStatement = this.clearNullArray(tmpStatement.trim().split(' '))
						}
						if(tmpStatement[0]=='error' || tmpStatement[0]=='错误'){
							if(tmpStatement[1] != '(' && tmpStatement[tmpStatement.length - 2] != ')'){
								tmpStatement.splice(1,0,'(')
								tmpStatement.splice(tmpStatement.length - 1,0,')')
							}
							errorArray = tmpStatement
						}
					}
					this.doCall(context,stateArray,errorArray)
					break;
				}else if(stateArray[0]=='error' || stateArray[0]=='错误'){
					if(row - 1 >=0){
						let stateArray1 = this.clearNullArray(context.scriptList[row - 1].trim().split(' '))
						if(stateArray1[0]=='call' || stateArray1[0]=='调用'){
							continue
						}
					}
					throw new Error(statement + " 语法错误")
					
				}else if(stateArray[0] == 'return' || stateArray[0] == '返回' || stateArray[0] == '退出' 
				      || stateArray[0] == '终止' || stateArray[0] == '中断'){
						  stateArray[0] = 'return'
					this.doReturn(context,stateArray)
					
				}else if(stateArray[0] == 'continue' || stateArray[0] == '跳转下次循环' ){
					stateArray[0] = 'continue'
					if(context.hasOwnProperty('forBody') && context['forBody']){
						throw new Interrupt('continue',{})
					}
				}else if(stateArray[0]=='toPage' || stateArray[0]=='跳转页面'){
					stateArray[0] = 'toPage'
					if(stateArray[1] != '(' && stateArray[stateArray.length - 1] != ')'){
						stateArray.splice(1,0,'(')
						stateArray.splice(stateArray.length,0,')')
					}
					this.doOpenPage(context,stateArray)
				}else if(stateArray[0] == 'break' || stateArray[0] == '退出循环' || stateArray[0] == '终止循环' 
				         || stateArray[0] == '中断循环'){
					stateArray[0] = 'break'
					if(context.hasOwnProperty('forBody') && context['forBody']){
						throw new Interrupt('break',{})
					}
				
				}else if(stateArray[0].substring(0,1)=='call' && stateArray[0].trim().length > 1){
					if(stateArray[1] != '(' && stateArray[stateArray.length - 1] != ')'){
						
						//Todo
					}
				}else{
					throw new Error(statement+" 此语法解释错误")
				}
			}
		}
		if(context.parent != null && context.scriptList.length > 0  && context.reverseRun && context.floor > 1){
			console.log('执行了反向运行')
			context.parent.reverseRun = context.reverseRun
			this.runScript(context.parent)
		}
	},
	doOpenPage(context,stateArray){
		let pmArr = stateArray.slice(2,-1).join('').split(',')
		pmArr[0] = xSysFunc.doBackStr(pmArr[0],context.strMap)
		if(typeof pmArr[1] == 'string'){
			if(xSysFunc.checkStrVar(pmArr[1])){
				pmArr[1] = xSysFunc.doBackStr(pmArr[1],context.strMap)
			}else{
				pmArr[1] = this.getVar(context,pmArr[1]).value
			}
		}
		let param = {}
		if(xSysFunc.isNumeric(pmArr[0])){
			param.fileId = pmArr[0]
		}else{
			param.path = pmArr[0]
		}
		if(typeof pmArr[1] == 'object' && pmArr[1].size > 0 ){
			param.param =JSON.stringify(pmArr[1])
		}
        context.PageRef.openPage(param)
	},
	async callServerFun(xParam,callback){
		let res = await runServerJson(xParam)
		if(callback){
			callback(res)
		}
	},
	doCall(context,stateArray,errorArray){
		let funParam = stateArray.slice(2,-2).join('').split(',')
		let funName = xSysFunc.doBackStr(funParam[0],context.strMap)
		let param  = this.getVar(context,funParam[1])
		let xParam = {
			path:funName,
			param:param.value
		}

		this.callServerFun(xParam,(res)=>{
			let newContext = this.getNewContext(context)
			newContext.reverseRun = true
			if(res.statusCode==200){
				if(res.data.code == 200){
					let nScript = context.blockMap.get(stateArray.slice(-1)[0])
					newContext.localVar[funParam[2]] = {type:'object',value:res.data.data}
					this.parse(newContext,nScript)
				}else{
					if(errorArray && errorArray.length > 0){
						let nScript = context.blockMap.get(errorArray.slice(-1)[0])
						newContext.localVar[errorArray[2]] = {type:'object',value:res.data}
						this.parse(newContext,nScript)
					}else{
						context.reverseRun = true
						this.runScript(context)
					}
				}
			}else{
				xSysFunc.showDialog(res.errMsg,funName+" 函数调用","确定","")
			}
		})
		throw new Interrupt('callBack',{})
	},
	doFor(context,stateArray){
		xSysFunc.sumFuncTime("scptDoFor")
		let ments = stateArray.slice(2,-2).join('').split(',')
		if(ments.length == 3){
			let mp = this.getAllVarMap(context)
			mp = this.mergeMaps(mp,context.strMap)
			//处理变量定义
			let sf = ments[0].split('=')
			let indexName = ""
			let startValue = 0
			let maxValue = 0
			let stepN = 1
			if(sf.length==2 && !xSysFunc.isNumeric(sf[0])){
				indexName = sf[0]
				let tmpStart = context.doServer.xxCalc(sf[1],mp)
				if(typeof tmpStart=='string' && xSysFunc.isNumeric(tmpStart)){
					startValue = parseInt(tmpStart)
				}else if(typeof tmpStart=='number'){
					startValue = tmpStart
				}else{
					throw Error("for 中 "+ments[0]+",...,... 初始值必须是数字")
				}
			}else{
				throw Error("for 中 "+ments[0]+",...,... 格式无法识别")
			}

			//处理条件判断
			mp.set(indexName,startValue);
			let maxRes = context.doServer.xxCalc(ments[1],mp)
			// if(!xSysFunc.isNumeric(maxRes) && mp.has(maxRes)){
			// 	maxRes = mp.get(maxRes)
			// }
			let isBoolCheck = false;
			if(typeof maxRes == 'number'){
				maxValue = maxRes
			}else if(typeof maxRes=='string' && xSysFunc.isNumeric(maxRes)){
				maxValue = parseInt(maxRes)
			}else if (typeof maxRes == 'boolean'){
				maxValue = 50000;
				isBoolCheck = true;
			}else{
				throw Error("for 中 ...,"+ments[1]+",... 格式无法识别")
			}
			
			let stepVar = context.doServer.xxCalc(ments[2],mp)
			if(typeof stepVar == 'number'){
				stepN = stepVar
			}else if(typeof stepVar=='string' && xSysFunc.isNumeric(stepVar)){
				stepN = parseInt(stepVar)	
			} else {
				throw Error("for 中...,... "+ments[2]+" 格式无法识别")
			}
			if(stepVar == 0){
				throw Error("for 中...,... "+ments[2]+" 步长不能为0")
			}
			
			let sptKey = stateArray[stateArray.length - 1]
			if(xSysFunc.checkObj(sptKey) && context.blockMap.has(sptKey)){
				let spt = context.blockMap.get(sptKey)
				
				let leftNum = startValue
				let rightNum = maxValue
				// if(stepN < 0){
				// 	leftNum = maxValue
				// 	rightNum = startValue
				// }
				let xTime = 1
				
				for(let forIndex=startValue;leftNum < rightNum;forIndex=forIndex + stepN){
					if (isBoolCheck){
						mp.set(indexName,forIndex);
						maxRes = context.doServer.xxCalc(ments[1],mp);
						if(!maxRes) break;
					}
					// if(stepN > 0){
					leftNum = forIndex
					// }
					// else{
					// 	rightNum = forIndex
					// }
					if(xTime > 100000){
						throw Error("for 单次循环超过10万次，系统保护机制强制退出")
					}else{
						xTime ++
					}
					
					let lineContext = this.getNewContext(context)
					lineContext.forBody = true
					lineContext.localVar[indexName] = {type:'number',value:forIndex}
					
					try{
						
						this.parse(lineContext,spt)
						
					}catch(error){
						if(error.hasOwnProperty('category') && error['category']=='continue'){
							continue
						}else if(error.hasOwnProperty('category') && error['category']=='break'){
							break;
						}else{
							throw error
						}
					}
				}
				
			}
			
			
			//处理变量增加
			
			
		}else{
			throw Error("for ("+stateArray.slice(2,-2).join('')+") 条件格式无法识别")
		}
	},
	doIf(context,stateArray){
		xSysFunc.sumFuncTime("scptDoIf")
		let condArray = this.solveVar(context,stateArray.slice(2,-2))
		let condition = xSysFunc.doBackStr(condArray.join(' '), context.strMap) 
		let blockKey = stateArray.slice(-1).join('')
		if(blockKey.trim().slice(0,2)=='@#' && blockKey.trim().slice(-2)=='#@'){
			let nMp = this.getAllVarMap(context)
			let result = context.doServer.xxCalc(condition,nMp)
			if((typeof result=='boolean' && result ) || (typeof result=='string' && result == 'true')){
				if(context.blockMap.has(blockKey)){
					let blockScript = context.blockMap.get(blockKey)
					let nContext = this.getNewContext(context)
					this.parse(nContext,blockScript)
					return true
				}
			}
		}
		return false
	},
	doReturn(context,stateArray){
		xSysFunc.sumFuncTime("scptDoReturn")
		let res = stateArray.length > 1 ? this.doExpression(context,stateArray.slice(1)) : {};
		if(typeof res == 'string' && res.length > 0){
			let mp = this.getAllVarMap(context)
			mp = this.mergeMaps(mp,context.strMap)
			res = xSysFunc.doBackObj(res,mp)
			if(typeof res == 'object'){
				for(let col in res){
					res[col] = xSysFunc.doBackStr(res[col],mp)
				}
			}
		}
		throw new Interrupt('return',res)
	},
	doAssign(context,stateArray){
		xSysFunc.sumFuncTime("scptDoAssign")
		let exps = {}
		if(xSysFunc.checkObj(stateArray.slice(2).join(''))){
			exps = xSysFunc.doBackObj(stateArray.slice(2).join(''),context.blockMap)
		}else{
			// let exArray = stateArray.slice(2)
			exps = this.doExpression(context,stateArray.slice(2))
		}

		let mp = this.getAllVarMap(context)
		mp = this.mergeMaps(mp,context.strMap)
		if (exps) {
			if(typeof exps=='string' && exps!= '' && exps.slice(0,5) == '[obj:'){
				 exps = xSysFunc.doBackObj(exps,mp)
			}else{
				 exps = xSysFunc.doBackStr(exps,mp)
			}
		}
		if(stateArray[0].slice(0,4)=='ref:'){
			context.doServer.xSetValue(stateArray[0],exps)
		}else{
			this.setVar(context,stateArray[0],exps)
		}
	},
	doFunc(context,stateArray){
		xSysFunc.sumFuncTime("scptDoFunc")
		let mp = this.getAllVarMap(context)
		mp = this.mergeMaps(mp,context.strMap)
		let result = this.doExpression(context,stateArray.slice(2,stateArray.length - 1))
		if (typeof result == 'object') {
			mp.set('#data#',result);
			result = stateArray[0]+"([obj:#data#])"
		} else {
			result = stateArray[0]+"("+result+")"
		}
		return xSysFunc.matchMeth(result,mp)
	},
	doExpression(context,stateArray){
		xSysFunc.sumFuncTime("scptDoExpresion")
		let pms = this.solveVar(context,stateArray)
		let res = pms.join('')
		let mp = this.getAllVarMap(context)
		mp = this.mergeMaps(mp,context.strMap)
		return context.doServer.xxCalc(res,mp)
		// return xSysFunc.doBackStr(res,mp)
		
	},
	doDefineVar(context,stateArray){
		xSysFunc.sumFuncTime("scptDoDefineVar")
		if(stateArray.length >= 3){
			let mp = this.getAllVarMap(context)
			if(mp.has(stateArray[2])){
				throw new Error(stateArray.slice(0,3).join(' ')+' 与参数 '+ stateArray[2] + ' 重名')
			}
			
			if(stateArray[1]=='string' || stateArray[1]=='字符串'){
				stateArray[1]='string'
				context.localVar[stateArray[2]] = {type:'string',value:''}

			}else if(stateArray[1]=='int' || stateArray[1]=='integer' || stateArray[1]=='Integer' ||
			          stateArray[1]=='long' || stateArray[1]=='Long' || stateArray[1]=='number' ||
					  stateArray[1]=='整数'){
					  stateArray[1] = 'number'  
				context.localVar[stateArray[2]] = {type:'number',value:0}
				
			}else if(stateArray[1]=='float' || stateArray[1]=='double' || stateArray[1]=='decimal'
			         || stateArray[1]=='实数'){
						 stateArray[1]='float'
				context.localVar[stateArray[2]] = {type:'float',value:0.0}
				
			}else if(stateArray[1]=='bool' || stateArray[1]=='boolean' || stateArray[1]=='Boolean'
			     || stateArray[1]=='逻辑值' || stateArray[1]=='布尔值'){
					 stateArray[1]='boolean'
				context.localVar[stateArray[2]] = {type:'boolean',value:false}
				
			}else if(stateArray[1]=='array' || stateArray[1]=='[]' || stateArray[1]=='list' 
			         || stateArray[1]=='数组' || stateArray[1]=='列表'){
						 stateArray[1]='list' 
				context.localVar[stateArray[2]] = {type:'list',value:[]}
				
			}else if(stateArray[1]=='object' || stateArray[1]=='Object' || stateArray[1]=='对象'){
				stateArray[1]='object'
				context.localVar[stateArray[2]] = {type:'object',value:{}}
				
			}else{
				throw new Error("数据类型未定义："+stateArray.join(' '))
			}
			if(stateArray.length >= 5 && (stateArray[3]=='=' || stateArray[3]=='等于' 
			           || stateArray[3]=='是' || stateArray[3]=='赋值' )){
				stateArray[3]='='
				let result = {}
				let rightState = stateArray.slice(4).join('')
				if(rightState.trim() == '{}'){
					
				}else if(xSysFunc.checkObj(rightState)){
					let tmpMap = this.mergeMaps(context.blockMap,context.strMap)
					result = xSysFunc.doBackObj(rightState,tmpMap)
				}else if(xSysFunc.checkArray(rightState.trim())){
					// result = xSysFunc.formatArray(rightState.trim(),context.strMap)
					result = rightState.trim()
				}else{
					result = this.doExpression(context,stateArray.slice(4))
				}
				this.setVar(context,stateArray[2],result)
			}

		}else{
			throw new Error("语法结构错误："+stateArray.join(' '))
		}
		
	},
	solveVar(context,stateArray){
		xSysFunc.sumFuncTime("scptSolveVar")
		for(let r=0;r<stateArray.length;r++){
			if(!this.isKeywords(stateArray[r])){
				let smlArray = stateArray[r].split(',')
				if(stateArray[r][0]=='[' && stateArray[r].slice(-1)==']'){
					smlArray = stateArray[r].slice(1,stateArray[r].length - 1).split(',')
				}
				for(let k=0;k<smlArray.length;k++){
					if(!this.isKeywords(smlArray[k])){
						if(smlArray[k].slice(0,4) == 'ref:') continue
						if(smlArray[k].indexOf('[') > 0 && smlArray[k].trim().slice(-1)==']'){
							let index1 = smlArray[k].trim().slice(0,-1).split('[')[1]
							let value1 = this.getVar(context,index1)
							smlArray[k] = smlArray[k].trim().slice(0,-1).split('[')[0]+ '[' + value1.value +']'
						}
						
						let tmp = this.getVar(context,smlArray[k])
						if(tmp != null){
							if(tmp.type=='object'){
								smlArray[k] = '[obj:'+smlArray[k]+']'
							}else if(tmp.type=='list'){
								smlArray[k] = '[list:'+smlArray[k]+']'
							}else{
								smlArray[k] = tmp.value
							}
						}else{
							throw new Error('变量:'+smlArray[k]+"  没有定义")
						}
					}
				}
				stateArray[r] = stateArray[r][0]=='[' && stateArray[r].slice(-1)==']' ? ('[' + smlArray.join(',')+']')  : smlArray.join(',')
			}
		}
		return stateArray
	},
	getAllVarMap(context){
		xSysFunc.sumFuncTime("scptGetAllVarMap")
		let locMap = new Map
		for(const key in context.localVar){
			locMap.set(key,context.localVar[key].value)
		}
		for(const pm in context.param){
			let item = toRaw(context.param[pm]);
			locMap.set(item.name,item)
		}
		if(context.parent != null){
			let priorMap = this.getAllVarMap(context.parent)
			locMap = this.mergeMaps(locMap,priorMap)
		}
		
		return locMap
	},
	mergeMaps(map1, map2) {
		xSysFunc.sumFuncTime("scptMergeMaps")
	    let merged = new Map([...map1,...map2]); // 复制第一个Map作为基础
	    for (let key in map2) { // 遍历第二个Map的条目
	        if (!merged.has(key)) { // 如果基础Map中没有这个键，则添加它
	            merged.set(key, map2.get(key));
	        } // 如果需要覆盖，可以去掉这个判断条件或者修改逻辑以适应你的需求。
	    }
	    return merged;
	},
	getVar(context,varName){
		xSysFunc.sumFuncTime("scptGetVar")
		let tmpName = varName.indexOf('[') >=0 && varName.slice(-1)==']' ? varName.slice(0,varName.indexOf('[')) : varName 
		let cols = tmpName.split('.')
		if(context && (context.localVar.hasOwnProperty(cols[0]) || context.param.hasOwnProperty(cols[0]))){
			if(varName.indexOf('.') >= 1){
				let obj = {}
				if(context.param.hasOwnProperty(cols[0])){
					obj.type = typeof context.param[cols[0]]
					obj.value = context.param[cols[0]]
				}else{
					obj = context.localVar[cols[0]]
				}
				if(obj.type == 'object'){
					let mValue = xSysFunc.getItem(obj.value,varName.split('.').slice(1).join('.'))
					let mType = 'string'
					if(typeof mValue=='object'){
						mType = 'object'
					}else if(xSysFunc.isNumeric(mValue)){
						mType = 'number'
					}else if(typeof mValue=='boolean'){
						mType = 'boolean'
					}
					return {
						type:mType,
						value:mValue
					}
				}else if(obj.type == 'list'){
					let indx = varName.split('[')[1].slice(0,-1)
					if(indx == '' || !xSysFunc.isNumeric(indx)){
						throw new Error(varName + " 数组下标错误")
					}
					let mValue = obj.value[indx]
					let mType = 'string'
					if(typeof mValue=='object'){
						mType = 'object'
					}else if(xSysFunc.isNumeric(mValue)){
						mType = 'number'
					}else if(typeof mValue=='boolean'){
						mType = 'boolean'
					}
					return {
						type:mType,
						value:mValue
					}
				}else{
					throw new Error('对象变量 '+ varName +' 未定义')
				}
			}else{
				let valueObj = {}
				if(context.param.hasOwnProperty(cols[0])){
					valueObj.type = typeof context.param[cols[0]]
					valueObj.value = context.param[cols[0]]
				}else{
					valueObj =   context.localVar[cols[0]]
				}
				if(valueObj.type == 'list'){
					let mValue = valueObj.value
					if(varName.indexOf('[') > 0 && varName.indexOf(']') > 0 ){
						let indx = varName.split('[')[1].slice(0,-1)
						
						if(indx == ''){
							throw new Error(varName + " 数组下标错误")
						}else if(!xSysFunc.isNumeric(indx)){
							indx = this.getVar(context,indx)
						}
						if(typeof indx=='number' || (typeof indx=='string' && xSysFunc.isNumeric(indx))){
							mValue = valueObj.value[indx]
						}else{
							throw new Error(varName + " 数组下标错误")
						}
						 
					}
					let mType = 'string'
					if(typeof mValue=='object'){
						mType = 'object'
					}else if(xSysFunc.isNumeric(mValue)){
						mType = 'number'
					}else if(typeof mValue=='boolean'){
						mType = 'boolean'
					}
					return {
						type:mType,
						value:mValue
					}
				}
				return valueObj
			}
		}else{
			if(context.parent != null){
				return this.getVar(context.parent,varName)
			}
			return null
		}
	},
	setVar(context,varName,varValue){
		xSysFunc.sumFuncTime("scptSetVar")
		if(context.param.hasOwnProperty(varName)){
			throw new Error('变量 '+varName+' 是参数，不能设值')
		}
		let tmpName = varName.indexOf('[') >=0 && varName.slice(-1)==']' ? varName.slice(0,varName.indexOf('[')) : varName 
		let cols = tmpName.split('.')
		if(context && context.localVar.hasOwnProperty(cols[0])){
			let varObj = context.localVar[cols[0]]
			if(varObj.type != 'object' && cols.length > 1){
				console.log("变量名 "+varName + " 命名不规范")
				throw new Error("变量名 "+varName + " 命名不规范")
			}
			if(varObj.type=='string'){
				if(typeof varValue =='string'){
					context.localVar[cols[0]] = {type:varObj.type,value:varValue+''}
				}else{
					throw new Error('变量 '+cols[0]+' 是string，初始化错误')
				} 

			}else if(varObj.type=='number'){
				if(typeof varValue =='number' || (typeof varValue=='string' && xSysFunc.isNumeric(varValue))){
					context.localVar[cols[0]] = {type:varObj.type,value:varValue+''}
				}else{
					throw new Error('变量 '+cols[0]+' 是Integer，初始化错误')
				} 

			}else if(varObj.type=='float'){
				if(typeof varValue =='float' || typeof varValue =='double' ||
				  (typeof varValue=='string' && xSysFunc.isNumeric(varValue))){
					  context.localVar[cols[0]] = {type:varObj.type,value:varValue+''}
				  }else{
					 throw new Error('变量 '+cols[0]+' 是Float，初始化错误') 
				  } 

			}else if(varObj.type=='boolean'){
				if(typeof varValue =='boolean' ||
				  (typeof varValue=='string' && (varValue == 'true' || varValue == 'false'))){
					 context.localVar[cols[0]] = { type:varObj.type,value:varValue+''} 
				  } else {
					 throw new Error('变量 '+cols[0]+' 是Boolean，初始化错误') 
				  } 

			}else if(varObj.type=='list'){
				if(varName.indexOf('[') >= 0 && varName.slice(-1)==']'){
					 let indx = varName.split('[')[1].slice(0,-1)
					 context.localVar[cols[0]].value[indx] = varValue
				 }else{
					 let allMap = this.getAllVarMap(context)
					 allMap = this.mergeMaps(allMap,context.blockMap)
					 allMap = this.mergeMaps(allMap,context.strMap)
					 let varArray = xSysFunc.xToArray(varValue,allMap)
					 if(varArray != null){
						 context.localVar[cols[0]].value = varArray
					 }else{
						 throw new Error(varValue+' 不是数据，不能赋值')
					 }
					 
				 }
				
			}else if(varObj.type=='object'){
				if(cols.length > 1){
					xSysFunc.itemSet(context.localVar[cols[0]].value,varName.split('.').slice(1).join('.'),varValue)
				}else{
					context.localVar[cols[0]] = {type:varObj.type,value:varValue}
				}
				
			}else{
				throw new Error("数据类型未定义："+stateArray.join(' '))
			}

		}else{
			if(context.parent != null){
				return this.setVar(context.parent,varName,varValue)
			}
		}
	},
	checkVar(context,varName){
		xSysFunc.sumFuncTime("scptCheckVar")
		let cols = varName.split('.')
		if (context && context.localVar.hasOwnProperty(cols[0])){
			if(cols.length > 1){
				let obj = context.localVar[cols[0]]
				if(obj.type=='object'){
					return xSysFunc.hasItem(obj.value,varName.substring(varName.indexOf('.')))
				}
			}else{
				return true
			}
		}else{
			if(context.parent != null){
				return this.checkVar(context.parent,cols[0])
			}
		}
		return false
	},
	
	replaceNoteScript(s){  //把脚本注释移除 备用
		xSysFunc.sumFuncTime("replaceNoteScript")
		let sptArray = s.split("\n")
		let mScript = []
		for(let r=0;r<sptArray.length;r++){
			let tp = sptArray[r].trim()
			if(tp.substring(0,2)=='//' || tp.length == 0) continue
			mScript.push(tp)
		}
		return mScript.join('\n');
	},
	putScriptToArray(s){  //把脚本分解成语句数组函数
		xSysFunc.sumFuncTime("scptPutScriptToArray")
		let sptArray = s.split(";")
		let mScript = []
		for(let r=0;r<sptArray.length;r++){
			// let tp = sptArray[r].split("\t").join(' ').trim()
			let tp = sptArray[r].replaceAll("\t",' ').trim()
			if(tp.substring(0,2)=='//' || tp.length == 0) continue
			tp = tp.replaceAll('>=',' *ge* ')
			tp = tp.replaceAll('<=',' *me* ')
			tp = tp.replaceAll('==',' *ee* ')
			tp = tp.replaceAll('!=',' *ne* ')
			tp = tp.replaceAll('>',' *gt* ')
			tp = tp.replaceAll('<',' *mt* ')
			tp = tp.replaceAll('=',' *eq* ')
			
			tp = tp.replaceAll(' *eq* ',' = ')
			tp = tp.replaceAll(' *me* ',' <= ')
			tp = tp.replaceAll(' *ee* ',' == ')
			tp = tp.replaceAll(' *ne* ',' != ')
			tp = tp.replaceAll(' *gt* ',' > ')
			tp = tp.replaceAll(' *mt* ',' < ')
			tp = tp.replaceAll(' *ge* ',' >= ')
			
			tp = tp.replaceAll('(',' ( ')
			tp = tp.replaceAll(')',' ) ')
			
			tp = tp.replaceAll('++',' *aa* ')
			tp = tp.replaceAll('+',' + ')
			tp = tp.replaceAll(' *aa* ',' ++ ')
			tp = tp.replaceAll('--',' *dd* ')
			tp = tp.replaceAll('-',' - ')
			tp = tp.replaceAll(' *dd* ',' -- ')
			tp = tp.replaceAll('*',' * ')
			tp = tp.replaceAll('/',' / ')
			
			mScript.push(tp)
		}
		return mScript
	},

	clearNullArray(ar){
		xSysFunc.sumFuncTime("scptClearNullArray")
		let re = []
		for(let n=0;n<ar.length;n++){
			if(ar[n].trim() != ""){
				re.push(ar[n])
			}
		}
		return re
	},
	
	optimizeArray(sc){
		let tmpScript = sc 
		for(let t=0;t<100;t++){
			let nTmp = this.getBlock(tmpScript,'[',']')
			if(nTmp.trim().length > 0){
				let tmpValue = nTmp.replaceAll(';',' ')
				sc = sc.replaceAll(nTmp,tmpValue)
				tmpScript = tmpScript.replaceAll(nTmp,"$$#"+ t + "#$$")
				continue;
			}else{
				break;
			}
		}
		return sc
	},
	
	doBlack(sc,b,e,nMap,b1,e1,end,floor,rec){  //记录块，替换块
	    xSysFunc.sumFuncTime("scptDoBlack")
		let tmpScript = sc
		let tmpStrScript = tmpScript
		for(let t=0;t<100;t++){
			let tmpStr = this.getBlock(tmpStrScript,b,e)
			if(tmpStr.length > 0){
				let key = b1 + floor + '#' + t + e1
				nMap.set(key ,tmpStr)
				// tmpScript = tmpScript.split(b+tmpStr+e).join(' '+key+' '+end)
				// console.log(tmpStr)
			 	let endStr = tmpStr.indexOf(";") >= 0 ? end : " ";
				tmpScript = tmpScript.replaceAll(b+tmpStr+e,' '+key+' '+endStr)

				let start = tmpStrScript.indexOf(b+tmpStr+e)
				let nextStart =start + (b+tmpStr+e).length
				tmpStrScript = tmpStrScript.substring(nextStart)
				continue
			}else{
				break
			}
		}
		
		return tmpScript
	},
	
	
	getBlock(sc,b,e){ //查找块函数
	    xSysFunc.sumFuncTime("scptGetBlock")
		let tmpStr = sc
		if(b==e && b != ''){
			let startPos = tmpStr.indexOf(b)
			let nOffset = startPos+b.length
			let endPos = nOffset + tmpStr.substring(nOffset).indexOf(b)
			if(startPos >= 0 && startPos < endPos){
				return tmpStr.substring(nOffset,endPos)
			}
		}else if(b != e && e != '' && b != ''){
			if(tmpStr.indexOf(b) >= 0){
				 tmpStr = tmpStr.substring(tmpStr.indexOf(b))
				 let strArr = tmpStr.split(b)
				 let floor = 0
				 let result = ""
				 for(let n=0;n<strArr.length;n++){
					 if(strArr[n] == '') continue
					 result += b
					 floor++
					 let endArr = strArr[n].split(e)
					 if(endArr.length >= 2){
						 for(let m=0;m<endArr.length;m++){
							 if(endArr[m]=='') continue
							 result += endArr[m]
							 if (m < endArr.length - 1) {
								result +=  e
							 	floor--
							 }
							 
							 if(floor <= 0){
								 return result.substring(b.length,result.length - e.length)
							 }
						 }
					 }else{
						result += strArr[n]
					 }
				 }
			}
		}
		return ""
	},
}