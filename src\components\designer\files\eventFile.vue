<template>
	<div class="content file-box" @mousemove="handleMouseMove" @mouseup="handleMouseUp">
		<div class="main-content ">
			<div class="file-title-box" >
				<div class="file-title">{{titleContent}}</div>
				<div v-if="showTestRunButton" class="file-btn" @click.stop="handleTestRun">
					<el-icon :size="15">
						<VideoPlay />
					</el-icon>
					<div class="file-label">试运行</div>
				</div>
				<div class="file-btn" @click.stop="showParam = !showParam">
					<el-icon :size="15"
						:style="{transition:'all 0.5s',transform:showParam ? 'rotate(-90deg)' : 'rotate(0deg)'}">
						<CaretLeft />
					</el-icon>
					<div class="file-label">{{showParam ? '收成' : '展开'}}</div>
				</div>
				<div class="file-btn" @click.stop="addParam()">
					<el-icon :size="15">
						<Operation />
					</el-icon>
					<div class="file-label">添加参数</div>
				</div>
				<div class="file-btn">
					<el-icon :size="15">
						<Reading />
					</el-icon>
					<div class="file-label" @click.stop="showGrammarPane = !showGrammarPane">语法板</div>
				</div>
			</div>
			<div class="param-box" :style="{height: paramHeight + 'px'}">
				<div class="flex_box">
					<div style="flex: 1;font-size: 13px;padding:5px ">参数名</div>
					<div style="width:200px;font-size: 13px;">参数类型</div>
					<div style="flex:1;font-size: 13px;">参数默认值</div>
					<div style="flex:1;font-size: 13px;">参数说明</div>
					<div style="width:40px;font-size: 13px;"></div>
				</div>
				<div v-for="(item,row) in scriptFile.paramList" :key="row" class="flex_box">
					<div style="flex:1;">
						<el-input v-model="item.name" placeholder="输入参数名称.." />
					</div>
					<div style="width:200px;">
						<el-select v-model="item.type" placeholder="选择数据类型" size="default">
						    <el-option key="String" label="字符型(String)" value="String" />
							<el-option key="Number" label="数值型(Number)" value="Number" />
							<el-option key="Boolean" label="逻辑型(Boolean)" value="Boolean" />
							<el-option key="Object" label="对象型(Object)" value="Object" />
							<el-option key="Array" label="数组型(Array)" value="Array" />
						</el-select>
					</div>
					<div style="flex:1;">
						<el-input v-model="item.value" :placeholder="'输入默认值.., 如:' + getDefaultFg(item.type)" />
					</div>
					<div style="flex:1;">
						<el-input v-model="item.remark" placeholder="输入参数说明" />
					</div>
					<div style="width:40px;">
						<el-icon :size="15" style="cursor:pointer;" @click.stop="removeParam(row)"> <Close /> </el-icon>
					</div>
				</div>
			</div>
			<div class="h-line" :style="{top: (paramHeight + 37) + 'px'}" @mousedown="onParamMouseDown"></div>
			<div class="code-box" :style="{height:'calc(100vh - 152px - '+ (layoutParam.bottomTabHeight + paramHeight)+'px)'}">
				<CodeMirrorEditor style="height:inherit;font-size: 15px;" v-model:modelValue = "scriptFile.script" @change="onChange"></CodeMirrorEditor>
			</div>
		</div>
		<div class="gammer-box" :style="{width: grammWidth + 'px',height:'calc(100vh - 134px - '+ layoutParam.bottomTabHeight+'px)'}" v-if="showGrammarPane">
             <div class="gammer-line" @mousedown = "onGramMouseDown" ></div>
			 <div class="gammer-content">
				 <div class="gammer-title-box">
					 <div class="gammer-title">语法库</div>
					 <el-icon :size="15" style="cursor:pointer;" @click.stop="showGrammarPane=false"> <Close /> </el-icon>
				 </div>
				 <div class="gammer-list demo-collapse">
					     <el-collapse v-model="controlData.activeNames">
					       <el-collapse-item v-for="(item,index) in groupGrammerList(props.category)" :key="index" :name="item.value">
					 		<template #title>
					 		  <span class="collapse-item-title">{{item.title}}</span>
					 		</template>
					        <div class="gramm-content" v-if="item.value != 'database' && item.value != 'pagevar' && item.value != 'globalvar'">
								<template v-if="getGrammerPart(item.value).hasCategories">
									<!-- 有二级分类的情况，使用嵌套的 el-collapse -->
									<el-collapse v-model="controlData.subActiveNames" class="sub-collapse ">
										<el-collapse-item v-for="(category, catIndex) in getGrammerPart(item.value).categories" :key="catIndex" :name="category.name">
											<template #title>
												<span class="sub-collapse-item-title "
													  :class="{ 'active-title': controlData.subActiveNames.includes(category.name) }">
													{{category.name}}
												</span>
											</template>
											<div v-for="(g,i) in category.items" :key="i" class="grammer-item"
												 :draggable="true" @dragstart="onGrammerStart(g,$event)" @dblclick="onDbClick(g,$event)">{{g.name}}</div>
										</el-collapse-item>
									</el-collapse>
								</template>
								<template v-else>
									<!-- 没有二级分类的情况，直接显示项目 -->
									<div v-for="(g,i) in getGrammerPart(item.value).items" :key="i" class="grammer-item"
										 :draggable="true" @dragstart="onGrammerStart(g,$event)" @dblclick="onDbClick(g,$event)">{{g.name}}</div>
								</template>
							</div>
							<!-- :style="{height:'calc(100vh - 200px - '+layoutParam.bottomTabHeight+'px)',overflow:'auto'}" -->
							<div v-else-if="item.value == 'database'">
								<el-tree
								    ref="grmTreeRef"
								    class="filter-tree"
								    :data="databaseRef.tables"
									default-expand-all
									empty-text="没有设置数据源"
								  >
								    <template #default="scope">
										<div style="padding:8px 1px;font-size: 14px;display: flex;justify-content: space-between;align-items: center;">
											<el-icon :size="18">
											    <Coin v-if="scope.data.type=='db'"/>
												<Tickets v-else-if="scope.data.type=='table'"/>
												<Expand v-else-if="scope.data.type=='column'"/>
											</el-icon>
										   <div draggable="true" @dragstart="onGrammerStart(scope.data,$event)" style="flex:1;">
										       {{scope.data.name}}
											</div>
										</div>
									</template>
								  </el-tree>
							</div>
							<div v-else-if="item.value=='pagevar'">
								<div class="page-data-bar" @dragover="handleDragover" @drop="handleDrop">
									<input type="text"  class="page-data-title" v-model="scriptFile.connectFile"
									    placeholder="从左边页面树上拖拽一个页面放此"/>
									<div class="page-data-btn">编辑</div>
									<div class="page-data-btn" @click.stop="onRefreshPageTree()">刷新</div>
								</div>
								<el-tree
								    ref="grmTreeRef2"
								    class="filter-tree"
								    :data="varMgr.pageVars"
									default-expand-all
									empty-text="没有页面变量"
								  >
								    <template #default="scope">
										<div style="padding:8px 1px;font-size: 14px;display: flex;justify-content: space-between;align-items: center;"
										    @dblclick="onDbClick(scope.data,$event)">
											<el-icon :size="18">
											    <ScaleToOriginal />
											</el-icon>
										   <div draggable="true" @dragstart="onGrammerStart(scope.data,$event)" style="flex:1;padding-left: 5px;">
										       {{scope.data.name + '[' + scope.data.type+']'}}
											</div>
										</div>
									</template>
								  </el-tree>
							</div>
							<div v-else-if="item.value=='globalvar'">
								<div style="display: flex;justify-content: space-between;align-items: center;padding-right: 10px;">
									<div>可引用全局变量</div>
									<el-icon :size="20" class="refresh-btn" @click.stop="readGlobalData()"><Refresh /></el-icon>
								</div>
								<el-tree
								    ref="grmTreeRef3"
								    class="filter-tree"
								    :data="varMgr.globalVars"
									default-expand-all
									empty-text="没有设置全局变量"
								  >
								    <template #default="scope">
										<div style="padding:8px 1px;font-size: 14px;display: flex;justify-content: space-between;align-items: center;"
										    @dblclick="onDbClick(scope.data,$event)">
											<el-icon :size="18">
											    <ScaleToOriginal />
											</el-icon>
										   <div draggable="true" @dragstart="onGrammerStart(scope.data,$event)" style="flex:1;padding-left: 5px;">
										       {{scope.data.name + '[' + scope.data.type+']'}}
											</div>
										</div>
									</template>
								  </el-tree>
							</div>
					       </el-collapse-item>
					     </el-collapse>
				 </div>
			 </div>
		</div>

	</div>
</template>

<script lang="ts" setup>
	import { reactive, ref, onMounted, inject, computed,toRaw ,watch, nextTick} from 'vue'
	import { useFileBase } from '../../../common/fileBasic';
	import CodeMirrorEditor from '../../CodeMirrorEditor.vue';
	import {apiUpdateFile,apiLoadFileJson,apiReadCurrentDbSchema,apiMethodCompileExecute} from '../../../apis/designer.js'
    import { ElMessage } from 'element-plus';
	import { useExplainVar } from '../../../common/useExplainVar';
	
	const {parse} = useExplainVar()
	const layoutParam = inject("layoutParam")
	const ProjectRef = inject("ProjectRef")
	const fileBase = useFileBase()
	const props = defineProps({
		fileItem:{
			type:Object,
			default:{}
		},
		category:{
			type:String,
			default:"event"
		}
	})
	
	const varMgr= reactive({
		pageVars:[],
		globalVars:[]
	})
	
	const scriptFile = reactive({
		fileId:0,
		version:0,
		script:"",
		connectFile:"",
		paramList:[
			{
				name: 'param1',
				type: 'String',
				value: '',
				remark:""
			},
			{
				name: 'param2',
				type: 'String',
				value: '',
				remark:""
			}
		]
	})
	
	watch(()=>scriptFile.paramList,(v)=>{
		props.fileItem.isModify = true
	},{deep:true})
	
	const titleContent = ref("script Editor ---脚本编辑")
	const eventRef = ref<HTMLElement>()   
	const showParam = ref(true)
	const paramOriginalHeight = ref(100)
	const paramHeight = computed(() => {
		return showParam.value ? paramOriginalHeight.value : 0
	})
	const showGrammarPane = ref(true)
	const grammOriginalWidth = ref(320)
	const grammWidth = computed(()=>{
		return showGrammarPane.value ? grammOriginalWidth.value : 0
	})

	// 判断是否显示"试运行"按钮
	const showTestRunButton = computed(() => {
		return titleContent.value.startsWith("Function Editor")
	})

	// 从titleContent中提取filePath
	const extractFilePath = computed(() => {
		const match = titleContent.value.match(/\[(.*?)\]/)
		return match ? match[1].trim() : ''
	})

	const handleMouseParam = reactive({
		state:0,
		downX:0,
	})
	
	const controlData = reactive({
		activeNames:[],
		subActiveNames:[],
		groupGrammerList:[
			{
				title:"数据库",
				value:'database',
				owner:'methrod'
			},
			{
				title:"SQL语法",
				value:'sql',
				owner:'methrod'
			},
			{
				title:"定义变量",
				value:'custom',
				owner:'public'
			},
			{
				title:"条件语法",
				value:'if',
				owner:'public'
			},
			{
				title:"循环语法",
				value:'for',
				owner:'public'
			},
			{
				title:"调用后台脚本",
				value:'call',
				owner:'event'
			},
			{
				title:"系统方法",
				value:'function',
				owner:'public'
			},
			{
				title:"页面变量",
				value:'pagevar',
				owner:'event'
			},
			{
				title:"全局变量",
				value:'globalvar',
				owner:'event'
			}
		],
		grammerList:{}
	})
	
	function groupGrammerList(type){
		return controlData.groupGrammerList.filter((item)=>{
			return item.owner=='public' || item.owner==type
		})
	}
	
	controlData.grammerList = inject("GrammerList")
	
	const databaseRef = reactive({
		tables:[
			{
				id:-100,
				name:'数据源',
				value:"root",
				type:'db',
				children:[
					{
						id:23,
						name:'user[用户表]',
						value:'user',
						type:'table',
						children:[
							{
								id:231,
								name:'account[账号]',
								value:'account',
								type:'column',
							},
							{
								id:231,
								name:'name[姓名]',
								value:'name',
								type:'column',
							},
							{
								id:231,
								name:'address[地址]',
								value:'address',
								type:'column',
							},
							{
								id:231,
								name:'password[密码]',
								value:'password',
								type:'column',
							},
							{
								id:231,
								name:'age[年龄]',
								value:'age',
								type:'column',
							}
						]
					},
					{
						id:24,
						name:'order[订单表]',
						value:'order',
						type:'table',
						children:[]
					},
					{
						id:25,
						name:'employee[员工表]',
						value:'employee',
						type:'table',
						children:[]
					},
					{
						id:26,
						name:'business[业务表]',
						value:'business',
						type:'table',
						children:[]
					}
				]
			}
		]
	})
	
	const handleMouseMove = (event)=>{
		if(handleMouseParam.state == 100){
			grammOriginalWidth.value += handleMouseParam.downX - event.clientX
			handleMouseParam.downX = event.clientX
		}else if(handleMouseParam.state == 200){
			paramOriginalHeight.value += event.clientY - handleMouseParam.downX
			handleMouseParam.downX = event.clientY
		}
	}
		
	const handleMouseUp = (event)=>{
		handleMouseParam.state = 0
	}
	
	function handleDragover(e){
		e.preventDefault()
	}
	
	function handleDrop(e){
		const res = e.dataTransfer.getData("treeItem")
		if(res){
			if(res.split(".")[0] == 'page' || res.split(".")[0] == 'component' ){
				 scriptFile.connectFile = res
				 onRefreshPageTree()
			}else{
				ElMessage({type: 'warning',message: '文件类型错误，必须是 页面 或者 组件 类型文件',})
			}
		}
	}

	onMounted(() => {
		if(props.category=='event'){
			titleContent.value = "Event Editor -- [ "+props.fileItem.fileItem.filePath + ' ]'
			readGlobalData()
		}else if(props.category=='methrod'){
			titleContent.value = "Function Editor -- [ "+props.fileItem.fileItem.filePath + ' ]'
			readCurrentDbSchema()
		}
		props.fileItem.func.saveData = saveData
		loadJsonByPath()
		
	})
	
	function readGlobalData(){
		let vars = parse(ProjectRef.BasicData.globalData.varText,"global")
		if(vars){
			varMgr.globalVars =  vars
		}else{
			varMgr.globalVars = []
		}
		
	}
	
	function onChange(data){
		props.fileItem.isModify = true
	}

	// "试运行"按钮点击事件
	async function handleTestRun(){
		try {
			const projectId = ProjectRef.BasicData.ProjectId
			const filePath = extractFilePath.value

			if (!filePath) {
				// 输出错误日志到LogMgr
				if (ProjectRef.funcObj?.otherFunc?.writeLog) {
					ProjectRef.funcObj.otherFunc.writeLog(`[试运行错误] 无法获取文件路径: ${titleContent.value}`)
				}
				return
			}

			if (ProjectRef.funcObj?.otherFunc?.writeLog) {
				ProjectRef.funcObj.otherFunc.writeLog(`[试运行开始] 执行文件: ${filePath}`)
			}

			const res = await apiMethodCompileExecute(projectId, filePath)

			if (res.code === 200) {
				
				if (ProjectRef.funcObj?.otherFunc?.writeLog) {
					ProjectRef.funcObj.otherFunc.writeLog(`[试运行成功] ${filePath}`)
					if (res.data) {
						const resultText = typeof res.data === 'string' ? res.data : JSON.stringify(res.data, null, 2)
						ProjectRef.funcObj.otherFunc.writeLog(`[执行结果] ${resultText}`)
					}
				}
			} else {
				
				if (ProjectRef.funcObj?.otherFunc?.writeLog) {
					ProjectRef.funcObj.otherFunc.writeLog(`[试运行失败] ${filePath}: ${res.message || '未知错误'}`)
				}
			}
		} catch (error) {
			if (ProjectRef.funcObj?.otherFunc?.writeLog) {
				ProjectRef.funcObj.otherFunc.writeLog(`[试运行异常]  ${error.message || error}`)
			}
		}
	}
	
	function saveData(success){
		let json = toRaw(scriptFile)
		updateFile(props.fileItem.fileItem.filePath,json,(data)=>{
			ElMessage({
				message:"保存成功",
				type:"success"
			})
			props.fileItem.isModify = false
			if(success) success()
		})
	}
	
	function onGramMouseDown(event:MouseEvent){
		handleMouseParam.state = 100
		handleMouseParam.downX = event.clientX
	}
	function onParamMouseDown(event:MouseEvent){
		handleMouseParam.state = 200
		handleMouseParam.downX = event.clientY
	}
	
	function addParam(){
		showParam.value = true
		let xh = (scriptFile.paramList.length + 1) * 32 + 30
		if(paramOriginalHeight.value < xh){
			paramOriginalHeight.value = xh
		}
		scriptFile.paramList.push({
			name:"",
			type:'String',
		    value:"",
			remark:""
		})
		props.fileItem.isModify = true
	}
	function removeParam(index:number){
		scriptFile.paramList.splice(index,1)
		let xh = scriptFile.paramList.length * 32 + 30
		paramOriginalHeight.value = xh
		props.fileItem.isModify = true
	}
	
	function onGrammerStart(g,e){
		let pm = {
			type:"grammer",
			value:g.value
		}
		if(g.hasOwnProperty('type') && g.type=='table'){
			let columns = ""
			if(g.hasOwnProperty('children')){
				for(let i=0;i<g.children.length;i++){
					if(columns.length > 0){
						columns +=","+g.children[i].value
					}else{
						columns = g.children[i].value
					}
				}
			}
			if(columns.length > 0){
				pm.value = "select "+ columns +" from "+g.value+';\n'
			}
		}
		e.dataTransfer.setData('item',JSON.stringify(pm))
	}

	function getGrammerPart(groupName:string){
		
		const grammerList = controlData.grammerList as any

		if (!grammerList || !grammerList.list) {
			return { hasCategories: false, items: [] }
		}

		const filteredItems = grammerList.list.filter((item)=>{
			return item.type == groupName && (item.rangeType==0 || (item.rangeType == (props.category=='event' ? 1 : 2)))
		})

		// 检查是否有需要分类的项目（rangeType=2 且有 classifyType）
		const itemsWithClassify = filteredItems.filter(item => item.rangeType === 2 && item.classifyType)
		const itemsWithoutClassify = filteredItems.filter(item => !(item.rangeType === 2 && item.classifyType))

		if (itemsWithClassify.length > 0) {
			// 按 classifyType 分组
			const categories = {}
			itemsWithClassify.forEach(item => {
				const categoryName = item.classifyType
				if (!categories[categoryName]) {
					categories[categoryName] = []
				}
				categories[categoryName].push(item)
			})

			// 返回分类结构
			const categoryList = Object.keys(categories).map(categoryName => ({
				name: categoryName,
				items: categories[categoryName]
			}))

			return { hasCategories: true, categories: categoryList, items: itemsWithoutClassify }
		}

		// 没有分类的情况，返回普通结构
		return { hasCategories: false, items: filteredItems }
	}
	
	function onDbClick(g,event){
		ProjectRef.funcObj.insertCoder(g.value)
	}
	
	function getDefaultFg(type){
		if(type=='String'){
			return 'xxx'
		}else if(type=='Number'){
			return '0,3,1.2'
		}else if(type=='Boolean'){
			return 'false'
		}else if(type=='Object'){
			return '{}'
		}else if(type=='Array'){
			return '[]'
		}
	}
	
	function onRefreshPageTree(){
		if(scriptFile.connectFile && scriptFile.connectFile.length > 0){
			loadPageByPath(scriptFile.connectFile,(data)=>{
				if(data.content){
					let JsonObj = JSON.parse(data.content)
					// console.log(JsonObj)
					if(JsonObj.instance && JsonObj.instance.varText){
						varMgr.pageVars = parse(JsonObj.instance.varText,"PageData")
					}
				}
				// console.log(data)
			})
		}
	}
	
	async function updateFile(path,json,success){
		console.log("save data=",json)
		let res = await apiUpdateFile({
			projectId:ProjectRef.BasicData.ProjectId,
			version:scriptFile.version,
			path:path,
			_json:json
		})
		if(res.code==200){
			if(res.data.hasOwnProperty("success") && res.data.success==false){
				ElMessage({
					message:res.message,
					type:"error"
				})
			}else{
			    scriptFile.version++
				if(success) success(res)
				props.fileItem.isModify = false
			}
		}
	}
	
	async function loadPageByPath(path,success){
		let res = await apiLoadFileJson({
			projectId:ProjectRef.BasicData.ProjectId,
			path:path
		})
		if(res.code==200){
			if(success) success(res.data)
		}
	}
	
	async function loadJsonByPath(){
		let res = await apiLoadFileJson({
			projectId:ProjectRef.BasicData.ProjectId,
			path:props.fileItem.fileItem.filePath
		})
		if(res.code==200){
			scriptFile.version = res.data.version
			if(res.data.content){
				let tmpJson = JSON.parse(res.data.content)
				console.log(tmpJson)
				if(tmpJson){
					scriptFile.fileId = tmpJson.fileId
					scriptFile.script = tmpJson.script
					scriptFile.paramList = tmpJson.paramList
					scriptFile.connectFile = ""
					if(tmpJson.connectFile && tmpJson.connectFile.length > 0){
						scriptFile.connectFile = tmpJson.connectFile
						onRefreshPageTree()
					}
					nextTick(()=>{
						props.fileItem.isModify = false
					})
				}
			}
		}
	}
	
	async function readCurrentDbSchema(){
		let res = await apiReadCurrentDbSchema(ProjectRef.BasicData.ProjectId)
		if(res.code==200){
			if(res.data && res.data.length > 0){
				databaseRef.tables[0].children = []
				let tableIndex = 1
				res.data.forEach(tableItem=>{
					let tables = {
						id:tableIndex,
						name:tableItem.tableName + '['+tableItem.fullName+']',
						value:tableItem.tableName,
						type:'table'
					}
					if(tableItem.columnList && tableItem.columnList.length > 0){
						tables.children = []
						let columnIndex = 1
						tableItem.columnList.forEach(columnItem=>{
							let column = {
								id:tableIndex*100+columnIndex,
								name:columnItem.columnName+'['+columnItem.fullName+']',
								value:columnItem.columnName,
								type:'column'
							}
							tables.children.push(column)
							columnIndex++
						})
					}
					databaseRef.tables[0].children.push(tables)
					tableIndex++
				})
			}
		}
	}

</script>

<style lang="scss" scoped>
	.content {
		position: relative;
		user-select: none;
		.main-content {
			position: relative;
			background-color: white;
			width:100%;
			height:100%;
			top:0px;
			left:0px;

			.file-title-box {
				position: absolute;
				top:0px;
				left:0px;
				right:0px;
				height: 42px;
				border-bottom: 1px solid #e6e6e6;
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 0px 20px;

				.file-title {
					flex: 1;
					text-align: left;
					font-size: 13px;
				}

				.file-btn {
					display: flex;
					justify-content: space-around;
					align-items: center;
					margin-left: 30px;
					font-size: 13px;
					font-weight: 300;
					cursor: pointer;

					.file-label {
						margin-left: 3px;
					}
				}
			}

			.param-box {
				position: absolute;
				top:37px;
				left:0px;
				right:0px;
				border-bottom: 1px solid #e6e6e6;
				background-color: #e6e6e6;
				overflow-y:auto ;
			}
			.h-line {
				position: absolute;
				left:0px;
				right:0px;
				width:100%;
				height:3px;
				background-color: transparent;
				cursor:row-resize;
				z-index:898;
				
			}
			.code-box {
				position: absolute;
				bottom:3px;
				left:1px;
				right:1px;
				overflow: auto;
				// border:1px solid red;
				width:calc(100% - 5px);
			}
		}
		.gammer-box{
			position: absolute;
			top:35px;
			bottom:0px;
			right:0px;
			width:100px;
			border:1px solid #e6e6e6;
			background-color: white;
			z-index:900;
			overflow-x:hidden;
			overflow-y:auto ;
			
			.gammer-line{
				position: absolute;
				top:0px;
				left:0px;
				width:4px;
				height:100%;
				z-index:987;
				cursor:col-resize;
			}
			.gammer-content{
				position: absolute;
				top:0px;
				left:0px;
				width:100%;
				height:100%;
				z-index:901;
				// text-shadow: -10px 0px 10px #ababab;
				
				.gammer-title-box{
					position: absolute;
					top:0px;
					left:0px;
					right:0px;
					height:32px;
					background-color: #d5d5d5;
					display: flex;
					justify-content: space-between;
					align-items: center;
					padding:1px 10px 1px 20px;
					// border-bottom:1px solid #d4d4d4;
					overflow:hidden;
					
					.gammer-title{
						flex:1;
						text-align: left;
						font-size: 13px;
						
					}
				}
				
				.gammer-list{
					position: absolute;
					top:40px;
					left:2px;
					width:calc(100% - 22px);
					height:calc(100% - 40px);
					overflow: auto;
					padding-left: 16px;
					
					.collapse-item-title{
						font-size: 15px;
					}
				}
			}
		}
	}
	.gramm-content{
		width:100%;
		background-color: white;
		.grammer-item{
			border-bottom:1px solid #e3e3e3;
			padding:6px 10px;
			text-align: left;
			cursor:pointer;
			
			
		}
		
	}
	.refresh-btn{
		cursor:pointer;
		transition: all 0.5s;
	}
	.refresh-btn:hover{
		transform: rotate(90deg);
	}
	.refresh-btn:active{
		transform: rotate(-50deg);
	}
	
	.page-data-bar{
		display: flex;
		justify-content: space-between;
		align-items: center;
		
		.page-data-title{
			height:26px;
			flex:1;
			border:1px solid #e5e5e5;
			border-radius: 5px;
			margin-right:4px;
			padding:0px 5px;
		}
		.page-data-title:focus{
			outline: none;
		}
		.page-data-btn{
			border:1px solid #e5e5e5;
			border-radius: 6px;
			font-size:12px;
			padding:3px 5px;
			cursor:pointer;
			margin:1px 2px;
			color: #838383;
		}
		.page-data-btn:hover{
			border:1px solid #b4b4b4;
			color: #4b4b4b;
		}
	}

	/* 二级分类样式 - 展开状态时标题变蓝色 */
	.sub-collapse-item-title{
		
		padding-left: 15px;
		color: #838383;
	}
	.sub-collapse-item-title.active-title {
		color: #409eff !important;
		font-weight: 600;
		
	}
</style>