import{c1 as yn,ay as Te,c2 as bt,c3 as gt,bN as _,c4 as Sn,bI as Cn,c5 as On,c6 as In,H as ze,ao as En,_ as be,K as ee,N as ae,M as h,c as E,g as W,o as y,a as w,D as R,Q as k,n as g,v as D,f as M,b as z,a5 as ot,a8 as N,y as Pe,j as le,aL as wn,S as yt,ad as A,aV as Tn,aB as St,aC as Vn,e as V,bz as Z,L as Ve,P as X,b9 as kn,ag as x,au as Ke,w as ve,ah as He,t as K,$ as Ue,d as he,bg as Mn,a2 as H,B as Ge,an as Rn,bo as $n,c7 as Ln,aX as we,c8 as it,ap as Ct,bu as pe,ai as Dn,bW as Bn,O as Nn,af as Pn,bm as Fn,J as de,aF as Wn,ab as rt,bn as An,Y as zn,r as Y,i as Kn,F as Fe,C as ut,k as ct,at as fe,a4 as Hn,R as Be,c9 as Un,W as Gn,Z as Ot,ca as qn,bx as Qn,U as It}from"./index-Dgb0NZ1J.js";import{u as dt,e as Xn,s as jn,E as Jn,d as Yn}from"./el-scrollbar-DLTsEwUl.js";import{u as Et,h as wt,e as Zn,b as _n}from"./el-button-B8TCQS4u.js";import{h as xn,i as el,t as tl,e as nl,a as ll,d as ft,U as ne,C as Tt,u as al}from"./index-CINbulG0.js";import{c as Q}from"./castArray-BpiBxx45.js";import{b as Vt,i as me}from"./isEqual-CwZW0B1R.js";import{s as sl,f as ol}from"./vnode-CV7pw3rS.js";import{S as il,k as rl}from"./el-form-item-Beaw8WQV.js";var ul=/\s/;function cl(e){for(var l=e.length;l--&&ul.test(e.charAt(l)););return l}var dl=/^\s+/;function fl(e){return e&&e.slice(0,cl(e)+1).replace(dl,"")}var pt=NaN,pl=/^[-+]0x[0-9a-f]+$/i,vl=/^0b[01]+$/i,ml=/^0o[0-7]+$/i,hl=parseInt;function vt(e){if(typeof e=="number")return e;if(yn(e))return pt;if(Te(e)){var l=typeof e.valueOf=="function"?e.valueOf():e;e=Te(l)?l+"":l}if(typeof e!="string")return e===0?e:+e;e=fl(e);var a=vl.test(e);return a||ml.test(e)?hl(e.slice(2),a?2:8):pl.test(e)?pt:+e}function bl(e,l,a,r){e.length;for(var o=a+1;o--;)if(l(e[o],o,e))return o;return-1}var gl=1,yl=2;function Sl(e,l,a,r){var o=a.length,d=o;if(e==null)return!d;for(e=Object(e);o--;){var n=a[o];if(n[2]?n[1]!==e[n[0]]:!(n[0]in e))return!1}for(;++o<d;){n=a[o];var f=n[0],m=e[f],u=n[1];if(n[2]){if(m===void 0&&!(f in e))return!1}else{var c=new il,i;if(!(i===void 0?Vt(u,m,gl|yl,r,c):i))return!1}}return!0}function kt(e){return e===e&&!Te(e)}function Cl(e){for(var l=rl(e),a=l.length;a--;){var r=l[a],o=e[r];l[a]=[r,o,kt(o)]}return l}function Mt(e,l){return function(a){return a==null?!1:a[e]===l&&(l!==void 0||e in Object(a))}}function Ol(e){var l=Cl(e);return l.length==1&&l[0][2]?Mt(l[0][0],l[0][1]):function(a){return a===e||Sl(a,e,l)}}var Il=1,El=2;function wl(e,l){return bt(e)&&kt(l)?Mt(gt(e),l):function(a){var r=_(a,e);return r===void 0&&r===l?xn(a,e):Vt(l,r,Il|El)}}function Tl(e){return function(l){return l==null?void 0:l[e]}}function Vl(e){return function(l){return Sn(l,e)}}function kl(e){return bt(e)?Tl(gt(e)):Vl(e)}function Ml(e){return typeof e=="function"?e:e==null?el:typeof e=="object"?Cn(e)?wl(e[0],e[1]):Ol(e):kl(e)}var Ne=function(){return On.Date.now()},Rl="Expected a function",$l=Math.max,Ll=Math.min;function Dl(e,l,a){var r,o,d,n,f,m,u=0,c=!1,i=!1,v=!0;if(typeof e!="function")throw new TypeError(Rl);l=vt(l)||0,Te(a)&&(c=!!a.leading,i="maxWait"in a,d=i?$l(vt(a.maxWait)||0,l):d,v="trailing"in a?!!a.trailing:v);function p(O){var F=r,J=o;return r=o=void 0,u=O,n=e.apply(J,F),n}function S(O){return u=O,f=setTimeout(P,l),c?p(O):n}function T(O){var F=O-m,J=O-u,C=l-F;return i?Ll(C,d-J):C}function $(O){var F=O-m,J=O-u;return m===void 0||F>=l||F<0||i&&J>=d}function P(){var O=Ne();if($(O))return j(O);f=setTimeout(P,T(O))}function j(O){return f=void 0,v&&r?p(O):(r=o=void 0,n)}function Me(){f!==void 0&&clearTimeout(f),u=0,r=m=o=f=void 0}function Re(){return f===void 0?n:j(Ne())}function se(){var O=Ne(),F=$(O);if(r=arguments,o=this,m=O,F){if(f===void 0)return S(m);if(i)return clearTimeout(f),f=setTimeout(P,l),p(m)}return f===void 0&&(f=setTimeout(P,l)),n}return se.cancel=Me,se.flush=Re,se}function Bl(e,l,a){var r=e==null?0:e.length;if(!r)return-1;var o=r-1;return bl(e,Ml(l),o)}const Nl=(e="")=>e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d"),fa=e=>In(e),We=ze({type:{type:String,values:["primary","success","info","warning","danger"],default:"primary"},closable:Boolean,disableTransitions:Boolean,hit:Boolean,color:String,size:{type:String,values:En},effect:{type:String,values:["dark","light","plain"],default:"light"},round:Boolean}),Pl={close:e=>e instanceof MouseEvent,click:e=>e instanceof MouseEvent},Fl=ee({name:"ElTag"}),Wl=ee({...Fl,props:We,emits:Pl,setup(e,{emit:l}){const a=e,r=Et(),o=ae("tag"),d=h(()=>{const{type:u,hit:c,effect:i,closable:v,round:p}=a;return[o.b(),o.is("closable",v),o.m(u||"primary"),o.m(r.value),o.m(i),o.is("hit",c),o.is("round",p)]}),n=u=>{l("close",u)},f=u=>{l("click",u)},m=u=>{var c,i,v;(v=(i=(c=u==null?void 0:u.component)==null?void 0:c.subTree)==null?void 0:i.component)!=null&&v.bum&&(u.component.subTree.component.bum=null)};return(u,c)=>u.disableTransitions?(y(),E("span",{key:0,class:g(D(d)),style:le({backgroundColor:u.color}),onClick:f},[w("span",{class:g(D(o).e("content"))},[k(u.$slots,"default")],2),u.closable?(y(),W(D(Pe),{key:0,class:g(D(o).e("close")),onClick:N(n,["stop"])},{default:M(()=>[z(D(ot))]),_:1},8,["class","onClick"])):R("v-if",!0)],6)):(y(),W(wn,{key:1,name:`${D(o).namespace.value}-zoom-in-center`,appear:"",onVnodeMounted:m},{default:M(()=>[w("span",{class:g(D(d)),style:le({backgroundColor:u.color}),onClick:f},[w("span",{class:g(D(o).e("content"))},[k(u.$slots,"default")],2),u.closable?(y(),W(D(Pe),{key:0,class:g(D(o).e("close")),onClick:N(n,["stop"])},{default:M(()=>[z(D(ot))]),_:1},8,["class","onClick"])):R("v-if",!0)],6)]),_:3},8,["name"]))}});var Al=be(Wl,[["__file","tag.vue"]]);const zl=yt(Al),q=new Map;if(St){let e;document.addEventListener("mousedown",l=>e=l),document.addEventListener("mouseup",l=>{if(e){for(const a of q.values())for(const{documentHandler:r}of a)r(l,e);e=void 0}})}function mt(e,l){let a=[];return A(l.arg)?a=l.arg:Tn(l.arg)&&a.push(l.arg),function(r,o){const d=l.instance.popperRef,n=r.target,f=o==null?void 0:o.target,m=!l||!l.instance,u=!n||!f,c=e.contains(n)||e.contains(f),i=e===n,v=a.length&&a.some(S=>S==null?void 0:S.contains(n))||a.length&&a.includes(f),p=d&&(d.contains(n)||d.contains(f));m||u||c||i||v||p||l.value(r,o)}}const Kl={beforeMount(e,l){q.has(e)||q.set(e,[]),q.get(e).push({documentHandler:mt(e,l),bindingFn:l.value})},updated(e,l){q.has(e)||q.set(e,[]);const a=q.get(e),r=a.findIndex(d=>d.bindingFn===l.oldValue),o={documentHandler:mt(e,l),bindingFn:l.value};r>=0?a.splice(r,1,o):a.push(o)},unmounted(e){q.delete(e)}};function Hl(){const e=Vn(),l=V(0),a=11,r=h(()=>({minWidth:`${Math.max(l.value,a)}px`}));return Z(e,()=>{var d,n;l.value=(n=(d=e.value)==null?void 0:d.getBoundingClientRect().width)!=null?n:0}),{calculatorRef:e,calculatorWidth:l,inputStyle:r}}const Rt=Symbol("ElSelectGroup"),ke=Symbol("ElSelect"),Ae="ElOption",Ul=ze({value:{type:[String,Number,Boolean,Object],required:!0},label:{type:[String,Number]},created:Boolean,disabled:Boolean});function Gl(e,l){const a=Ve(ke);a||tl(Ae,"usage: <el-select><el-option /></el-select/>");const r=Ve(Rt,{disabled:!1}),o=h(()=>c(Q(a.props.modelValue),e.value)),d=h(()=>{var p;if(a.props.multiple){const S=Q((p=a.props.modelValue)!=null?p:[]);return!o.value&&S.length>=a.props.multipleLimit&&a.props.multipleLimit>0}else return!1}),n=h(()=>{var p;return(p=e.label)!=null?p:X(e.value)?"":e.value}),f=h(()=>e.value||e.label||""),m=h(()=>e.disabled||l.groupDisabled||d.value),u=Ke(),c=(p=[],S)=>{if(X(e.value)){const T=a.props.valueKey;return p&&p.some($=>kn(_($,T))===_(S,T))}else return p&&p.includes(S)},i=()=>{!e.disabled&&!r.disabled&&(a.states.hoveringIndex=a.optionsArray.indexOf(u.proxy))},v=p=>{const S=new RegExp(Nl(p),"i");l.visible=S.test(String(n.value))||e.created};return x(()=>n.value,()=>{!e.created&&!a.props.remote&&a.setSelected()}),x(()=>e.value,(p,S)=>{const{remote:T,valueKey:$}=a.props;if((T?p!==S:!me(p,S))&&(a.onOptionDestroy(S,u.proxy),a.onOptionCreate(u.proxy)),!e.created&&!T){if($&&X(p)&&X(S)&&p[$]===S[$])return;a.setSelected()}}),x(()=>r.disabled,()=>{l.groupDisabled=r.disabled},{immediate:!0}),{select:a,currentLabel:n,currentValue:f,itemSelected:o,isDisabled:m,hoverItem:i,updateOption:v}}const ql=ee({name:Ae,componentName:Ae,props:Ul,setup(e){const l=ae("select"),a=wt(),r=h(()=>[l.be("dropdown","item"),l.is("disabled",D(f)),l.is("selected",D(n)),l.is("hovering",D(v))]),o=he({index:-1,groupDisabled:!1,visible:!0,hover:!1}),{currentLabel:d,itemSelected:n,isDisabled:f,select:m,hoverItem:u,updateOption:c}=Gl(e,o),{visible:i,hover:v}=Ue(o),p=Ke().proxy;m.onOptionCreate(p),Mn(()=>{const T=p.value,{selected:$}=m.states,P=$.some(j=>j.value===p.value);H(()=>{m.states.cachedOptions.get(T)===p&&!P&&m.states.cachedOptions.delete(T)}),m.onOptionDestroy(T,p)});function S(){f.value||m.handleOptionSelect(p)}return{ns:l,id:a,containerKls:r,currentLabel:d,itemSelected:n,isDisabled:f,select:m,visible:i,hover:v,states:o,hoverItem:u,updateOption:c,selectOptionClick:S}}});function Ql(e,l){return ve((y(),E("li",{id:e.id,class:g(e.containerKls),role:"option","aria-disabled":e.isDisabled||void 0,"aria-selected":e.itemSelected,onMousemove:e.hoverItem,onClick:N(e.selectOptionClick,["stop"])},[k(e.$slots,"default",{},()=>[w("span",null,K(e.currentLabel),1)])],42,["id","aria-disabled","aria-selected","onMousemove","onClick"])),[[He,e.visible]])}var qe=be(ql,[["render",Ql],["__file","option.vue"]]);const Xl=ee({name:"ElSelectDropdown",componentName:"ElSelectDropdown",setup(){const e=Ve(ke),l=ae("select"),a=h(()=>e.props.popperClass),r=h(()=>e.props.multiple),o=h(()=>e.props.fitInputWidth),d=V("");function n(){var f;d.value=`${(f=e.selectRef)==null?void 0:f.offsetWidth}px`}return Ge(()=>{n(),Z(e.selectRef,n)}),{ns:l,minWidth:d,popperClass:a,isMultiple:r,isFitInputWidth:o}}});function jl(e,l,a,r,o,d){return y(),E("div",{class:g([e.ns.b("dropdown"),e.ns.is("multiple",e.isMultiple),e.popperClass]),style:le({[e.isFitInputWidth?"width":"minWidth"]:e.minWidth})},[e.$slots.header?(y(),E("div",{key:0,class:g(e.ns.be("dropdown","header"))},[k(e.$slots,"header")],2)):R("v-if",!0),k(e.$slots,"default"),e.$slots.footer?(y(),E("div",{key:1,class:g(e.ns.be("dropdown","footer"))},[k(e.$slots,"footer")],2)):R("v-if",!0)],6)}var Jl=be(Xl,[["render",jl],["__file","select-dropdown.vue"]]);const Yl=(e,l)=>{const{t:a}=Rn(),r=wt(),o=ae("select"),d=ae("input"),n=he({inputValue:"",options:new Map,cachedOptions:new Map,optionValues:[],selected:[],selectionWidth:0,collapseItemWidth:0,selectedLabel:"",hoveringIndex:-1,previousQuery:null,inputHovering:!1,menuVisibleOnFocus:!1,isBeforeHide:!1}),f=V(),m=V(),u=V(),c=V(),i=V(),v=V(),p=V(),S=V(),T=V(),$=V(),P=V(),{isComposing:j,handleCompositionStart:Me,handleCompositionUpdate:Re,handleCompositionEnd:se}=nl({afterComposition:t=>xe(t)}),{wrapperRef:O,isFocused:F,handleBlur:J}=ll(i,{beforeFocus(){return ie.value},afterFocus(){e.automaticDropdown&&!C.value&&(C.value=!0,n.menuVisibleOnFocus=!0)},beforeBlur(t){var s,b;return((s=u.value)==null?void 0:s.isFocusInsideContent(t))||((b=c.value)==null?void 0:b.isFocusInsideContent(t))},afterBlur(){var t;C.value=!1,n.menuVisibleOnFocus=!1,e.validateEvent&&((t=U==null?void 0:U.validate)==null||t.call(U,"blur").catch(s=>ft()))}}),C=V(!1),oe=V(),{form:ge,formItem:U}=Zn(),{inputId:Lt}=_n(e,{formItemContext:U}),{valueOnClear:Dt,isEmptyValue:Bt}=$n(e),ie=h(()=>e.disabled||(ge==null?void 0:ge.disabled)),$e=h(()=>A(e.modelValue)?e.modelValue.length>0:!Bt(e.modelValue)),Nt=h(()=>{var t;return(t=ge==null?void 0:ge.statusIcon)!=null?t:!1}),Pt=h(()=>e.clearable&&!ie.value&&n.inputHovering&&$e.value),Qe=h(()=>e.remote&&e.filterable&&!e.remoteShowSuffix?"":e.suffixIcon),Ft=h(()=>o.is("reverse",!!(Qe.value&&C.value))),Le=h(()=>(U==null?void 0:U.validateState)||""),Wt=h(()=>Le.value&&Ln[Le.value]),At=h(()=>e.remote?300:0),zt=h(()=>e.remote&&!n.inputValue&&n.options.size===0),Kt=h(()=>e.loading?e.loadingText||a("el.select.loading"):e.filterable&&n.inputValue&&n.options.size>0&&re.value===0?e.noMatchText||a("el.select.noMatch"):n.options.size===0?e.noDataText||a("el.select.noData"):null),re=h(()=>L.value.filter(t=>t.visible).length),L=h(()=>{const t=Array.from(n.options.values()),s=[];return n.optionValues.forEach(b=>{const I=t.findIndex(B=>B.value===b);I>-1&&s.push(t[I])}),s.length>=t.length?s:t}),Ht=h(()=>Array.from(n.cachedOptions.values())),Ut=h(()=>{const t=L.value.filter(s=>!s.created).some(s=>s.currentLabel===n.inputValue);return e.filterable&&e.allowCreate&&n.inputValue!==""&&!t}),Xe=()=>{e.filterable&&pe(e.filterMethod)||e.filterable&&e.remote&&pe(e.remoteMethod)||L.value.forEach(t=>{var s;(s=t.updateOption)==null||s.call(t,n.inputValue)})},je=Et(),Gt=h(()=>["small"].includes(je.value)?"small":"default"),qt=h({get(){return C.value&&!zt.value},set(t){C.value=t}}),Qt=h(()=>{if(e.multiple&&!we(e.modelValue))return Q(e.modelValue).length===0&&!n.inputValue;const t=A(e.modelValue)?e.modelValue[0]:e.modelValue;return e.filterable||we(t)?!n.inputValue:!0}),Xt=h(()=>{var t;const s=(t=e.placeholder)!=null?t:a("el.select.placeholder");return e.multiple||!$e.value?s:n.selectedLabel}),jt=h(()=>it?null:"mouseenter");x(()=>e.modelValue,(t,s)=>{e.multiple&&e.filterable&&!e.reserveKeyword&&(n.inputValue="",ye("")),Se(),!me(t,s)&&e.validateEvent&&(U==null||U.validate("change").catch(b=>ft()))},{flush:"post",deep:!0}),x(()=>C.value,t=>{t?ye(n.inputValue):(n.inputValue="",n.previousQuery=null,n.isBeforeHide=!0),l("visible-change",t)}),x(()=>n.options.entries(),()=>{St&&(Se(),e.defaultFirstOption&&(e.filterable||e.remote)&&re.value&&Je())},{flush:"post"}),x([()=>n.hoveringIndex,L],([t])=>{Nn(t)&&t>-1?oe.value=L.value[t]||{}:oe.value={},L.value.forEach(s=>{s.hover=oe.value===s})}),Ct(()=>{n.isBeforeHide||Xe()});const ye=t=>{n.previousQuery===t||j.value||(n.previousQuery=t,e.filterable&&pe(e.filterMethod)?e.filterMethod(t):e.filterable&&e.remote&&pe(e.remoteMethod)&&e.remoteMethod(t),e.defaultFirstOption&&(e.filterable||e.remote)&&re.value?H(Je):H(Jt))},Je=()=>{const t=L.value.filter(B=>B.visible&&!B.disabled&&!B.states.groupDisabled),s=t.find(B=>B.created),b=t[0],I=L.value.map(B=>B.value);n.hoveringIndex=lt(I,s||b)},Se=()=>{if(e.multiple)n.selectedLabel="";else{const s=A(e.modelValue)?e.modelValue[0]:e.modelValue,b=Ye(s);n.selectedLabel=b.currentLabel,n.selected=[b];return}const t=[];we(e.modelValue)||Q(e.modelValue).forEach(s=>{t.push(Ye(s))}),n.selected=t},Ye=t=>{let s;const b=Bn(t);for(let te=n.cachedOptions.size-1;te>=0;te--){const G=Ht.value[te];if(b?_(G.value,e.valueKey)===_(t,e.valueKey):G.value===t){s={value:t,currentLabel:G.currentLabel,get isDisabled(){return G.isDisabled}};break}}if(s)return s;const I=b?t.label:t??"";return{value:t,currentLabel:I}},Jt=()=>{n.hoveringIndex=L.value.findIndex(t=>n.selected.some(s=>Ie(s)===Ie(t)))},Yt=()=>{n.selectionWidth=Number.parseFloat(window.getComputedStyle(m.value).width)},Zt=()=>{n.collapseItemWidth=$.value.getBoundingClientRect().width},De=()=>{var t,s;(s=(t=u.value)==null?void 0:t.updatePopper)==null||s.call(t)},Ze=()=>{var t,s;(s=(t=c.value)==null?void 0:t.updatePopper)==null||s.call(t)},_e=()=>{n.inputValue.length>0&&!C.value&&(C.value=!0),ye(n.inputValue)},xe=t=>{if(n.inputValue=t.target.value,e.remote)et();else return _e()},et=Dl(()=>{_e()},At.value),ue=t=>{me(e.modelValue,t)||l(Tt,t)},_t=t=>Bl(t,s=>{const b=n.cachedOptions.get(s);return b&&!b.disabled&&!b.states.groupDisabled}),xt=t=>{if(e.multiple&&t.code!==Dn.delete&&t.target.value.length<=0){const s=Q(e.modelValue).slice(),b=_t(s);if(b<0)return;const I=s[b];s.splice(b,1),l(ne,s),ue(s),l("remove-tag",I)}},en=(t,s)=>{const b=n.selected.indexOf(s);if(b>-1&&!ie.value){const I=Q(e.modelValue).slice();I.splice(b,1),l(ne,I),ue(I),l("remove-tag",s.value)}t.stopPropagation(),Oe()},tt=t=>{t.stopPropagation();const s=e.multiple?[]:Dt.value;if(e.multiple)for(const b of n.selected)b.isDisabled&&s.push(b.value);l(ne,s),ue(s),n.hoveringIndex=-1,C.value=!1,l("clear"),Oe()},nt=t=>{var s;if(e.multiple){const b=Q((s=e.modelValue)!=null?s:[]).slice(),I=lt(b,t);I>-1?b.splice(I,1):(e.multipleLimit<=0||b.length<e.multipleLimit)&&b.push(t.value),l(ne,b),ue(b),t.created&&ye(""),e.filterable&&!e.reserveKeyword&&(n.inputValue="")}else l(ne,t.value),ue(t.value),C.value=!1;Oe(),!C.value&&H(()=>{Ce(t)})},lt=(t,s)=>we(s)?-1:X(s.value)?t.findIndex(b=>me(_(b,e.valueKey),Ie(s))):t.indexOf(s.value),Ce=t=>{var s,b,I,B,te;const G=A(t)?t[0]:t;let Ee=null;if(G!=null&&G.value){const ce=L.value.filter(gn=>gn.value===G.value);ce.length>0&&(Ee=ce[0].$el)}if(u.value&&Ee){const ce=(B=(I=(b=(s=u.value)==null?void 0:s.popperRef)==null?void 0:b.contentRef)==null?void 0:I.querySelector)==null?void 0:B.call(I,`.${o.be("dropdown","wrap")}`);ce&&sl(ce,Ee)}(te=P.value)==null||te.handleScroll()},tn=t=>{n.options.set(t.value,t),n.cachedOptions.set(t.value,t)},nn=(t,s)=>{n.options.get(t)===s&&n.options.delete(t)},ln=h(()=>{var t,s;return(s=(t=u.value)==null?void 0:t.popperRef)==null?void 0:s.contentRef}),an=()=>{n.isBeforeHide=!1,H(()=>{var t;(t=P.value)==null||t.update(),Ce(n.selected)})},Oe=()=>{var t;(t=i.value)==null||t.focus()},sn=()=>{var t;if(C.value){C.value=!1,H(()=>{var s;return(s=i.value)==null?void 0:s.blur()});return}(t=i.value)==null||t.blur()},on=t=>{tt(t)},rn=t=>{if(C.value=!1,F.value){const s=new FocusEvent("focus",t);H(()=>J(s))}},un=()=>{n.inputValue.length>0?n.inputValue="":C.value=!1},at=()=>{ie.value||(it&&(n.inputHovering=!0),n.menuVisibleOnFocus?n.menuVisibleOnFocus=!1:C.value=!C.value)},cn=()=>{if(!C.value)at();else{const t=L.value[n.hoveringIndex];t&&!t.isDisabled&&nt(t)}},Ie=t=>X(t.value)?_(t.value,e.valueKey):t.value,dn=h(()=>L.value.filter(t=>t.visible).every(t=>t.isDisabled)),fn=h(()=>e.multiple?e.collapseTags?n.selected.slice(0,e.maxCollapseTags):n.selected:[]),pn=h(()=>e.multiple?e.collapseTags?n.selected.slice(e.maxCollapseTags):[]:[]),st=t=>{if(!C.value){C.value=!0;return}if(!(n.options.size===0||re.value===0||j.value)&&!dn.value){t==="next"?(n.hoveringIndex++,n.hoveringIndex===n.options.size&&(n.hoveringIndex=0)):t==="prev"&&(n.hoveringIndex--,n.hoveringIndex<0&&(n.hoveringIndex=n.options.size-1));const s=L.value[n.hoveringIndex];(s.isDisabled||!s.visible)&&st(t),H(()=>Ce(oe.value))}},vn=()=>{if(!m.value)return 0;const t=window.getComputedStyle(m.value);return Number.parseFloat(t.gap||"6px")},mn=h(()=>{const t=vn();return{maxWidth:`${$.value&&e.maxCollapseTags===1?n.selectionWidth-n.collapseItemWidth-t:n.selectionWidth}px`}}),hn=h(()=>({maxWidth:`${n.selectionWidth}px`})),bn=t=>{l("popup-scroll",t)};return Z(m,Yt),Z(S,De),Z(O,De),Z(T,Ze),Z($,Zt),Ge(()=>{Se()}),{inputId:Lt,contentId:r,nsSelect:o,nsInput:d,states:n,isFocused:F,expanded:C,optionsArray:L,hoverOption:oe,selectSize:je,filteredOptionsCount:re,updateTooltip:De,updateTagTooltip:Ze,debouncedOnInputChange:et,onInput:xe,deletePrevTag:xt,deleteTag:en,deleteSelected:tt,handleOptionSelect:nt,scrollToOption:Ce,hasModelValue:$e,shouldShowPlaceholder:Qt,currentPlaceholder:Xt,mouseEnterEventName:jt,needStatusIcon:Nt,showClose:Pt,iconComponent:Qe,iconReverse:Ft,validateState:Le,validateIcon:Wt,showNewOption:Ut,updateOptions:Xe,collapseTagSize:Gt,setSelected:Se,selectDisabled:ie,emptyText:Kt,handleCompositionStart:Me,handleCompositionUpdate:Re,handleCompositionEnd:se,onOptionCreate:tn,onOptionDestroy:nn,handleMenuEnter:an,focus:Oe,blur:sn,handleClearClick:on,handleClickOutside:rn,handleEsc:un,toggleMenu:at,selectOption:cn,getValueKey:Ie,navigateOptions:st,dropdownMenuVisible:qt,showTagList:fn,collapseTagList:pn,popupScroll:bn,tagStyle:mn,collapseTagStyle:hn,popperRef:ln,inputRef:i,tooltipRef:u,tagTooltipRef:c,prefixRef:v,suffixRef:p,selectRef:f,wrapperRef:O,selectionRef:m,scrollbarRef:P,menuRef:S,tagMenuRef:T,collapseItemRef:$}};var Zl=ee({name:"ElOptions",setup(e,{slots:l}){const a=Ve(ke);let r=[];return()=>{var o,d;const n=(o=l.default)==null?void 0:o.call(l),f=[];function m(u){A(u)&&u.forEach(c=>{var i,v,p,S;const T=(i=(c==null?void 0:c.type)||{})==null?void 0:i.name;T==="ElOptionGroup"?m(!Pn(c.children)&&!A(c.children)&&pe((v=c.children)==null?void 0:v.default)?(p=c.children)==null?void 0:p.default():c.children):T==="ElOption"?f.push((S=c.props)==null?void 0:S.value):A(c.children)&&m(c.children)})}return n.length&&m((d=n[0])==null?void 0:d.children),me(f,r)||(r=f,a&&(a.states.optionValues=f)),n}}});const _l=ze({name:String,id:String,modelValue:{type:de([Array,String,Number,Boolean,Object]),default:void 0},autocomplete:{type:String,default:"off"},automaticDropdown:Boolean,size:zn,effect:{type:de(String),default:"light"},disabled:Boolean,clearable:Boolean,filterable:Boolean,allowCreate:Boolean,loading:Boolean,popperClass:{type:String,default:""},popperOptions:{type:de(Object),default:()=>({})},remote:Boolean,loadingText:String,noMatchText:String,noDataText:String,remoteMethod:Function,filterMethod:Function,multiple:Boolean,multipleLimit:{type:Number,default:0},placeholder:{type:String},defaultFirstOption:Boolean,reserveKeyword:{type:Boolean,default:!0},valueKey:{type:String,default:"value"},collapseTags:Boolean,collapseTagsTooltip:Boolean,maxCollapseTags:{type:Number,default:1},teleported:dt.teleported,persistent:{type:Boolean,default:!0},clearIcon:{type:rt,default:An},fitInputWidth:Boolean,suffixIcon:{type:rt,default:Wn},tagType:{...We.type,default:"info"},tagEffect:{...We.effect,default:"light"},validateEvent:{type:Boolean,default:!0},remoteShowSuffix:Boolean,showArrow:{type:Boolean,default:!0},offset:{type:Number,default:12},placement:{type:de(String),values:Xn,default:"bottom-start"},fallbackPlacements:{type:de(Array),default:["bottom-start","top-start","right","left"]},tabindex:{type:[String,Number],default:0},appendTo:dt.appendTo,...Fn,...al(["ariaLabel"])});jn.scroll;const ht="ElSelect",xl=ee({name:ht,componentName:ht,components:{ElSelectMenu:Jl,ElOption:qe,ElOptions:Zl,ElTag:zl,ElScrollbar:Yn,ElTooltip:Jn,ElIcon:Pe},directives:{ClickOutside:Kl},props:_l,emits:[ne,Tt,"remove-tag","clear","visible-change","focus","blur","popup-scroll"],setup(e,{emit:l,slots:a}){const r=h(()=>{const{modelValue:c,multiple:i}=e,v=i?[]:void 0;return A(c)?i?c:v:i?v:c}),o=he({...Ue(e),modelValue:r}),d=Yl(o,l),{calculatorRef:n,inputStyle:f}=Hl(),m=c=>{ol(c).filter(v=>X(v)&&v.type.name==="ElOption").forEach(v=>{const p={...v.props};p.currentLabel=p.label||(X(p.value)?"":p.value),d.onOptionCreate(p)})};Ct(()=>{e.persistent||H(()=>{var c,i;const v=Gn(Fe,(i=(c=a.default)==null?void 0:c.call(a))!=null?i:[]).children;m(v)})}),Ot(ke,he({props:o,states:d.states,selectRef:d.selectRef,optionsArray:d.optionsArray,setSelected:d.setSelected,handleOptionSelect:d.handleOptionSelect,onOptionCreate:d.onOptionCreate,onOptionDestroy:d.onOptionDestroy}));const u=h(()=>e.multiple?d.states.selected.map(c=>c.currentLabel):d.states.selectedLabel);return{...d,modelValue:r,selectedLabel:u,calculatorRef:n,inputStyle:f}}});function ea(e,l){const a=Y("el-tag"),r=Y("el-tooltip"),o=Y("el-icon"),d=Y("el-option"),n=Y("el-options"),f=Y("el-scrollbar"),m=Y("el-select-menu"),u=Kn("click-outside");return ve((y(),E("div",{ref:"selectRef",class:g([e.nsSelect.b(),e.nsSelect.m(e.selectSize)]),[Un(e.mouseEnterEventName)]:c=>e.states.inputHovering=!0,onMouseleave:c=>e.states.inputHovering=!1},[z(r,{ref:"tooltipRef",visible:e.dropdownMenuVisible,placement:e.placement,teleported:e.teleported,"popper-class":[e.nsSelect.e("popper"),e.popperClass],"popper-options":e.popperOptions,"fallback-placements":e.fallbackPlacements,effect:e.effect,pure:"",trigger:"click",transition:`${e.nsSelect.namespace.value}-zoom-in-top`,"stop-popper-mouse-event":!1,"gpu-acceleration":!1,persistent:e.persistent,"append-to":e.appendTo,"show-arrow":e.showArrow,offset:e.offset,onBeforeShow:e.handleMenuEnter,onHide:c=>e.states.isBeforeHide=!1},{default:M(()=>{var c;return[w("div",{ref:"wrapperRef",class:g([e.nsSelect.e("wrapper"),e.nsSelect.is("focused",e.isFocused),e.nsSelect.is("hovering",e.states.inputHovering),e.nsSelect.is("filterable",e.filterable),e.nsSelect.is("disabled",e.selectDisabled)]),onClick:N(e.toggleMenu,["prevent"])},[e.$slots.prefix?(y(),E("div",{key:0,ref:"prefixRef",class:g(e.nsSelect.e("prefix"))},[k(e.$slots,"prefix")],2)):R("v-if",!0),w("div",{ref:"selectionRef",class:g([e.nsSelect.e("selection"),e.nsSelect.is("near",e.multiple&&!e.$slots.prefix&&!!e.states.selected.length)])},[e.multiple?k(e.$slots,"tag",{key:0},()=>[(y(!0),E(Fe,null,ut(e.showTagList,i=>(y(),E("div",{key:e.getValueKey(i),class:g(e.nsSelect.e("selected-item"))},[z(a,{closable:!e.selectDisabled&&!i.isDisabled,size:e.collapseTagSize,type:e.tagType,effect:e.tagEffect,"disable-transitions":"",style:le(e.tagStyle),onClose:v=>e.deleteTag(v,i)},{default:M(()=>[w("span",{class:g(e.nsSelect.e("tags-text"))},[k(e.$slots,"label",{label:i.currentLabel,value:i.value},()=>[ct(K(i.currentLabel),1)])],2)]),_:2},1032,["closable","size","type","effect","style","onClose"])],2))),128)),e.collapseTags&&e.states.selected.length>e.maxCollapseTags?(y(),W(r,{key:0,ref:"tagTooltipRef",disabled:e.dropdownMenuVisible||!e.collapseTagsTooltip,"fallback-placements":["bottom","top","right","left"],effect:e.effect,placement:"bottom",teleported:e.teleported},{default:M(()=>[w("div",{ref:"collapseItemRef",class:g(e.nsSelect.e("selected-item"))},[z(a,{closable:!1,size:e.collapseTagSize,type:e.tagType,effect:e.tagEffect,"disable-transitions":"",style:le(e.collapseTagStyle)},{default:M(()=>[w("span",{class:g(e.nsSelect.e("tags-text"))}," + "+K(e.states.selected.length-e.maxCollapseTags),3)]),_:1},8,["size","type","effect","style"])],2)]),content:M(()=>[w("div",{ref:"tagMenuRef",class:g(e.nsSelect.e("selection"))},[(y(!0),E(Fe,null,ut(e.collapseTagList,i=>(y(),E("div",{key:e.getValueKey(i),class:g(e.nsSelect.e("selected-item"))},[z(a,{class:"in-tooltip",closable:!e.selectDisabled&&!i.isDisabled,size:e.collapseTagSize,type:e.tagType,effect:e.tagEffect,"disable-transitions":"",onClose:v=>e.deleteTag(v,i)},{default:M(()=>[w("span",{class:g(e.nsSelect.e("tags-text"))},[k(e.$slots,"label",{label:i.currentLabel,value:i.value},()=>[ct(K(i.currentLabel),1)])],2)]),_:2},1032,["closable","size","type","effect","onClose"])],2))),128))],2)]),_:3},8,["disabled","effect","teleported"])):R("v-if",!0)]):R("v-if",!0),w("div",{class:g([e.nsSelect.e("selected-item"),e.nsSelect.e("input-wrapper"),e.nsSelect.is("hidden",!e.filterable)])},[ve(w("input",{id:e.inputId,ref:"inputRef","onUpdate:modelValue":i=>e.states.inputValue=i,type:"text",name:e.name,class:g([e.nsSelect.e("input"),e.nsSelect.is(e.selectSize)]),disabled:e.selectDisabled,autocomplete:e.autocomplete,style:le(e.inputStyle),tabindex:e.tabindex,role:"combobox",readonly:!e.filterable,spellcheck:"false","aria-activedescendant":((c=e.hoverOption)==null?void 0:c.id)||"","aria-controls":e.contentId,"aria-expanded":e.dropdownMenuVisible,"aria-label":e.ariaLabel,"aria-autocomplete":"none","aria-haspopup":"listbox",onKeydown:[fe(N(i=>e.navigateOptions("next"),["stop","prevent"]),["down"]),fe(N(i=>e.navigateOptions("prev"),["stop","prevent"]),["up"]),fe(N(e.handleEsc,["stop","prevent"]),["esc"]),fe(N(e.selectOption,["stop","prevent"]),["enter"]),fe(N(e.deletePrevTag,["stop"]),["delete"])],onCompositionstart:e.handleCompositionStart,onCompositionupdate:e.handleCompositionUpdate,onCompositionend:e.handleCompositionEnd,onInput:e.onInput,onClick:N(e.toggleMenu,["stop"])},null,46,["id","onUpdate:modelValue","name","disabled","autocomplete","tabindex","readonly","aria-activedescendant","aria-controls","aria-expanded","aria-label","onKeydown","onCompositionstart","onCompositionupdate","onCompositionend","onInput","onClick"]),[[Hn,e.states.inputValue]]),e.filterable?(y(),E("span",{key:0,ref:"calculatorRef","aria-hidden":"true",class:g(e.nsSelect.e("input-calculator")),textContent:K(e.states.inputValue)},null,10,["textContent"])):R("v-if",!0)],2),e.shouldShowPlaceholder?(y(),E("div",{key:1,class:g([e.nsSelect.e("selected-item"),e.nsSelect.e("placeholder"),e.nsSelect.is("transparent",!e.hasModelValue||e.expanded&&!e.states.inputValue)])},[e.hasModelValue?k(e.$slots,"label",{key:0,label:e.currentPlaceholder,value:e.modelValue},()=>[w("span",null,K(e.currentPlaceholder),1)]):(y(),E("span",{key:1},K(e.currentPlaceholder),1))],2)):R("v-if",!0)],2),w("div",{ref:"suffixRef",class:g(e.nsSelect.e("suffix"))},[e.iconComponent&&!e.showClose?(y(),W(o,{key:0,class:g([e.nsSelect.e("caret"),e.nsSelect.e("icon"),e.iconReverse])},{default:M(()=>[(y(),W(Be(e.iconComponent)))]),_:1},8,["class"])):R("v-if",!0),e.showClose&&e.clearIcon?(y(),W(o,{key:1,class:g([e.nsSelect.e("caret"),e.nsSelect.e("icon"),e.nsSelect.e("clear")]),onClick:e.handleClearClick},{default:M(()=>[(y(),W(Be(e.clearIcon)))]),_:1},8,["class","onClick"])):R("v-if",!0),e.validateState&&e.validateIcon&&e.needStatusIcon?(y(),W(o,{key:2,class:g([e.nsInput.e("icon"),e.nsInput.e("validateIcon"),e.nsInput.is("loading",e.validateState==="validating")])},{default:M(()=>[(y(),W(Be(e.validateIcon)))]),_:1},8,["class"])):R("v-if",!0)],2)],10,["onClick"])]}),content:M(()=>[z(m,{ref:"menuRef"},{default:M(()=>[e.$slots.header?(y(),E("div",{key:0,class:g(e.nsSelect.be("dropdown","header")),onClick:N(()=>{},["stop"])},[k(e.$slots,"header")],10,["onClick"])):R("v-if",!0),ve(z(f,{id:e.contentId,ref:"scrollbarRef",tag:"ul","wrap-class":e.nsSelect.be("dropdown","wrap"),"view-class":e.nsSelect.be("dropdown","list"),class:g([e.nsSelect.is("empty",e.filteredOptionsCount===0)]),role:"listbox","aria-label":e.ariaLabel,"aria-orientation":"vertical",onScroll:e.popupScroll},{default:M(()=>[e.showNewOption?(y(),W(d,{key:0,value:e.states.inputValue,created:!0},null,8,["value"])):R("v-if",!0),z(n,null,{default:M(()=>[k(e.$slots,"default")]),_:3})]),_:3},8,["id","wrap-class","view-class","class","aria-label","onScroll"]),[[He,e.states.options.size>0&&!e.loading]]),e.$slots.loading&&e.loading?(y(),E("div",{key:1,class:g(e.nsSelect.be("dropdown","loading"))},[k(e.$slots,"loading")],2)):e.loading||e.filteredOptionsCount===0?(y(),E("div",{key:2,class:g(e.nsSelect.be("dropdown","empty"))},[k(e.$slots,"empty",{},()=>[w("span",null,K(e.emptyText),1)])],2)):R("v-if",!0),e.$slots.footer?(y(),E("div",{key:3,class:g(e.nsSelect.be("dropdown","footer")),onClick:N(()=>{},["stop"])},[k(e.$slots,"footer")],10,["onClick"])):R("v-if",!0)]),_:3},512)]),_:3},8,["visible","placement","teleported","popper-class","popper-options","fallback-placements","effect","transition","persistent","append-to","show-arrow","offset","onBeforeShow","onHide"])],16,["onMouseleave"])),[[u,e.handleClickOutside,e.popperRef]])}var ta=be(xl,[["render",ea],["__file","select.vue"]]);const na=ee({name:"ElOptionGroup",componentName:"ElOptionGroup",props:{label:String,disabled:Boolean},setup(e){const l=ae("select"),a=V(),r=Ke(),o=V([]);Ot(Rt,he({...Ue(e)}));const d=h(()=>o.value.some(u=>u.visible===!0)),n=u=>{var c;return u.type.name==="ElOption"&&!!((c=u.component)!=null&&c.proxy)},f=u=>{const c=Q(u),i=[];return c.forEach(v=>{var p;Qn(v)&&(n(v)?i.push(v.component.proxy):A(v.children)&&v.children.length?i.push(...f(v.children)):(p=v.component)!=null&&p.subTree&&i.push(...f(v.component.subTree)))}),i},m=()=>{o.value=f(r.subTree)};return Ge(()=>{m()}),qn(a,m,{attributes:!0,subtree:!0,childList:!0}),{groupRef:a,visible:d,ns:l}}});function la(e,l,a,r,o,d){return ve((y(),E("ul",{ref:"groupRef",class:g(e.ns.be("group","wrap"))},[w("li",{class:g(e.ns.be("group","title"))},K(e.label),3),w("li",null,[w("ul",{class:g(e.ns.b("group"))},[k(e.$slots,"default")],2)])],2)),[[He,e.visible]])}var $t=be(na,[["render",la],["__file","option-group.vue"]]);const pa=yt(ta,{Option:qe,OptionGroup:$t}),va=It(qe);It($t);export{Kl as C,pa as E,va as a,zl as b,fa as c,Dl as d,Ml as e,ke as s};
