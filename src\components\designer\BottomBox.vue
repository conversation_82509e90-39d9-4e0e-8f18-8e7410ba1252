<template>
	<div class="fw-box">
	    <div class="title-box">
			<div class="title-able">Console--区域</div>
			<div class="btn-box" style="margin-left:30px;" @click.stop="onClearLog()">
				<el-icon> <Delete /> </el-icon>
				<div class="btn-text">清除</div>
			</div>
			<div class="btn-box" @click.stop="onCloseBlock()">
				<el-icon> <Close /> </el-icon>
				<div class="btn-text">关闭</div>
			</div>
			<div style="flex:1;"></div>
			<div class="btn-stop" @click.stop="ProjectRef.funcObj.stopCompile()" v-if="compileStatus">
				<div class="btn-stop-img-out">
					<div class="btn-stop-img-in"></div>
				</div>
				<div class="btn-stop-label">停止编译</div>
			</div>
		</div>
		<LogMgr />
	</div>
</template>

<script lang="ts" setup>
	import { reactive,ref,inject,onMounted,onUnmounted } from 'vue';
	import LogMgr from './bottomChild/LogMgr.vue';
	const ProjectRef = inject("ProjectRef")
	
	let compileStatus = ref(false)
	
	ProjectRef.funcObj.otherFunc.updateCompileStatus = (status:boolean)=>{
		compileStatus.value = status
	}

	function onCloseBlock(){
		ProjectRef.funcObj.setBlockVisible('bottom')
	}
	
	function onClearLog(){
		ProjectRef.funcObj.clearLog()
	}
	
	
	
</script>

<style lang="scss" scoped>
	.fw-box{
		background-color: #F5F5F5;
		user-select: none; /* 标准语法 */
		overflow-y: hidden;
		.title-box{
			background-color: white;
			padding:8px 40px 8px 16px;
			display: flex;
			justify-content: space-between;
			align-items: center;
			white-space: nowrap;
			.title-able{
				font-size: 13px;
			}
			.btn-box{
				display: flex;
				justify-content: center;
				align-items: center;
				margin-left:20px;
				font-size: 13px;
				cursor: pointer;
				color:#999999;
				
				.btn-text{
					margin-left: 2px;
					
				}
			}
			.btn-box:hover{
				color:#878787;
			}
			.btn-stop {
				display: flex;
				justify-content: flex-start;
				align-items: center;
				cursor: pointer;
				
				.btn-stop-img-out{
					display: flex;
					justify-content: center;
					align-items: center;
					width:18px;
					height:18px;
					border-radius: 15px;
					border: 1px solid #bfbfbf;
					
					.btn-stop-img-in{
						width:8px;
						height:8px;
						background-color: #ff1111;
					}
				}
				.btn-stop-label{
					font-size: 12px; 
					color: #a8a8a8;
					margin-left: 5px;
				}
			}
			.btn-stop:active{
				transform: scale(0.98);
			}
		}
	}
</style>