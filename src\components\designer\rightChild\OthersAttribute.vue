<template>
	<div class="content">
		<div class="attributStyle" v-for="(objKey,index) in Object.keys(props.others)" :key="index" >
			<div class="valid-panel" v-if="props.others[objKey].type != 'Array'">
				<div class="item-label">{{props.others[objKey].label}}</div>
				<div style="color: #808080;">:</div>
				<el-input class="item-value" v-model="props.others[objKey].value"  v-if="props.others[objKey].type=='String'"/>
				<el-input-number  class="item-value common-input" v-model="props.others[objKey].value" :controls="false"  :precision="0"  v-if="props.others[objKey].type=='Number'"/>
				<el-switch class="item-value" v-model="props.others[objKey].value" size="small" v-if="props.others[objKey].type=='Boolean'"/>
				<el-select class="item-value" v-model="props.others[objKey].value" placeholder="请选择"  v-if="props.others[objKey].type=='List'">
					<el-option v-for="childItem in props.others[objKey].listData" :key="childItem.value" :label="childItem.name" :value="childItem.value" />
				</el-select>
				<div class="item-value" v-if="props.others[objKey].type=='Object'" >{{ props.others[objKey].value }}</div>
			</div>
			<el-icon class="expression-icon" v-if="props.others[objKey].type=='Object'"
				@click="expressionMgr.handleOpenExpression(props.others[objKey],'value','valueVar','')">
				<DocumentChecked color="#409eff"  v-if="props.others[objKey].value && props.others[objKey].value.length > 0"/>
				<DocumentDelete v-else />
			</el-icon>
			<el-icon class="expression-icon" v-if="props.others[objKey].hasOwnProperty('expression')"
				@click="expressionMgr.handleOpenExpression(props.others[objKey],'expression','valueVar','')">
				<DocumentChecked color="#409eff"  v-if="props.others[objKey].expression && props.others[objKey].expression.length > 0"/>
				<DocumentDelete v-else />
			</el-icon>
			<div v-if="props.others[objKey].type =='Array'"> 
                 
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
	import {reactive,ref,onMounted,inject,toRaw} from "vue";
	
	const props = defineProps({
		others:{
			type:Object,
			default:{}
		},
		expressionMgr:{
			type:Object,
			default:{
				handleOpenExpression : (parent,key,openType,returnType)=>{}
			}
		}
	})
	
</script>

<style lang="scss" scoped>
	 .content{
		 padding-bottom: 10px;
		 margin-top:10px;
		 .attributStyle{
			 display: flex;
			 justify-content: flex-start;
			 align-items: center;
			 width:calc(100% - 4px);
			 
			 .valid-panel{
				 display: flex;
				 justify-content: space-between;
				 align-items: center;
				 width:calc(100% - 26px);
				 
				 .item-label{
					 min-width:60px;
					 font-size: 13px;
					 text-align: left;
					 color: #808080;
				 }
				 .item-value{
					 flex:1;
					 margin: 5px;
					 text-align: left;
				 }
				 :deep(.el-input-number .el-input__inner) {
					text-align: left;
				}
			 }
			 .expression-icon {
			 	margin-left: 8px;
			 	color: #808080;
			 	cursor: pointer;
				width:20px;
				height:20px;
			 }
		 }
	 }
</style>