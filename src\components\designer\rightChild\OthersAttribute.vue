<template>
	<div class="content">
		<div class="attributStyle" v-for="(objKey,index) in Object.keys(props.others)" :key="index" >
			<div class="valid-panel" v-if="props.others[objKey].type != 'Array'">
				<div class="item-label">{{props.others[objKey].label}}</div>
				<div style="color: #808080;">:</div>
				<el-input class="item-value" v-model="props.others[objKey].value"  v-if="props.others[objKey].type=='String'"/>
				<el-input-number  class="item-value common-input" v-model="props.others[objKey].value" :controls="false"  :precision="0"  v-if="props.others[objKey].type=='Number'"/>
				<el-switch class="item-value" v-model="props.others[objKey].value" size="small" v-if="props.others[objKey].type=='Boolean'"/>
				<el-select class="item-value" v-model="props.others[objKey].value" placeholder="请选择"  v-if="props.others[objKey].type=='List'">
					<el-option v-for="childItem in props.others[objKey].listData" :key="childItem.value" :label="childItem.name" :value="childItem.value" />
				</el-select>
				<div class="item-value" v-if="props.others[objKey].type=='Object'" >{{ props.others[objKey].value }}</div>
			</div>
			<el-icon class="expression-icon" v-if="props.others[objKey].type=='Object'"
				@click="expressionMgr.handleOpenExpression(props.others[objKey],'value','valueVar','')">
				<DocumentChecked color="#409eff"  v-if="props.others[objKey].value && props.others[objKey].value.length > 0"/>
				<DocumentDelete v-else />
			</el-icon>
			<el-icon class="expression-icon" v-if="props.others[objKey].hasOwnProperty('expression')"
				@click="expressionMgr.handleOpenExpression(props.others[objKey],'expression','valueVar','')">
				<DocumentChecked color="#409eff"  v-if="props.others[objKey].expression && props.others[objKey].expression.length > 0"/>
				<DocumentDelete v-else />
			</el-icon>
			<div v-if="props.others[objKey].type =='Array'" class="array-container">
				<div class="array-header">
					<div class="item-label">{{props.others[objKey].label}}</div>
				</div>
				<div class="array-items">
					<div
						v-for="(item, itemIndex) in props.others[objKey].items"
						:key="itemIndex"
						class="array-item-row">
						<div
							v-for="(formatField, fieldKey) in props.others[objKey].format"
							:key="fieldKey"
							class="array-field">
							<el-input
								v-if="formatField.type === 'String'"
								v-model="props.others[objKey].items[itemIndex][fieldKey]"
								class="field-input"
								:placeholder="formatField.label || fieldKey"/>
							<el-input-number
								v-else-if="formatField.type === 'Number'"
								v-model="props.others[objKey].items[itemIndex][fieldKey]"
								class="field-input common-input"
								:controls="false"
								:precision="0"/>
							<el-switch
								v-else-if="formatField.type === 'Boolean'"
								v-model="props.others[objKey].items[itemIndex][fieldKey]"
								class="field-input"
								size="small"/>
						</div>
						<div class="array-buttons">
							<el-button
								v-if="props.others[objKey].addBtn !== false"
								type="primary"
								size="small"
								@click="addArrayItem(objKey)"
								:icon="Plus">
							</el-button>
							<el-button
								v-if="props.others[objKey].delBtn !== false"
								type="danger"
								size="small"
								@click="removeArrayItem(objKey, itemIndex)"
								:icon="Close">
							</el-button>
						</div>
					</div>
					<div v-if="!props.others[objKey].items || props.others[objKey].items.length === 0" class="array-empty-row">
						<div class="array-empty-text">暂无数据</div>
						<el-button
							v-if="props.others[objKey].addBtn !== false"
							type="primary"
							size="small"
							@click="addArrayItem(objKey)"
							:icon="Plus">
							添加
						</el-button>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
	import {reactive,ref,onMounted,inject,toRaw} from "vue";
	import { Plus, Close } from '@element-plus/icons-vue';
	
	const props = defineProps({
		others:{
			type:Object,
			default:{}
		},
		expressionMgr:{
			type:Object,
			default:{
				handleOpenExpression : (parent,key,openType,returnType)=>{}
			}
		}
	})

	// 添加数组项
	const addArrayItem = (objKey: string) => {
		if (!props.others[objKey].items) {
			props.others[objKey].items = [];
		}
		// 根据format创建新的对象项
		const newItem: any = {};
		if (props.others[objKey].format) {
			Object.keys(props.others[objKey].format).forEach(fieldKey => {
				const formatField = props.others[objKey].format[fieldKey];
				// 根据字段类型设置默认值
				if (formatField.type === 'String') {
					newItem[fieldKey] = formatField.defaultValue || '';
				} else if (formatField.type === 'Number') {
					newItem[fieldKey] = formatField.defaultValue || 0;
				} else if (formatField.type === 'Boolean') {
					newItem[fieldKey] = formatField.defaultValue || false;
				} else {
					newItem[fieldKey] = formatField.defaultValue || '';
				}
			});
		}
		props.others[objKey].items.push(newItem);
	}

	// 删除数组项
	const removeArrayItem = (objKey: string, itemIndex: number) => {
		if (props.others[objKey].items && itemIndex >= 0 && itemIndex < props.others[objKey].items.length) {
			props.others[objKey].items.splice(itemIndex, 1);
		}
	}

</script>

<style lang="scss" scoped>
	 .content{
		 padding-bottom: 10px;
		 margin-top:10px;
		 .attributStyle{
			 display: flex;
			 justify-content: flex-start;
			 align-items: center;
			 width:calc(100% - 4px);
			 
			 .valid-panel{
				 display: flex;
				 justify-content: space-between;
				 align-items: center;
				 width:calc(100% - 26px);
				 
				 .item-label{
					 min-width:60px;
					 font-size: 13px;
					 text-align: left;
					 color: #808080;
				 }
				 .item-value{
					 flex:1;
					 margin: 5px;
					 text-align: left;
				 }
				 :deep(.el-input-number .el-input__inner) {
					text-align: left;
				}
			 }
			 .expression-icon {
			 	margin-left: 8px;
			 	color: #808080;
			 	cursor: pointer;
				width:20px;
				height:20px;
			 }
		 }

		 // Array 类型样式
		 .array-container {
			 width: 100%;
			 margin: 5px 0;

			 .array-header {
				 margin-bottom: 8px;

				 .item-label {
					 font-size: 13px;
					 color: #808080;
				 }
			 }

			 .array-items {
				 .array-item-row {
					 display: flex;
					 align-items: center;
					 margin-bottom: 5px;
					 gap: 8px;

					 .array-field {
						 flex: 1;

						 .field-input {
							 width: 100%;
						 }
					 }

					 .array-buttons {
						 display: flex;
						 gap: 4px;
						 flex-shrink: 0;
					 }
				 }

				 .array-empty-row {
					 display: flex;
					 align-items: center;
					 justify-content: space-between;
					 padding: 10px;
					 border: 1px dashed #dcdfe6;
					 border-radius: 4px;

					 .array-empty-text {
						 color: #909399;
						 font-size: 12px;
					 }
				 }
			 }
		 }
	 }
</style>