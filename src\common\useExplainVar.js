import { ElMessage } from 'element-plus'
import {reactive,toRaw} from 'vue'

export function useExplainVar(){

   function parse(varStr,category){
	   const strMap = new Map
	   varStr = varStr.replaceAll("'",'"')
	   let delStrResult = doBlack(varStr,'"','"',strMap,"@@","@@","",0)
	   if(delStrResult.indexOf("'") >= 0){
		   ElMessage({
			   message:"字符串不能用单引号[\']表示，必须用双引号[\"]表示",
			   type:"error"
		   })
		   return null
	   }
	   let secResult = doBlack(delStrResult,"{","}",strMap,"@#","#@","",0)
	   let midResult = doBlack(secResult,"[","]",strMap,"@%","%@","",0)
	   let statements = midResult.replaceAll("\t","").replaceAll("\n",";").split(";")
	   // console.log(statements)
	   let resultObj = []
	   for(let n=0;n<statements.length;n++){
		   if(statements[n].trim() != "" && statements[n].substring(0,2) !='//'){
		       let firstArray = statements[n].trim().split("=")
			   let secondArray = firstArray[0].trim().split(' ')
			   if(secondArray.length != 2){
				   ElMessage({
					   message:"变量定义 [ "+statements[n]+" ] 的格式异常",
					   type:"error"
				   })
				   return null
			   }
			   let varObj = {
					   name:secondArray.slice(-1)[0],
					   type:secondArray[0],
					   value:(category=='global' ? 'ref:GlobalData.' : ( category=='component' ? 'ref:Component.' : 'ref:PageData.')) + secondArray.slice(-1)[0],
				   }
			   if(secondArray[0]=='number'){
				   varObj.default = firstArray.length > 1 ?  parseFloat(firstArray[1]) : 0
				   resultObj.push(varObj)
			   }else if(secondArray[0]=='string'){
				   if(firstArray.length > 1 && strMap.has(firstArray[1].trim())){
					   varObj.default = strMap.get(firstArray[1].trim())
				   }else{
					   varObj.default = ""
				   }
				   resultObj.push(varObj)
			   }else if(secondArray[0]=='boolean'){
				   varObj.default = firstArray.length > 1 && firstArray[1].trim()=='true'
				   resultObj.push(varObj)
			   }else if(secondArray[0]=='array'){
				   if(firstArray.length > 1 && strMap.has(firstArray[1].trim())){
					   let tmpValue = firstArray[1].trim()
					   if(tmpValue.substring(0,2)=='@%' && tmpValue.slice(-2)=='%@' && strMap.has(tmpValue)){
						   tmpValue = strMap.get(firstArray[1].trim())
						   varObj.children = formatArrayObj(tmpValue,strMap,varObj.value)
						   varObj.default = []
						//  解析组件数组对象到默认值
						   if(varObj.children && varObj.children.length > 0) {
							varObj.children.forEach(element => {
								let obj = {}
								if(element.children && element.children.length > 0) {
									element.children.forEach(ele => {
										if (element.type == 'object'){
											obj[ele.name] = ele.default;
										}
									});
									varObj.default.push(obj)
								}
								
								
							});
						   }
					   }
					}else{
						varObj.default = []
					}
				   resultObj.push(varObj)
			   }else if(secondArray[0]=='object'){
				   if(firstArray.length > 1 && strMap.has(firstArray[1].trim())){
					   let childStr =  strMap.get(firstArray[1].trim()) 
					   if(childStr.length > 0){
						   // childStr = formatObj(childStr,strMap,varObj.value)
						   // let childObj = JSON.parse(childStr)
						   // varObj.children = disassemble(varObj.value+".",childObj)
						   varObj.children = formatObj(childStr,strMap,varObj.value)
						   varObj.default = {}
					   }
				   }else{
					   varObj.default = {}
				   }
				   resultObj.push(varObj)
			   }
		   }
	   }
       console.log(resultObj)
	   return resultObj
   }
   
   function formatArrayObj(sourceStr,strMap,prefix){
	   let resultObj = []
	   let tmpArrayStr = doBlack(sourceStr,"[","]",strMap,"@%","%@","",1)
	   let tmpArray = tmpArrayStr.trim().replaceAll('\n',"").split(",")
	   if(tmpArray && tmpArray.length > 0){
		   for(let i=0;i<tmpArray.length;i++){
			   let tItem = tmpArray[i].trim()
			   let preStr = tItem.substring(0,2)
			   let sufStr = tItem.slice(-2)
			   let varObj = {
				   name:'['+i+']',
				   type:'any',
				   value:prefix+"."+i,
			   }
			   if(preStr=='@%' && sufStr=='%@'){
				   varObj.type = 'array'
				   let childStr =  strMap.get(tItem)
				   if(childStr.length > 0){
					   varObj.children = formatArrayObj(childStr,strMap,varObj.value)
					   varObj.default = {}
				   }
			   }else if(preStr=='@#' && sufStr=='#@'){
				   varObj.type = 'object'
				   let childStr =  strMap.get(tItem)
				   if(childStr.length > 0){
					   varObj.children = formatObj(childStr,strMap,varObj.value)
					   varObj.default = {}
				   }
			   }else if(preStr=='@@' && sufStr=='@@'){
				   varObj.type = 'string'
				   varObj.default = strMap.get(tItem)
			   }else if(isNumeric(tItem)){
				   varObj.type = 'number'
				   varObj.default = parseInt(tItem)
			   }else if(tItem=='true' || tItem=='false'){
				   varObj.type = 'boolean'
				   varObj.default=tItem
			   }
			   resultObj.push(varObj)
		   }
	   }
	   return resultObj
   }
   
   function formatObj(sourceStr,strMap,prefix){
	   let childStr = "\{" + rebackStr(sourceStr,strMap) +"\}"
	   let mArr = childStr.replaceAll("{",",").replaceAll("}",",").replaceAll("\n","").split(",")
	   for(let n1=0;n1<mArr.length;n1++){
		   mArr[n1] = mArr[n1].trim()
		   if(mArr[n1].length > 0 && mArr[n1].indexOf(":") >= 0){
			   let pArr = mArr[n1].split(":")
			   if(pArr[0].indexOf('"') >= 0){
				   ElMessage({
					   message:"变量名[" + pArr[0]+"]错误， 不能带引号！",
					   type:"error"
				   })
				   return null
			   }
			   childStr = childStr.replaceAll(pArr[0]+":",'"'+pArr[0].trim()+'":')
		   }
	   }
	   let childObj = JSON.parse(childStr)
	   return disassemble(prefix+".",childObj)
   }
   
   function rebackStr(source,strMap){
	   let tmpStr = source.trim()
	   let tmpRet = tmpStr
	   for(let i=0;i<1000;i++){
		   if(tmpStr.indexOf("@@") >= 0 && tmpStr.substring(tmpStr.indexOf("@@") + 2).indexOf("@@") >= 0){
			   let tmpArray = tmpStr.substring(tmpStr.indexOf("@@") + 2).split("@@")
			   if(tmpArray.length > 1 && tmpArray[0].indexOf("#") >= 0){
				   let key = "@@" + tmpArray[0] + "@@"
				   let value = ""
				   if(strMap.has(key)){
					   value = '"' + strMap.get(key) +'"'
				   }
				   tmpRet = tmpRet.replaceAll(key,value)
				   tmpStr = tmpRet
			   }
		   }else{
			   break;
		   }
	   }
	   return tmpRet
   }
   
   function disassemble(path,nObj){
	   let resArray = []
	   for(let key of Object.keys(nObj)){
		   let typeStr = typeof nObj[key]
		   let varObj = {
			   name:key,
			   type:typeStr,
			   value:path + key,
			   default:nObj[key]
		   }
		   if(typeStr=='object' && nObj[key]){
			   varObj.children = disassemble(varObj.value+".",nObj[key])
		   }
		   resArray.push(varObj)
	   }
	   return resArray
   }
   
   function doBlack(sc,b,e,nMap,b1,e1,end,floor){  //记录块，替换块
		let tmpScript = sc
		let tmpStrScript = tmpScript
		for(let t=0;t<100;t++){
			let tmpStr = getBlock(tmpStrScript,b,e)
			if(tmpStr.length > 0){
				let key = b1 + floor + '#' + t + e1
				nMap.set(key ,tmpStr)
				// tmpScript = tmpScript.replaceAll(b+tmpStr+e,' '+key+' '+end)
				let endStr = tmpStr.indexOf(";") >= 0 ? end : " ";
				tmpScript = tmpScript.replaceAll(b+tmpStr+e,' '+key+' '+endStr)
				let start = tmpStrScript.indexOf(b+tmpStr+e)
				let nextStart =start + (b+tmpStr+e).length
				tmpStrScript = tmpStrScript.substring(nextStart)
				continue
			}else{
				break
			}
		}
		
		return tmpScript
    }
	function getBlock(sc,b,e){ //查找块函数
		let tmpStr = sc
		if(b==e && b != ''){
			let startPos = tmpStr.indexOf(b)
			let nOffset = startPos+b.length
			let endPos = nOffset + tmpStr.substring(nOffset).indexOf(b)
			if(startPos >= 0 && startPos < endPos){
				return tmpStr.substring(nOffset,endPos)
			}
		}else if(b != e && e != '' && b != ''){
			if(tmpStr.indexOf(b) >= 0){
				 tmpStr = tmpStr.substring(tmpStr.indexOf(b))
				 let strArr = tmpStr.split(b)
				 let floor = 0
				 let result = ""
				 for(let n=0;n<strArr.length;n++){
					 if(strArr[n] == '') continue
					 result += b
					 floor++
					 let endArr = strArr[n].split(e)
					 if(endArr.length >= 2){
						 for(let m=0;m<endArr.length;m++){
							 if(endArr[m]=='') continue
							 result += endArr[m]
							 if (m < endArr.length - 1) {
								result +=  e
							 	floor--
							 }
							 
							 if(floor <= 0){
								 return result.substring(b.length,result.length - e.length)
							 }
						 }
					 }else{
						result += strArr[n]
					 }
				 }
			}
		}
		return ""
	}
	
	
	function releaseFile(fileScript){
		return {
			fileId:fileScript.fileId,
			filePath:fileScript.filePath,
			params:JSON.parse(JSON.stringify(fileScript.params)),
			instance:{
				varText:fileScript.instance.varText
			},
			attribute:JSON.parse(JSON.stringify(fileScript.attribute)),
			triggers:JSON.parse(JSON.stringify(fileScript.triggers)),
			componentList:remParent(fileScript.componentList)
		}
	}
	
	function remParent(src){
		let result = []
		if(src.length > 0){
			for(let n=0;n<src.length;n++){
				let item = {}
				for(let key of Object.keys(src[n])){
					if(key != 'parent' && key != 'children'){
						item[key] = toRaw(src[n][key])
					}else if(key == 'children'){
						item.children = remParent(src[n].children)
					}
				}
				result.push(item)
			}
		}
		return result
	}
	
	
	function packFile(fileJson){
		
		return ""
	}
	
	function addParent(cptList,parent){
		if(cptList.length > 0){
			for(let n=0;n<cptList.length;n++){
				cptList[n].parent = parent
				if(cptList.hasOwnProperty("children") && cptList.children.length > 0){
					addParent(cptList[n].children,cptList[n])
				}
			}
		}
	}
	
	function reParams(params){
		let res = []
		if(params && params.length > 0){
			for(let n=0;n<params.length;n++){
				res.push({
					name:toRaw(params[n].name),
					type:toRaw(params[n].type),
					value:"ref:ParamData."+toRaw(params[n].name)
				})
			}
		}
		return res
	}
	
	function isNumeric(value){
		return /^[+-]?(?:\d+(\.\d*)?|\.\d+)([eE][+-]?\d+)?$/.test(value);
	}

   return {parse,releaseFile,packFile,reParams}
}