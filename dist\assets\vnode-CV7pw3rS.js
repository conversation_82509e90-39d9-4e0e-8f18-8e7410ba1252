import{aB as T,b4 as ae,B,bg as H,e as O,aV as j,ai as oe,_ as ie,K as ce,Q as fe,ag as U,v as y,X as de,a2 as X,af as le,Z as Ee,ad as x,bx as m,aD as pe,cv as ve}from"./index-Dgb0NZ1J.js";const Te=(e,o)=>{if(!T)return!1;const t={undefined:"overflow",true:"overflow-y",false:"overflow-x"}[String(o)],n=ae(e,t);return["scroll","auto","overlay"].some(r=>n.includes(r))},xe=(e,o)=>{if(!T)return;let t=e;for(;t;){if([window,document,document.documentElement].includes(t))return window;if(Te(t,o))return t;t=t.parentNode}return t};let w;const Ke=e=>{var o;if(!T)return 0;if(w!==void 0)return w;const t=document.createElement("div");t.className=`${e}-scrollbar__wrap`,t.style.visibility="hidden",t.style.width="100px",t.style.position="absolute",t.style.top="-9999px",document.body.appendChild(t);const n=t.offsetWidth;t.style.overflow="scroll";const r=document.createElement("div");r.style.width="100%",t.appendChild(r);const a=r.offsetWidth;return(o=t.parentNode)==null||o.removeChild(t),w=n-a,w};function ke(e,o){if(!T)return;if(!o){e.scrollTop=0;return}const t=[];let n=o.offsetParent;for(;n!==null&&e!==n&&e.contains(n);)t.push(n),n=n.offsetParent;const r=o.offsetTop+t.reduce((b,S)=>b+S.offsetTop,0),a=r+o.offsetHeight,d=e.scrollTop,p=d+e.clientHeight;r<d?e.scrollTop=r:a>p&&(e.scrollTop=a-e.clientHeight)}const me='a[href],button:not([disabled]),button:not([hidden]),:not([tabindex="-1"]),input:not([disabled]),input:not([type="hidden"]),select:not([disabled]),textarea:not([disabled])',be=e=>getComputedStyle(e).position==="fixed"?!1:e.offsetParent!==null,Be=e=>Array.from(e.querySelectorAll(me)).filter(o=>se(o)&&be(o)),se=e=>{if(e.tabIndex>0||e.tabIndex===0&&e.getAttribute("tabIndex")!==null)return!0;if(e.tabIndex<0||e.hasAttribute("disabled")||e.getAttribute("aria-disabled")==="true")return!1;switch(e.nodeName){case"A":return!!e.href&&e.rel!=="ignore";case"INPUT":return!(e.type==="hidden"||e.type==="file");case"BUTTON":case"SELECT":case"TEXTAREA":return!0;default:return!1}},K="focus-trap.focus-after-trapped",k="focus-trap.focus-after-released",Se="focus-trap.focusout-prevented",q={cancelable:!0,bubbles:!1},_e={cancelable:!0,bubbles:!1},z="focusAfterTrapped",J="focusAfterReleased",ye=Symbol("elFocusTrap"),g=O(),R=O(0),M=O(0);let A=0;const re=e=>{const o=[],t=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:n=>{const r=n.tagName==="INPUT"&&n.type==="hidden";return n.disabled||n.hidden||r?NodeFilter.FILTER_SKIP:n.tabIndex>=0||n===document.activeElement?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;t.nextNode();)o.push(t.currentNode);return o},Q=(e,o)=>{for(const t of e)if(!we(t,o))return t},we=(e,o)=>{if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(o&&e===o)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1},Ae=e=>{const o=re(e),t=Q(o,e),n=Q(o.reverse(),e);return[t,n]},Le=e=>e instanceof HTMLInputElement&&"select"in e,E=(e,o)=>{if(e&&e.focus){const t=document.activeElement;let n=!1;j(e)&&!se(e)&&!e.getAttribute("tabindex")&&(e.setAttribute("tabindex","-1"),n=!0),e.focus({preventScroll:!0}),M.value=window.performance.now(),e!==t&&Le(e)&&o&&e.select(),j(e)&&n&&e.removeAttribute("tabindex")}};function Z(e,o){const t=[...e],n=e.indexOf(o);return n!==-1&&t.splice(n,1),t}const Ne=()=>{let e=[];return{push:n=>{const r=e[0];r&&n!==r&&r.pause(),e=Z(e,n),e.unshift(n)},remove:n=>{var r,a;e=Z(e,n),(a=(r=e[0])==null?void 0:r.resume)==null||a.call(r)}}},Ce=(e,o=!1)=>{const t=document.activeElement;for(const n of e)if(E(n,o),document.activeElement!==t)return},ee=Ne(),Oe=()=>R.value>M.value,L=()=>{g.value="pointer",R.value=window.performance.now()},te=()=>{g.value="keyboard",R.value=window.performance.now()},Re=()=>(B(()=>{A===0&&(document.addEventListener("mousedown",L),document.addEventListener("touchstart",L),document.addEventListener("keydown",te)),A++}),H(()=>{A--,A<=0&&(document.removeEventListener("mousedown",L),document.removeEventListener("touchstart",L),document.removeEventListener("keydown",te))}),{focusReason:g,lastUserFocusTimestamp:R,lastAutomatedFocusTimestamp:M}),N=e=>new CustomEvent(Se,{..._e,detail:e});let v=[];const ne=e=>{e.code===oe.esc&&v.forEach(o=>o(e))},Fe=e=>{B(()=>{v.length===0&&document.addEventListener("keydown",ne),T&&v.push(e)}),H(()=>{v=v.filter(o=>o!==e),v.length===0&&T&&document.removeEventListener("keydown",ne)})},Ie=ce({name:"ElFocusTrap",inheritAttrs:!1,props:{loop:Boolean,trapped:Boolean,focusTrapEl:Object,focusStartEl:{type:[Object,String],default:"first"}},emits:[z,J,"focusin","focusout","focusout-prevented","release-requested"],setup(e,{emit:o}){const t=O();let n,r;const{focusReason:a}=Re();Fe(s=>{e.trapped&&!d.paused&&o("release-requested",s)});const d={paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}},p=s=>{if(!e.loop&&!e.trapped||d.paused)return;const{code:u,altKey:i,ctrlKey:c,metaKey:f,currentTarget:W,shiftKey:$}=s,{loop:G}=e,ue=u===oe.tab&&!i&&!c&&!f,_=document.activeElement;if(ue&&_){const h=W,[D,P]=Ae(h);if(D&&P){if(!$&&_===P){const l=N({focusReason:a.value});o("focusout-prevented",l),l.defaultPrevented||(s.preventDefault(),G&&E(D,!0))}else if($&&[D,h].includes(_)){const l=N({focusReason:a.value});o("focusout-prevented",l),l.defaultPrevented||(s.preventDefault(),G&&E(P,!0))}}else if(_===h){const l=N({focusReason:a.value});o("focusout-prevented",l),l.defaultPrevented||s.preventDefault()}}};Ee(ye,{focusTrapRef:t,onKeydown:p}),U(()=>e.focusTrapEl,s=>{s&&(t.value=s)},{immediate:!0}),U([t],([s],[u])=>{s&&(s.addEventListener("keydown",p),s.addEventListener("focusin",F),s.addEventListener("focusout",I)),u&&(u.removeEventListener("keydown",p),u.removeEventListener("focusin",F),u.removeEventListener("focusout",I))});const b=s=>{o(z,s)},S=s=>o(J,s),F=s=>{const u=y(t);if(!u)return;const i=s.target,c=s.relatedTarget,f=i&&u.contains(i);e.trapped||c&&u.contains(c)||(n=c),f&&o("focusin",s),!d.paused&&e.trapped&&(f?r=i:E(r,!0))},I=s=>{const u=y(t);if(!(d.paused||!u))if(e.trapped){const i=s.relatedTarget;!de(i)&&!u.contains(i)&&setTimeout(()=>{if(!d.paused&&e.trapped){const c=N({focusReason:a.value});o("focusout-prevented",c),c.defaultPrevented||E(r,!0)}},0)}else{const i=s.target;i&&u.contains(i)||o("focusout",s)}};async function V(){await X();const s=y(t);if(s){ee.push(d);const u=s.contains(document.activeElement)?n:document.activeElement;if(n=u,!s.contains(u)){const c=new Event(K,q);s.addEventListener(K,b),s.dispatchEvent(c),c.defaultPrevented||X(()=>{let f=e.focusStartEl;le(f)||(E(f),document.activeElement!==f&&(f="first")),f==="first"&&Ce(re(s),!0),(document.activeElement===u||f==="container")&&E(s)})}}}function Y(){const s=y(t);if(s){s.removeEventListener(K,b);const u=new CustomEvent(k,{...q,detail:{focusReason:a.value}});s.addEventListener(k,S),s.dispatchEvent(u),!u.defaultPrevented&&(a.value=="keyboard"||!Oe()||s.contains(document.activeElement))&&E(n??document.body),s.removeEventListener(k,S),ee.remove(d)}}return B(()=>{e.trapped&&V(),U(()=>e.trapped,s=>{s?V():Y()})}),H(()=>{e.trapped&&Y(),t.value&&(t.value.removeEventListener("keydown",p),t.value.removeEventListener("focusin",F),t.value.removeEventListener("focusout",I),t.value=void 0)}),{onKeydown:p}}});function he(e,o,t,n,r,a){return fe(e.$slots,"default",{handleKeydown:e.onKeydown})}var He=ie(Ie,[["render",he],["__file","focus-trap.vue"]]),De=(e=>(e[e.TEXT=1]="TEXT",e[e.CLASS=2]="CLASS",e[e.STYLE=4]="STYLE",e[e.PROPS=8]="PROPS",e[e.FULL_PROPS=16]="FULL_PROPS",e[e.HYDRATE_EVENTS=32]="HYDRATE_EVENTS",e[e.STABLE_FRAGMENT=64]="STABLE_FRAGMENT",e[e.KEYED_FRAGMENT=128]="KEYED_FRAGMENT",e[e.UNKEYED_FRAGMENT=256]="UNKEYED_FRAGMENT",e[e.NEED_PATCH=512]="NEED_PATCH",e[e.DYNAMIC_SLOTS=1024]="DYNAMIC_SLOTS",e[e.HOISTED=-1]="HOISTED",e[e.BAIL=-2]="BAIL",e))(De||{});const ge=e=>{if(!m(e))return{};const o=e.props||{},t=(m(e.type)?e.type.props:void 0)||{},n={};return Object.keys(t).forEach(r=>{pe(t[r],"default")&&(n[r]=t[r].default)}),Object.keys(o).forEach(r=>{n[ve(r)]=o[r]}),n},C=e=>{const o=x(e)?e:[e],t=[];return o.forEach(n=>{var r;x(n)?t.push(...C(n)):m(n)&&((r=n.component)!=null&&r.subTree)?t.push(n,...C(n.component.subTree)):m(n)&&x(n.children)?t.push(...C(n.children)):m(n)&&n.shapeFlag===2?t.push(...C(n.type())):t.push(n)}),t};export{He as E,ye as F,De as P,xe as a,Ke as b,C as f,ge as g,se as i,Be as o,ke as s,E as t};
