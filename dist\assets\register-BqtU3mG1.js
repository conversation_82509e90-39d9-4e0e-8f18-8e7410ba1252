import{E as S}from"./el-button-B8TCQS4u.js";import{E as U,a as I}from"./el-form-item-Beaw8WQV.js";import{E as N}from"./el-checkbox-DnOi5Qa-.js";/* empty css                 */import{d as w,e as i,c as v,a,b as t,w as k,k as r,f as s,g as R,F as z,h as T,i as $,o as g,t as j,j as L,l as M,E,q as P}from"./index-Dgb0NZ1J.js";import{E as A}from"./index-C-aKrRv3.js";import"./castArray-BpiBxx45.js";import"./index-CINbulG0.js";import"./isEqual-CwZW0B1R.js";const G={class:"reset_desc"},H={class:"code_container"},J={key:0},K={class:"read_forget"},O={class:"read_text"},ae={__name:"register",setup(Q){const _=T(),x=w({mobile:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请检查手机号码是否有误",trigger:"change"}],password:[{required:!0,message:"请输入密码",trigger:"change"}],vcode:[{required:!0,message:"请输入验证码",trigger:"change"}],name:[{required:!0,message:"请输入昵称",trigger:"change"}],agreement:[{validator:(c,e,n)=>{e?n():n(new Error("请阅读并接受用户协议和隐私政策"))},trigger:"change"}]}),b=i(),f=i(!1),u=i(60),y=i(null),m=i(!1),o=w({mobile:"",password:"",vcode:"",name:"",agreement:!1,workcity:4403}),h=()=>{_.push("/login/index")},B=()=>{f.value=!0,u.value=60,y.value=setInterval(()=>{u.value===0?(f.value=!1,clearInterval(y.value)):u.value--},1e3)},C=async()=>{if(/^1[3-9]\d{9}$/.test(o.mobile))try{await M(o.mobile),E({message:"发送成功！",type:"success"}),B()}catch{}},F=()=>{b.value.validate(async c=>{if(c)try{m.value=!0,await P(o),E({message:"注册成功！",type:"success"}),m.value=!1,_.push("/login/index")}catch{m.value=!1}})};return(c,e)=>{const n=S,p=A,d=I,q=N,D=U,V=$("debounce");return g(),v(z,null,[e[13]||(e[13]=a("div",{class:"login_title"},"欢迎注册",-1)),a("div",G,[e[6]||(e[6]=r(" 欢迎注册平台会员，如果您已拥有账户，则可以在此 ")),t(n,{type:"primary",link:"",style:{"font-size":"14px",color:"#2b6bff",padding:"0",border:"null"},onClick:h},{default:s(()=>e[5]||(e[5]=[r(" 登录 ")])),_:1,__:[5]})]),t(D,{ref_key:"formRef",ref:b,model:o,rules:x,"status-icon":""},{default:s(()=>[t(d,{prop:"mobile"},{default:s(()=>[t(p,{modelValue:o.mobile,"onUpdate:modelValue":e[0]||(e[0]=l=>o.mobile=l),placeholder:"请输入手机号码",class:"input",maxlength:11},null,8,["modelValue"])]),_:1}),t(d,{prop:"vcode",class:"form_item"},{default:s(()=>[t(p,{modelValue:o.vcode,"onUpdate:modelValue":e[1]||(e[1]=l=>o.vcode=l),placeholder:"请输入验证码",class:"input",maxlength:6},null,8,["modelValue"]),a("div",H,[f.value?(g(),v("div",J,j(u.value)+"s",1)):k((g(),v("div",{key:1,style:L({color:o.mobile.length===11?"#2B6BFF":"#999999"})},e[7]||(e[7]=[r(" 发送验证码 ")]),4)),[[V,C]])])]),_:1}),t(d,{prop:"name"},{default:s(()=>[t(p,{modelValue:o.name,"onUpdate:modelValue":e[2]||(e[2]=l=>o.name=l),placeholder:"请输入昵称",class:"input"},null,8,["modelValue"])]),_:1}),t(d,{prop:"password"},{default:s(()=>[t(p,{modelValue:o.password,"onUpdate:modelValue":e[3]||(e[3]=l=>o.password=l),placeholder:"请输入密码",class:"input",type:"password","show-password":""},null,8,["modelValue"])]),_:1}),t(d,{prop:"agreement",style:{margin:"0"}},{default:s(()=>[a("div",K,[a("div",O,[t(q,{modelValue:o.agreement,"onUpdate:modelValue":e[4]||(e[4]=l=>o.agreement=l)},null,8,["modelValue"]),e[8]||(e[8]=r(" 阅读并接受 ")),e[9]||(e[9]=a("a",{href:"https://baidu.com",target:"_blank",rel:"noopener noreferrer"}," 用户协议 ",-1)),e[10]||(e[10]=r(" 和 ")),e[11]||(e[11]=a("a",{href:"https://baidu.com",target:"_blank",rel:"noopener noreferrer"}," 隐私政策 ",-1))])])]),_:1})]),_:1},8,["model","rules"]),k((g(),R(n,{type:"primary",class:"submit_btn",loading:m.value},{default:s(()=>e[12]||(e[12]=[r(" 注册 ")])),_:1,__:[12]},8,["loading"])),[[V,F]])],64)}}};export{ae as default};
